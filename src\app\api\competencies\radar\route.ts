import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { MaterialStatus } from '@prisma/client';
import { z } from 'zod';

// Response schemas
const CompetencyMetricsSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullable(),
  uniqueUsersEngaged: z.number(),
  totalSystemUsersForMetric: z.number(),
  metrics: z.object({
    trainedPercentage: z.number().min(0).max(100),
    inSystemPercentage: z.number().min(0).max(100),
    notEngagedPercentage: z.number().min(0).max(100)
  })
});

const StatsSchema = z.object({
  totalCompetencies: z.number(),
  mostEngagedCompetency: z.string(),
  highestEngagementRate: z.string(),
  lowestEngagementRate: z.string()
});

const ResponseSchema = z.object({
  competencies: z.array(CompetencyMetricsSchema),
  stats: StatsSchema
});

// Interface to match Zod schema for internal processing clarity
interface ProcessedCompetencyMetrics {
  id: number;
  name: string;
  description: string | null;
  uniqueUsersEngaged: number;
  totalSystemUsersForMetric: number;
  metrics: {
    trainedPercentage: number;
    inSystemPercentage: number;
    notEngagedPercentage: number;
  };
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const totalSystemUsers = await prisma.user.count();
    if (totalSystemUsers === 0) {
        // Handle case with no users to avoid division by zero and return empty/meaningful response
        return NextResponse.json(ResponseSchema.parse({
            competencies: [],
            stats: {
                totalCompetencies: 0,
                mostEngagedCompetency: 'N/A',
                highestEngagementRate: 'N/A',
                lowestEngagementRate: 'N/A'
            }
        }));
    }

    const competenciesFromDb = await prisma.competency.findMany({
      include: {
        materialCompetencies: {
          include: {
            training_material: {
              include: {
                trainingParticipants: {
                  select: { userId: true }
                },
                userTrainingProgress: {
                  select: {
                    userId: true,
                    status: true
                  }
                }
              }
            }
          }
        }
      }
    });

    const processedMetrics: ProcessedCompetencyMetrics[] = competenciesFromDb.map(competency => {
      const engagedUserIds = new Set<number>();
      const trainedUserIds = new Set<number>();

      competency.materialCompetencies.forEach(mc => {
        if (mc.training_material && mc.training_material.status === MaterialStatus.PUBLISHED) {
          // Track engaged users (participating in any way)
          mc.training_material.trainingParticipants.forEach(participant => {
            engagedUserIds.add(participant.userId);
          });

          // Track trained users (completed the training)
          mc.training_material.userTrainingProgress.forEach(progress => {
            if (progress.status === 'COMPLETED') {
              trainedUserIds.add(progress.userId);
            }
          });
        }
      });

      const uniqueUsersEngaged = engagedUserIds.size;
      const uniqueUsersTrained = trainedUserIds.size;
      
      const trainedPercentage = totalSystemUsers > 0 ? Math.round((uniqueUsersTrained / totalSystemUsers) * 100) : 0;
      const inSystemPercentage = totalSystemUsers > 0 ? Math.round((uniqueUsersEngaged / totalSystemUsers) * 100) : 0;
      const notEngagedPercentage = 100 - inSystemPercentage;

      return {
        id: competency.id,
        name: competency.competencyName,
        description: competency.description,
        uniqueUsersEngaged: uniqueUsersEngaged,
        totalSystemUsersForMetric: totalSystemUsers,
        metrics: {
          trainedPercentage,
          inSystemPercentage,
          notEngagedPercentage
        }
      };
    });

    const sortedMetrics = processedMetrics.sort((a, b) => b.uniqueUsersEngaged - a.uniqueUsersEngaged);

    const overallStats = {
      totalCompetencies: sortedMetrics.length,
      mostEngagedCompetency: sortedMetrics.length > 0 ? sortedMetrics[0].name : 'N/A',
      highestEngagementRate: sortedMetrics.reduce((highest, current) => 
        (!highest || current.metrics.trainedPercentage > highest.metrics.trainedPercentage) ? current : highest
      , null as ProcessedCompetencyMetrics | null)?.name || 'N/A',
      lowestEngagementRate: sortedMetrics.reduce((lowest, current) => 
        (sortedMetrics.length > 0 && (!lowest || current.metrics.trainedPercentage < lowest.metrics.trainedPercentage)) ? current : lowest
      , null as ProcessedCompetencyMetrics | null)?.name || 'N/A'
    };

    const response = {
      competencies: sortedMetrics,
      stats: overallStats
    };

    // Validate response data using the Zod schema
    const validationResult = ResponseSchema.safeParse(response);

    if (!validationResult.success) {
      console.error('API Response validation failed:', validationResult.error.flatten());
      // Return a generic error to the client, log the specifics on the server
      return NextResponse.json(
        { error: 'Internal server error: Invalid data structure generated.' }, 
        { status: 500 }
      );
    }

    return NextResponse.json(validationResult.data);

  } catch (error) {
    console.error('Error fetching competency metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch competency metrics' },
      { status: 500 }
    );
  }
} 