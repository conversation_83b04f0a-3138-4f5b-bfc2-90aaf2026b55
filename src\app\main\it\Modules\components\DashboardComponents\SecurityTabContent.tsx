import React, { memo, useMemo } from 'react';
import { StatCard } from '@/components/shared/StatCard';
import { ShieldCheck, LogIn, RefreshCw, AlertTriangle } from 'lucide-react';

const SecurityTabContent: React.FC = () => {
  const cards = useMemo(() => [
    {
      title: 'Security Status',
      value: 'Protected',
      icon: ShieldCheck,
      color: 'text-green-600',
      subtitle: 'Protected ✓',
    },
    {
      title: 'Failed Logins',
      value: 12,
      icon: LogIn,
      color: 'text-blue-500',
      subtitle: 'Last 24 hours',
    },
    {
      title: 'Last Backup',
      value: '6h ago',
      icon: RefreshCw,
      color: 'text-green-600',
      subtitle: '✓ Successful',
    },
    {
      title: 'Vulnerabilities',
      value: 2,
      icon: AlertTriangle,
      color: 'text-yellow-500',
      subtitle: 'Needs attention',
    },
  ], []);

  const auditLog = useMemo(() => [
    { type: 'warning', message: 'Failed login attempt from ************', time: '15m ago' },
    { type: 'success', message: 'Password policy updated by admin', time: '2h ago' },
    { type: 'success', message: 'Security scan completed', time: '6h ago' },
    { type: 'critical', message: 'Unusual file access pattern detected', time: '1d ago' },
    { type: 'success', message: 'Firewall rules updated', time: '2d ago' },
    { type: 'warning', message: 'Multiple failed logins detected', time: '3d ago' },
    { type: 'critical', message: 'Malware signature found', time: '4d ago' },
    { type: 'success', message: 'Backup completed', time: '5d ago' },
    { type: 'info', message: 'User permissions updated', time: '6d ago' },
  ], []);

  const typeColor = useMemo(() => ({
    critical: 'bg-red-500',
    warning: 'bg-yellow-400',
    success: 'bg-green-500',
    info: 'bg-blue-500',
  }), []);

  return (
    <div className="flex flex-col h-full min-h-0">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-3 h-fit">
        {cards.map((card, idx) => (
          <div key={idx} className="flex flex-col h-full min-h-0 bg-white rounded-lg shadow-sm border border-gray-100 p-4 overflow-auto">
            <StatCard {...card} className="w-full h-full flex-1 min-h-0 truncate" />
          </div>
        ))}
      </div>
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 flex-1 flex flex-col min-h-0 overflow-hidden">
        <h2 className="text-lg font-semibold text-gray-800 mb-2">Security Audit Log</h2>
        <p className="text-gray-500 text-xs mb-4">Recent security events</p>
        <ul className="space-y-2 flex-1 min-h-0 overflow-auto max-h-[60vh]">
          {auditLog.map((event, idx) => (
            <li key={idx} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
              <div className="flex items-center gap-2 min-w-0">
                <span className={`inline-block w-2 h-2 rounded-full ${typeColor[event.type]}`}></span>
                <span className="text-sm text-gray-700 truncate" title={event.message}>{event.message}</span>
              </div>
              <span className="text-xs text-gray-400 whitespace-nowrap">{event.time}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default memo(SecurityTabContent); 