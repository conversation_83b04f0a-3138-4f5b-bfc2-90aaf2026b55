interface ExportData {
  title: string;
  headers: string[];
  data: any[][];
  timeframe?: string;
}

export const convertToCSV = ({ title, headers, data, timeframe }: ExportData): string => {
  // Excel styling for headers (light blue background)
  const styleTag = `sep=,\n`;  // Excel separator specification
  
  // Create title section with merged cells effect
  const titleSection = [
    `"${title}",${"," .repeat(headers.length - 1)}`,
    timeframe ? `"Report Period: ${timeframe}",${"," .repeat(headers.length - 1)}` : "",
    "" // Empty line after title
  ].join('\n');

  // Style headers with quotes to handle commas
  const headerString = headers.map(header => `"${header}"`).join(',');

  // Process data rows with proper escaping
  const rowsString = data.map(row =>
    row.map(value => {
      if (value === null || value === undefined) return '';
      const stringValue = String(value);
      // Escape commas, quotes, and newlines
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',')
  ).join('\n');

  return `${styleTag}${titleSection}\n${headerString}\n${rowsString}`;
};

export const downloadCSV = (csvString: string, filename: string): void => {
  // Add BOM for Excel to properly detect UTF-8
  const BOM = '\uFEFF';
  const blob = new Blob([BOM + csvString], { type: 'text/csv;charset=utf-8;' });
  
  if ((window.navigator as any).msSaveOrOpenBlob) {
    // Handle IE11
    (window.navigator as any).msSaveOrOpenBlob(blob, filename);
  } else {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}; 