'use client';

import React from 'react';
import { Calendar, ChevronDown } from 'lucide-react';
import { TimeFilterProps } from '../utils/types';
import { timeFilterOptions } from '../data/config';

const TimeFilter: React.FC<TimeFilterProps> = ({ 
  timeFilter, 
  isFilterOpen, 
  filterRef, 
  setIsFilterOpen, 
  handleFilterChange 
}) => (
  <div className="relative" ref={filterRef as React.RefObject<HTMLDivElement>}>
    <button 
      onClick={() => setIsFilterOpen(!isFilterOpen)}
      className='flex gap-2 items-center text-sm rounded-md bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-3 py-2 cursor-pointer'
    >
      <Calendar size={16} />
      <span>{timeFilter}</span>
      <ChevronDown size={14} />
    </button>
    {isFilterOpen && (
      <div className='absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10'>
        <ul className='py-1'>
          {timeFilterOptions.map((filter) => (
            <li key={filter}>
              <button
                onClick={() => handleFilterChange(filter)}
                className={`block w-full text-left px-4 py-2 text-sm cursor-pointer ${timeFilter === filter ? 'bg-gray-100 text-color3' : 'hover:bg-gray-50'}`}
              >
                {filter}
              </button>
            </li>
          ))}
        </ul>
      </div>
    )}
  </div>
);

export default TimeFilter; 