import React, { useState, useEffect } from 'react';
import { Bar, Doughnut } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface ComplianceMetricsProps {
  className?: string;
  timeframe?: string;
}

interface RiskLevel {
  high: number;
  medium: number;
  low: number;
}

interface DepartmentTrainingStatus {
  department: string;
  required: number;
  completed: number;
  pending: number;
  overdue: number;
}

interface ComplianceData {
  overallCompliance: number;
  riskLevel: RiskLevel;
  trainingStatus: DepartmentTrainingStatus[];
  complianceTrends: {
    labels: string[];
    data: number[];
  };
}

const ComplianceMetrics: React.FC<ComplianceMetricsProps> = ({ 
  className,
  timeframe = 'This Week'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ComplianceData | null>(null);

  useEffect(() => {
    const fetchComplianceData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/hr-analytics/compliance');
        if (!response.ok) throw new Error('Failed to fetch compliance data');
        const complianceData = await response.json();
        setData(complianceData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching compliance data');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchComplianceData();
  }, [timeframe]);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className} flex justify-center items-center min-h-[400px]`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className} flex flex-col justify-center items-center min-h-[400px]`}>
        <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
        <p className="text-red-500 text-center">
          {error || 'No compliance data available'}
        </p>
      </div>
    );
  }

  const riskLevelData: ChartData<'doughnut'> = {
    labels: ['High Risk', 'Medium Risk', 'Low Risk'],
    datasets: [{
      data: [data.riskLevel.high, data.riskLevel.medium, data.riskLevel.low],
      backgroundColor: ['#EF4444', '#F59E0B', '#10B981'],
      borderWidth: 0
    }]
  };

  const complianceTrendData: ChartData<'bar'> = {
    labels: data.complianceTrends.labels,
    datasets: [{
      label: 'Compliance Rate',
      data: data.complianceTrends.data,
      backgroundColor: '#3B82F6',
      borderColor: '#2563EB',
      borderWidth: 1
    }]
  };

  const doughnutOptions: ChartOptions<'doughnut'> = {
    cutout: '70%',
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      }
    }
  };

  const barOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: value => `${value}%`
        }
      }
    },
    plugins: {
      legend: {
        display: false
      }
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-800">Compliance Overview</h2>
          <p className="text-sm text-gray-500">Training compliance metrics</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-800">{data.overallCompliance}%</div>
          <div className="text-sm text-gray-500">Overall Compliance</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Risk Level Distribution</h3>
          <div className="h-[300px] relative">
            <Doughnut data={riskLevelData} options={doughnutOptions} />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Compliance Trends</h3>
          <div className="h-[300px]">
            <Bar data={complianceTrendData} options={barOptions} />
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-sm font-medium text-gray-700 mb-4">Department Training Status</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Overdue</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.trainingStatus.map((status, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{status.department}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{status.required}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 text-center">{status.completed}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-amber-600 text-center">{status.pending}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 text-center">{status.overdue}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ComplianceMetrics; 