"use client"

import { X, FileText, FileImage, FileArchive, FileVideo } from "lucide-react"

interface FileListProps {
  files: File[];
  onRemove: (index: number) => void;
}

const FileList: React.FC<FileListProps> = ({ files, onRemove }) => {
  const getFileIcon = (file: File) => {
    const fileType = file.type

    if (fileType.includes("image")) {
      return <FileImage className="w-5 h-5 text-blue-500" />
    } else if (fileType.includes("video")) {
      return <FileVideo className="w-5 h-5 text-orange-400" />
    } else if (fileType.includes("zip") || fileType.includes("archive")) {
      return <FileArchive className="w-5 h-5 text-yellow-500" />
    } else {
      return <FileText className="w-5 h-5 text-red-600" />
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes"

    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"]

    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  return (
    <ul className="space-y-2">
      {files.map((file, index) => (
        <li key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md gap-2">
          <div className="flex items-center flex-1 min-w-0">
            {getFileIcon(file)}
            <div className="ml-2 flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{file.name}</p>
              <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
            </div>
          </div>
          <button
            type="button"
            onClick={() => onRemove(index)}
            className="text-gray-500 cursor-pointer hover:text-red-500 flex-shrink-0"
          >
            <X className="w-4 h-4" />
          </button>
        </li>
      ))}
    </ul>
  )
}

export default FileList 