import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { formatDistanceToNow } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) { 
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 });
    }
    
    const userId = parseInt(session.user.id as string, 10);
    if (isNaN(userId)) {
        return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '5', 10);
    const skip = parseInt(searchParams.get('skip') || '0', 10);
    const isRead = searchParams.get('isRead');
    
    const where: any = { recipientUserId: userId };
    
    if (isRead !== null) {
      where.isRead = isRead === 'true';
    }

    const notifications = await prisma.systemNotification.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: skip,
      select: {
        id: true,
        message: true,
        type: true, 
        isRead: true,
        relatedEntityType: true,
        relatedEntityId: true,
        createdAt: true,
      }
    });

    const formattedNotifications = notifications.map(notification => {
      let path = '/notifications'; 

      // Get user role from session
      const userRole = session.user.role as string;
      const isHR = ['HR_ADMIN', 'HR_STAFF'].includes(userRole);
      const isEmployee = userRole === 'EMPLOYEE';
      const isAdmin = ['ADMIN', 'SYSTEM'].includes(userRole);

      if (notification.relatedEntityType === 'TRAINING_MATERIAL' && 
          notification.relatedEntityId && 
          notification.type === 'MISSING_REQUIREMENTS') { 
        path = `/main/employee/submit-missing-documents/${notification.relatedEntityId}`;
      } else if (notification.type === 'NEW_MATERIAL_SUBMITTED' && 
                 notification.relatedEntityType === 'TRAINING_MATERIAL' && 
                 notification.relatedEntityId) {
        path = `/main/hr/training-material-details/${notification.relatedEntityId}`; 
      } else if (notification.type === 'PARTICIPANT_REQUIREMENTS_UPDATED' &&
                 notification.relatedEntityType === 'TRAINING_MATERIAL' &&
                 notification.relatedEntityId) {
        // Redirect to appropriate dashboard based on user role
        if (isHR) {
          path = `/main/hr/Dashboard?module=approvals`;
        } else if (isEmployee) {
          path = `/main/employee/Dashboard?module=knowledge`;
        } else if (isAdmin) {
          path = `/main/admin/Dashboard`;
        }
      } else if (notification.type === 'MATERIAL_PENDING_OVERDUE' &&
                 notification.relatedEntityType === 'TRAINING_MATERIAL' &&
                 notification.relatedEntityId) {
        path = `/main/hr/training-material-details/${notification.relatedEntityId}`;
      } else if (notification.type === 'MATERIAL_READY_FOR_REVIEW' &&
                 notification.relatedEntityType === 'TRAINING_MATERIAL' &&
                 notification.relatedEntityId) {
        path = `/main/employee/training/${notification.relatedEntityId}`;
      } else if (notification.relatedEntityType === 'trainingMaterial' && notification.relatedEntityId) {
        path = `/main/employee/training/${notification.relatedEntityId}`;
      } else if (notification.relatedEntityType === 'assessment' && notification.relatedEntityId) {
        path = `/main/employee/assessment/${notification.relatedEntityId}`;
      }
      
      let displayTitle = 'Notification';
      if (notification.type) {
        displayTitle = notification.type
          .toLowerCase()
          .replace(/_/g, ' ')
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }

      return {
        id: notification.id,
        title: displayTitle, 
        message: notification.message,
        time: formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true }),
        read: notification.isRead,
        path: path,
        relatedEntityType: notification.relatedEntityType,
        relatedEntityId: notification.relatedEntityId,
      };
    });

    // Count total and unread notifications
    const totalCount = await prisma.systemNotification.count({
      where: { recipientUserId: userId }
    });
    
    const unreadCount = await prisma.systemNotification.count({
      where: { 
        recipientUserId: userId,
        isRead: false
      }
    });

    return NextResponse.json({
      notifications: formattedNotifications,
      totalCount,
      unreadCount
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
} 