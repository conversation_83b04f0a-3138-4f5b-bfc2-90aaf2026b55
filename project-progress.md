# Project-Roadmap
Title: Feature Compilation
Description: This is where all the features of the system can be viewed. It includes status(completed,partially completed, blocked, not-started) description, priority, dependencies (other feature it depends on), and additional notes.

# Features
## General ---

### Feature: User Authentication
    Status: Partially Completed
    Description: Implements the user authentication system, including login, logout, password recovery, and role-based redirection.
    Priority: High
    Dependencies: None
    Notes:
        - Next-Auth integration for additional security has not yet started
        - Forgot Password / Account Recovery: Completed
        - Login/Logout with RBAC: Partially Completed
            - IT Admin redirection pages: Not yet implemented
            - HR and Training Unit currently redirect to the same page
            - Employee redirection: Completed


## Employee

### Feature: Employee Home/Landing Page
    Status: Partially Completed
    Description: Employee Landing Page after Login. The user is able to view and search the training materials, filter via category, type, and competencies. 
    Priority: High
    Dependencies: 
                - Create New Course/Training Material Feature
                - Training Material Approval Feature
    Notes:
        - UI: Completed
        - Functionality: Not Yet Started
            -- No backend integration for fetching actual courses/training materials.

### Feature: Course/Training Material Creation
    Status: Partially Completed
    Description: Allows Employees to create new courses/training materials.
    Priority: High
    Dependencies: None
    Notes:
        - UI: Completed
        - Functionality: Partially Completed
        - Dynamic Error Handling: Not Started (needed across all modules)
        - Modules:
            - Overview and Details Page: Partially Completed
                - Required Input: Course Title, Description, Category, Competencies   
                - Optional: Objectives
                - Validation: Static validation complete, dynamic validation pending
                - Competency Search Suggestion needs improvement
                - State persistence issues with objectives and competencies when navigating
            - Training Modules Upload Section: Partially Completed
                - Required Inputs: Training Module Title, Training Module Materials/Resources
                - Initial module creation UX needs improvement
                - Validation: Static validation complete, dynamic validation pending
            - Tag Participants: Partially Completed
                - Required Input: Participant Name
                - Validation: Static validation complete, dynamic validation pending
            - Certifications and Forms: Partially Completed
                - Required Input: Assessment Form (PDF)
                - Optional: Certificate (PDF)
                - File storage system integration pending
                - Validation: Static validation complete, dynamic validation pending
            - Submit For Approval: Partially Completed
                - Required Input: None
                - Validation: Static validation complete, dynamic validation pending
                - What Happens Next information updated:
                    - System will notify all tagged participants
                    - Admin or approver will validate and approve the content
                    - You'll receive a notification once approved
            - File Storage System: Partially Completed
                - Forms are saved to the database
                - File system structure needs implementation:
                    - public/uploads/[training-material-id]
                        - Certifications
                        - Assessment forms
                        - Training Material
                            -- Training Module
                                --- Training Module Materials

## HR / Training Unit

### Feature: Publication Approval
    Status: Not Started
    Description: Allows HR and Training Unit to Approve of Materials Submitted By Employees
    Priority: High
    Dependencies: Course/Training Material Creation
    Notes:
        - UI: Completed
        - Functionality: Not Started
        - Modules:
            - Publication Approval Dashboard: Partially Completed
                - List of Materials for Review
                    - Information Needed: Title of Training Material, Training Material Creator, Category Of Material, Submitted Date (mm/dd/yy), Unit Of Employee, Status, Remove the Last activity, Actions (review button)
                    - Search and Filter by Status or Newest/Oldest 
                    - Pagination
                - Training Material Overview: Partially Completed
                    - Information Needed: Training Material Title, Course Overview(Training Material Description), Learning Objectives (Objectives), Course Details (Category, Competencies, Types(The types of Training Resources involved such as youtube link, pdf, docx, etc.))
                - Review Resources: Partially Completed
                    - Information Needed: Module Title, Module Description, Training Resource Name, Training Resource Description, Training Resource Type
                    - If Training Resource is a file then the user must view the resource in another tab similar to how browsers act when a user clicks a pdf. If it is a youtube link then similar to course creation, it should be previewable in the tab itself.
                - Participant Review: Partially Completed
                    - Information Needed: Training Participants Names/Email/Unit, Status of Passed forms (Assessment Form/Certification)
                    - Search and Filter By
                    - The User must be able to view the Assessment Form and Certification Passed by the Participants
                    - The User must be able to send a notification to participants regarding missing forms
                    - Pagination
                - Review Summary: Partially Completed
                    - Information Needed: Course Information (Title, Unit, Submitted By, Submission Date), Statistics (Total Modules, Total Participants, Percentage of Passed Forms both Assessment Form and Certification Form)
                    - Approve can only done when assessment form submission is 100%
                    - Upon Reject, Open Modal that Prompts the User to input reason of rejection, then when the user tries to submit it asks an are you sure question. If sure then send a notification to participant containing the reason of rejection


###  Feature: