'use client';

import React from 'react';
import { useState } from 'react';
import { ReviewData, TrainingParticipant, CompetencyLink } from '../types';
import { FileText, Users, BookOpen, Clock, User2, CheckCircle2, UserSquare, XCircle } from 'lucide-react';

interface ReviewSubmitProps {
  data: ReviewData;
  onApprove: () => void;
  onReject: (reason: string) => void;
  prevStep: () => void;
  isLoading: boolean;
}

const ReviewSubmit = ({ 
  data, 
  onApprove, 
  onReject,
  prevStep,
  isLoading 
}: ReviewSubmitProps) => {
  const anyPending = Array.isArray(data.materialCompetencies)
    ? data.materialCompetencies.some((comp: CompetencyLink) => 
        !!comp.temporaryCompetencyId && comp.status === 'pending'
      )
    : false;

  // Calculate completion statistics
  const totalParticipants = data.trainingParticipants?.length || 0;
  const completedAssessments = data.trainingParticipants?.filter((p: TrainingParticipant) => !!p.assessmentFormKey).length || 0;
  const completedCertifications = data.trainingParticipants?.filter((p: TrainingParticipant) => !!p.certificateKey).length || 0;
  const assessmentCompletionRate = totalParticipants ? Math.round((completedAssessments / totalParticipants) * 100) : 0;
  const certificationCompletionRate = totalParticipants ? Math.round((completedCertifications / totalParticipants) * 100) : 0;

  // New approval logic checks
  const competenciesExist = Array.isArray(data.materialCompetencies) && data.materialCompetencies.length > 0;
  
  const atLeastOneCompetencyApproved = (() => {
    if (!competenciesExist) return true; // No competencies, so doesn't block

    // Check if any official competency is present (implicitly approved)
    if (data.materialCompetencies.some(comp => !!comp.competencyId)) {
      return true;
    }

    // If only temporary competencies, at least one must be explicitly 'approved'
    return data.materialCompetencies.some(comp => 
      !!comp.temporaryCompetencyId && comp.status === 'approved'
    );
  })();

  const participantsExist = totalParticipants > 0;
  const allAssessmentsSubmitted = participantsExist
    ? completedAssessments === totalParticipants
    : true; // If no participants, this rule doesn't block approval

  // Combine all block conditions for approval
  const blockApproval = isLoading || 
                        anyPending || 
                        (competenciesExist && !atLeastOneCompetencyApproved) || 
                        (participantsExist && !allAssessmentsSubmitted);

  // Find creator
  const creator = data.trainingParticipants?.find(p => p.isCreator);
  const creatorName = creator ? `${creator.user.firstName} ${creator.user.lastName}` : 'N/A';
  // Use the included unit name, fallback to ID or N/A
  const creatorDepartmentName = creator?.user?.unit?.unitName 
    ?? (creator ? `Unit ID: ${creator.user.unitId}` : 'N/A');

  const handleApproveAction = () => {
    setShowApproveConfirmation(true);
  };

  const handleConfirmApprove = () => {
    onApprove();
    setShowApproveConfirmation(false);
  };

  const handleRejectAction = () => {
    setRejectionReason('');
    setShowRejectConfirmation(true);
  };

  const handleConfirmReject = () => {
    if (!rejectionReason.trim()) {
      alert('Please provide a reason for rejection.');
      return;
    }
    onReject(rejectionReason.trim());
    setShowRejectConfirmation(false);
  };

  let approvalTooltip = '';
  if (anyPending) {
    approvalTooltip = 'Resolve all pending competencies before publishing.';
  } else if (competenciesExist && !atLeastOneCompetencyApproved) {
    approvalTooltip = 'At least one competency must be approved.';
  } else if (participantsExist && !allAssessmentsSubmitted) {
    approvalTooltip = 'All participant assessments must be submitted before approval.';
  }

  const [showApproveConfirmation, setShowApproveConfirmation] = useState(false);
  const [showRejectConfirmation, setShowRejectConfirmation] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');

  return (
    <div className="space-y-8">
      {/* Review Summary */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Review Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Course Information */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 space-y-6">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider">Course Information</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Title</p>
                  <p className="font-medium">{data.title}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <User2 className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Creator</p>
                  <p className="font-medium">{creatorName}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <UserSquare className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Department</p>
                  <p className="font-medium">{creatorDepartmentName}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Submission Date</p>
                  <p className="font-medium">
                    {data.createdAt ? new Date(data.createdAt).toLocaleDateString() : 'Invalid Date'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="bg-white p-6 rounded-lg border border-gray-200 space-y-6">
            <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider">Statistics</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <BookOpen className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Total Modules</p>
                  <p className="font-medium">{data.modules.length} modules</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Total Participants</p>
                  <p className="font-medium">{totalParticipants} participants</p>
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">Assessment Completion</p>
                <div className="flex items-center gap-2">
                  <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-color3 transition-all duration-500"
                      style={{ width: `${assessmentCompletionRate}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-600">{assessmentCompletionRate}%</span>
                </div>
                <p className="text-xs text-gray-500">{completedAssessments} of {totalParticipants} completed</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">Certification Completion</p>
                <div className="flex items-center gap-2">
                  <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-color3 transition-all duration-500"
                      style={{ width: `${certificationCompletionRate}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-600">{certificationCompletionRate}%</span>
                </div>
                <p className="text-xs text-gray-500">{completedCertifications} of {totalParticipants} completed</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <button
          onClick={prevStep}
          className="px-6 py-2 border cursor-pointer border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isLoading}
        >
          Previous 
        </button>
        <div className='flex flex-col items-center'>
        <div className="flex gap-4">
          <button
            onClick={handleRejectAction}
            className="flex items-center gap-2 px-6 py-2 cursor-pointer bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            <XCircle className="w-4 h-4" />
            Reject
          </button>
          <button
            onClick={handleApproveAction}
            className="flex items-center gap-2 px-6 py-2 cursor-pointer bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading || blockApproval}
            title={approvalTooltip}
          >
            <CheckCircle2 className="w-4 h-4" />
            Approve
          </button>
        </div>
      </div>
      </div>
      <div className='w-full flex '> {anyPending && (
          <span className="pt-2 text-xs text-red-600 text-end  w-full">Resolve all pending competencies before publishing.</span>
        )}
        {!anyPending && competenciesExist && !atLeastOneCompetencyApproved && (
          <span className="pt-2 text-xs text-red-600 text-end  w-full">At least one competency must be approved to publish.</span>
        )}
        {!anyPending && !(competenciesExist && !atLeastOneCompetencyApproved) && participantsExist && !allAssessmentsSubmitted && (
          <span className="pt-2 text-xs text-red-600 text-end  w-full">All participant assessments must be submitted.</span>
        )}</div>

      {/* Approval Confirmation Dialog */}
      {showApproveConfirmation && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900/30 backdrop-blur-sm">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Confirm Approval</h3>
            <p className="text-gray-600 mb-6">Are you sure you want to approve this training material?</p>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => setShowApproveConfirmation(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmApprove}
                className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Confirmation Dialog */}
      {showRejectConfirmation && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-900/30 backdrop-blur-sm">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Confirm Rejection</h3>
            <div className="mb-4">
              <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Rejection <span className="text-red-500">*</span>
              </label>
              <textarea
                id="rejectionReason"
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500"
                placeholder="Please provide details..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
              />
            </div>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => setShowRejectConfirmation(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmReject}
                className={`px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed`}
                disabled={!rejectionReason.trim()}
              >
                Confirm Reject
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewSubmit; 