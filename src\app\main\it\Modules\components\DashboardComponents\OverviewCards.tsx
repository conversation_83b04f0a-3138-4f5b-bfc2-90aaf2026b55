import React, { memo, useMemo } from 'react';
import { StatCard } from '@/components/shared/StatCard';
import { Server, Shield, Users, Activity } from 'lucide-react';

const OverviewCards: React.FC = () => {
  const cards = useMemo(() => [
    {
      title: 'System Health',
      value: '98%',
      icon: Server,
      trend: 2.5,
      subtitle: 'Overall system status',
      color: 'text-green-600'
    },
    {
      title: 'Security Score',
      value: '92',
      icon: Shield,
      trend: -1.2,
      subtitle: 'Security compliance',
      color: 'text-blue-600'
    },
    {
      title: 'Active Users',
      value: '1,234',
      icon: Users,
      trend: 5.8,
      subtitle: 'Current online users',
      color: 'text-purple-600'
    },
    {
      title: 'System Load',
      value: '45%',
      icon: Activity,
      trend: -2.1,
      subtitle: 'Current CPU usage',
      color: 'text-orange-600'
    }
  ], []);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 h-full min-h-0">
      {cards.map((card, idx) => (
        <div key={idx} className="flex flex-col h-full min-h-0 bg-white rounded-lg shadow-sm border border-gray-100 p-4 overflow-auto">
          <StatCard
            {...card}
            className="w-full h-full flex-1 min-h-0 truncate"
          />
        </div>
      ))}
    </div>
  );
};

export default memo(OverviewCards); 