// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum EmployeeType {
  COS
  PERMANENT
}

enum MaterialStatus {
  DRAFT
  PENDING_APPROVAL
  PUBLISHED
  ARCHIVED
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

enum PermissionName {
  CREATE_TRAINING_MATERIAL
  EDIT_OWN_TRAINING_MATERIAL
  EDIT_ANY_TRAINING_MATERIAL
  DELETE_OWN_TRAINING_MATERIAL
  DELETE_ANY_TRAINING_MATERIAL
  PUBLISH_TRAINING_MATERIAL
  ARCHIVE_TRAINING_MATERIAL
  APPROVE_TRAINING_MATERIAL
  VIEW_PUBLISHED_TRAINING_MATERIAL
  VIEW_ALL_TRAINING_MATERIAL
  MANAGE_MODULES
  UPLOAD_FILESS
  VIEW_OWN_PROGRESS
  VIEW_ALL_USER_PROGRESS
  MANAGE_USERS
  MANAGE_ROLES_PERMISSIONS
  TAG_PARTICIPANTS
  MANAGE_CATEGORIES
  MANAGE_COMPETENCIES
}

model Division {
  id           Int      @id @default(autoincrement())
  divisionName String   @unique

  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  units Unit[]

  @@map("divisions")
}

model Unit {
  id         Int      @id @default(autoincrement())
  unitName   String   @unique
  divisionId Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  division Division @relation(fields: [divisionId], references: [id], onDelete: Cascade)
  users    User[]

  @@unique([divisionId, unitName])
  @@map("units")
}

model Role {
  id        Int      @id @default(autoincrement())
  roleName  String   @unique
  description String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  users       User[]
  rolePermissions RolePermission[]

  @@map("roles")
}

model User {
  id                   Int           @id @default(autoincrement())
  unitId               Int
  roleId               Int
  firstName            String        @db.VarChar(100)
  lastName             String        @db.VarChar(100)
  password             String
  email                String        @unique
  employeeType         EmployeeType

  // Password Reset Fields
  passwordResetToken   String?   @unique
  passwordResetExpires DateTime?

  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  lastLoginAt          DateTime?
  failedLoginAttempts  Int           @default(0)
  accountLockedUntil   DateTime?

  unit   Unit @relation(fields: [unitId], references: [id], onDelete: Restrict)
  role   Role @relation(fields: [roleId], references: [id], onDelete: Restrict)

  userPermissions       UserPermission[]

  // Logs
  userSessions          UserSession[]
  auditLogs             AuditLog[]
  userModuleProgress    UserModuleProgress[]
  userTrainingProgress  UserTrainingProgress[]
  userFileProgress UserFileProgress[]

  // Participation

  involvedTrainingParticipants TrainingParticipant[] @relation("InvolvedParticipants")

  // Approvals
  materialApprovals     MaterialApproval[]
  materialStatusLogs    MaterialStatusLog[]

  // Competencies
  createdTemporaryCompetencies TemporaryCompetency[] @relation("CreatedTemporaryCompetencies")
  reviewedTemporaryCompetencies TemporaryCompetency[] @relation("ReviewedTemporaryCompetencies")

  // Add these missing relations
  userSearches UserSearch[]
  notifications SystemNotification[] @relation("UserNotifications")

  @@map("users")
}

model Permission {
  id          Int            @id @default(autoincrement())
  name        PermissionName @unique
  description String?        @db.Text

  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  rolePermissions RolePermission[]
  userPermissions UserPermission[]

  @@map("permissions")
}

model RolePermission {
  id           Int @id @default(autoincrement())
  roleId       Int
  permissionId Int

  role        Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission  Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("rolePermissions")
}

model UserPermission {
  id           Int @id @default(autoincrement())
  userId       Int
  permissionId Int

  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission  Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([userId, permissionId])
  @@map("userPermissions")
}

model UserSession {
  id        Int      @id @default(autoincrement())
  userId    Int
  token     String
  ipAddress String
  userAgent String  @db.Text
  expiresAt DateTime

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("userSessions")
}

model Category{
  id Int @id @default(autoincrement())
  categoryName String @unique @db.VarChar(255)
  description String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  trainingMaterials TrainingMaterial[]

  @@map("categories")
}

model Competency{
  id Int @id @default(autoincrement())
  competencyName String @unique @db.VarChar(255)
  description String? @db.Text

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  materialCompetencies MaterialCompetency[]

  @@map("competencies")
}

model TemporaryCompetency {
  id                    Int                   @id @default(autoincrement())
  competencyName        String                @unique @db.VarChar(255)
  description           String?
  createdByUserId       Int? // Adjusted based on migration SQL
  status                String                @default("PENDING") @db.VarChar(50)
  createdAt             DateTime              @default(now()) @db.Timestamp(3)
  updatedAt             DateTime              @updatedAt @db.Timestamp(3)
  reviewedAt            DateTime?             @db.Timestamp(3)
  reviewedByUserId      Int?
  createdByUser         User?                 @relation("CreatedTemporaryCompetencies", fields: [createdByUserId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  reviewedByUser        User?                 @relation("ReviewedTemporaryCompetencies", fields: [reviewedByUserId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  materialCompetencies  MaterialCompetency[]

  @@unique([competencyName, status])
  @@map("temporary_competencies")
}

model TrainingMaterial {
  id                   Int                    @id @default(autoincrement())
  title                String                 @db.VarChar(255)
  description          String?
  categoryId           Int
  status               MaterialStatus         @default(DRAFT)
  publishedAt          DateTime?              @db.Timestamp(3)
  createdAt            DateTime               @default(now()) @db.Timestamp(3)
  updatedAt            DateTime               @updatedAt @db.Timestamp(3)
  category             Category               @relation(fields: [categoryId], references: [id], onDelete: Restrict, onUpdate: Cascade)
  materialCompetencies MaterialCompetency[]
  trainingParticipants TrainingParticipant[]
  modules              Module[]
  materialApprovals    MaterialApproval[]
  materialStatusLogs   MaterialStatusLog[]
  userTrainingProgress UserTrainingProgress[]
  objectives           Objective[] // Added in migration
}

model Objective {
  id                 Int              @id @default(autoincrement())
  trainingMaterialId Int
  text               String
  displayOrder       Int
  createdAt          DateTime         @default(now()) @db.Timestamp(3)
  updatedAt          DateTime         @updatedAt @db.Timestamp(3)
  trainingMaterial   TrainingMaterial @relation(fields: [trainingMaterialId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([trainingMaterialId, displayOrder])
  @@index([trainingMaterialId]) // Added in migration
}

model MaterialCompetency {
  id                     Int                   @id @default(autoincrement())
  training_material_id   Int
  competency_id          Int?
  temporary_competency_id Int?
  assigned_at            DateTime              @default(now()) @db.Timestamp(3)
  competency             Competency?           @relation(fields: [competency_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
  training_material      TrainingMaterial      @relation(fields: [training_material_id], references: [id], onDelete: Cascade, onUpdate: Cascade)
  temporaryCompetency    TemporaryCompetency?  @relation(fields: [temporary_competency_id], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([training_material_id, competency_id, temporary_competency_id])
  @@map("material_competencies")
}

model TrainingParticipant {
  id                 Int      @id @default(autoincrement())
  userId             Int
  trainingMaterialId Int
  isCreator          Boolean  @default(false)

  certificateKey     String?  @db.VarChar(512)
  assessmentFormKey  String?  @db.VarChar(512)

  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  user             User             @relation("InvolvedParticipants", fields: [userId], references: [id], onDelete: Cascade)
  trainingMaterial TrainingMaterial @relation(fields: [trainingMaterialId], references: [id], onDelete: Cascade)

  @@unique([userId, trainingMaterialId])
  @@map("training_participants")
}

model Module {
  id                 Int                  @id @default(autoincrement())
  trainingMaterialId Int
  title              String               @db.VarChar(255)
  description        String?
  moduleOrder        Int
  createdAt          DateTime             @default(now()) @db.Timestamp(3)
  updatedAt          DateTime             @updatedAt @db.Timestamp(3)
  trainingMaterial   TrainingMaterial     @relation(fields: [trainingMaterialId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  userModuleProgress UserModuleProgress[]
  resources          TrainingResource[]   @relation("ModuleResources")

  @@unique([trainingMaterialId, moduleOrder])
  @@map("modules")
}

model TrainingResource {
  id             Int      @id @default(autoincrement())
  resourceType   String   @db.VarChar(50) // e.g., \'file\', \'link\'

  // File-specific fields (optional)
  fileKey        String?  @unique @db.VarChar(1024)
  fileType       String?  @db.VarChar(100)
  fileName       String?  @db.Text

  // Link-specific fields (optional)
  resourceUrl    String?  @db.Text // The actual URL
  description    String?  @db.Text
  videoType      String?  @db.VarChar(50) // e.g., \'youtube\', \'other\'

  // Order within a module
  displayOrder   Int      @default(1)

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  modules        Module[] @relation("ModuleResources")

  userFileProgress UserFileProgress[]

  // Ensure either a fileKey or resourceUrl is present (example using @@raw - adjust based on DB)
  // @@raw("CONSTRAINT check_resource_source CHECK ((\"fileKey\" IS NOT NULL) OR (\"resourceUrl\" IS NOT NULL))")
  // Note: Raw constraints might need adjustments based on the specific database and Prisma version.
  // A simpler approach might be application-level validation.
}

model UserTrainingProgress {
  id                 Int            @id @default(autoincrement())
  userId             Int
  trainingMaterialId Int
  status             ProgressStatus @default(NOT_STARTED)
  progressPercentage Int            @default(0)
  startedAt          DateTime?
  completedAt        DateTime?
  lastAccessedAt     DateTime?

  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @updatedAt

  user             User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  trainingMaterial TrainingMaterial @relation(fields: [trainingMaterialId], references: [id], onDelete: Cascade)

  @@unique([userId, trainingMaterialId])
  @@map("userTrainingProgress")
}

model UserModuleProgress {
  id            Int            @id @default(autoincrement())
  userId        Int
  moduleId      Int
  status        ProgressStatus @default(NOT_STARTED)
  startedAt     DateTime?
  completedAt   DateTime?
  lastAccessedAt DateTime?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  module Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  @@unique([userId, moduleId])
  @@map("userModuleProgress")
}

model UserFileProgress {
  id             Int      @id @default(autoincrement())
  userId         Int
  progress       Int      @default(0)
  lastPage       Int?
  lastTimestamp  Int?
  status         ProgressStatus @default(NOT_STARTED)
  completedAt    DateTime? @db.Timestamp(3)
  lastAccessedAt DateTime? @db.Timestamp(3)
  created_at     DateTime @default(now()) @db.Timestamp(3)
  updated_at     DateTime @updatedAt @db.Timestamp(3)
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  resourceId     Int
  resource       TrainingResource @relation(fields: [resourceId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@unique([userId, resourceId])
}

model UserSearch {
  id          Int      @id @default(autoincrement())
  userId      Int
  searchQuery String   @db.Text
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("userSearches")
}

model AuditLog {
  id           Int       @id @default(autoincrement())
  userId       Int?
  action       String    @db.VarChar(255)
  targetEntity String?
  targetId     Int?
  details      Json?
  ipAddress    String?
  createdAt    DateTime  @default(now())

  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("auditLogs")
}

model MaterialApproval {
  id                 Int            @id @default(autoincrement())
  trainingMaterialId Int
  approverUserId     Int
  status             MaterialStatus @default(PENDING_APPROVAL)
  comments           String?        @db.Text
  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @updatedAt


  trainingMaterial TrainingMaterial @relation(fields: [trainingMaterialId], references: [id], onDelete: Cascade)
  approver         User             @relation(fields: [approverUserId], references: [id], onDelete: Restrict) // Don't delete approval if approver deleted

  @@map("materialApprovals")
}

model MaterialStatusLog {
  id                 Int      @id @default(autoincrement())
  trainingMaterialId Int
  changedByUserId    Int?
  oldStatus          String?
  newStatus          String
  reason             String?  @db.Text
  createdAt          DateTime @default(now())

  trainingMaterial TrainingMaterial @relation(fields: [trainingMaterialId], references: [id], onDelete: Cascade)
  changedByUser    User?            @relation(fields: [changedByUserId], references: [id], onDelete: SetNull)

  @@map("materialStatusLogs")
}

model SystemNotification {
  id                Int       @id @default(autoincrement())
  recipientUserId   Int?
  message           String    @db.Text
  type              String    @db.VarChar(100)
  isRead            Boolean   @default(false)
  relatedEntityType String?
  relatedEntityId   Int?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  recipient User? @relation("UserNotifications", fields: [recipientUserId], references: [id], onDelete: Cascade)

  @@map("systemNotifications")
}
