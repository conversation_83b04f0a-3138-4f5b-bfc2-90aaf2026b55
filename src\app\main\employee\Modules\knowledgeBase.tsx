'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { FiChevronDown, FiX, FiBookOpen, FiVideo, FiFileText, FiUser, FiClock, FiArchive, FiRefreshCw } from 'react-icons/fi';
import SearchBar from '@/components/ui/SearchBar';
import Pagination from '@/components/ui/Pagination';
import {
  learningCourses,
  archivedCourses,
  categories,
  types,
  tags,
  Course
} from '@/constants/courseData';
import { trainingMaterialApi } from '@/lib/trainingMaterial';
import { useRouter } from 'next/navigation';

interface FilterDropdownProps {
  title: string;
  options: string[];
  selected: string | string[] | null;
  onSelect: (option: string | string[]) => void;
  onClear: () => void;
  multiple?: boolean;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  title, options, selected, onSelect, onClear, multiple = false
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (option: string) => {
    if (multiple) {
      if (Array.isArray(selected)) {
        const isSelected = selected.includes(option);
        if (isSelected) {
          onSelect(selected.filter(item => item !== option));
        } else {
          onSelect([...selected, option]);
        }
      } else {
        onSelect([option]);
      }
    } else {
      onSelect(option === selected ? "" : option);
      setIsOpen(false);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center cursor-pointer gap-2 px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm text-gray-700 hover:bg-gray-50"
      >
        {!multiple && selected ? (
          <div className="flex items-center gap-2">
            <span className="font-medium text-[#0077CC]">{selected}</span>
            <FiX
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onClear();
              }}
            />
          </div>
        ) : (
          <span className="font-medium flex items-center gap-1">
            {title} <FiChevronDown className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </span>
        )}
        {Array.isArray(selected) && selected.length > 0 && (
          <span className="flex items-center gap-1 text-[#0077CC] bg-blue-50 px-2 py-0.5 rounded-full text-xs">
            {selected.length} selected
            <FiX
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onClear();
              }}
            />
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-[15]">
          <div className="py-1 max-h-[200px] overflow-y-auto">
            {options.map((option) => (
              <button
                key={option}
                onClick={() => handleSelect(option)}
                className={`w-full text-left px-4 py-2 text-sm cursor-pointer ${
                  multiple
                    ? Array.isArray(selected) && selected.includes(option)
                      ? 'bg-blue-50 text-[#0077CC]'
                      : 'text-gray-700 hover:bg-gray-50'
                    : selected === option
                    ? 'bg-blue-50 text-[#0077CC]'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                {multiple && (
                  <span className="inline-block w-4 mr-2">
                    {Array.isArray(selected) && selected.includes(option) && '✓'}
                  </span>
                )}
                {option}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const NoDataState: React.FC<{ message: string; subMessage?: string }> = ({ message, subMessage }) => (
  <div className="col-span-3 flex flex-col items-center justify-center py-12 px-4 text-center">
    <div className="w-16 h-16 mb-4 text-gray-400">
      <FiBookOpen className="w-full h-full" />
    </div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">{message}</h3>
    {subMessage && <p className="text-sm text-gray-500">{subMessage}</p>}
  </div>
);

interface CourseCardProps {
  course: any;
  action?: string;
  onEdit?: () => Promise<void>;
  onArchive?: () => void;
  onRestore?: () => void;
}

const CourseCard = ({ course, action = '', onEdit = () => Promise.resolve(), onArchive = () => {}, onRestore = () => {} }: CourseCardProps) => {
  const router = useRouter();
  const formattedDate = course.submittedDate ? new Date(course.submittedDate).toLocaleDateString() : '';
  const tags = [...(course.officialTags || []), ...(course.temporaryTags || [])];
  const [showTooltip, setShowTooltip] = useState(false);
  const [showTitleTooltip, setShowTitleTooltip] = useState(false);
  const [showRestoreTooltip, setShowRestoreTooltip] = useState(false);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const [isTitleTruncated, setIsTitleTruncated] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error' | 'warning'>('success');

  useEffect(() => {
    if (titleRef.current) {
      const el = titleRef.current;
      setIsTitleTruncated(el.scrollHeight > el.clientHeight + 1);
    }
  }, [course.title]);

  const handleCourseClick = () => {
    router.push(`/main/employee/Dashboard?module=courseDetails&id=${course.id}`);
  };

  const renderTypeIcon = () => {
    if (course.materialType === 'VIDEO') {
      return (
        <span className="group relative flex items-center">
          <FiVideo className="text-green-500 w-5 h-5" />
          <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
            Video Course
          </span>
        </span>
      );
    } else if (course.materialType === 'DOCUMENT') {
      return (
        <span className="group relative flex items-center">
          <FiFileText className="text-[#0077CC] w-5 h-5" />
          <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
            Document Based
          </span>
        </span>
      );
    } else if (course.materialType === 'BOTH') {
      return (
        <span className="flex gap-2 items-center">
          <span className="group relative flex items-center">
            <FiVideo className="text-green-500 w-5 h-5" />
            <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
              Video Course
            </span>
          </span>
          <span className="group relative flex items-center">
            <FiFileText className="text-[#0077CC] w-5 h-5" />
            <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
              Document Based
            </span>
          </span>
        </span>
      );
    }
    return null;
  };

  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation();
    setAlertMessage('Are you sure you want to archive this resource?');
    setAlertType('warning');
    setShowAlert(true);
  };

  const handleRestore = (e: React.MouseEvent) => {
    e.stopPropagation();
    setAlertMessage('Are you sure you want to restore this resource?');
    setAlertType('warning');
    setShowAlert(true);
  };

  const confirmAction = () => {
    if (alertMessage.includes('archive')) {
      onArchive();
      setAlertMessage('Resource archived successfully');
      setAlertType('success');
    } else if (alertMessage.includes('restore')) {
      onRestore();
      setAlertMessage('Resource restored successfully');
      setAlertType('success');
    }
    setTimeout(() => setShowAlert(false), 2000);
  };

  return (
    <div className="relative">
      {showAlert && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-[9999]">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">Confirmation</h3>
            <p className="mb-6">{alertMessage}</p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowAlert(false)}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md cursor-pointer"
              >
                Cancel
              </button>
              <button
                onClick={confirmAction}
                className={`px-4 py-2 text-white rounded-md cursor-pointer ${
                  alertType === 'warning' ? 'bg-[#0077CC] hover:bg-[#0066B3]' : 'bg-green-500 hover:bg-green-600'
                }`}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
      <div
        className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-100 flex flex-col h-full min-h-[280px] md:min-h-[300px] w-full max-w-full"
        onClick={handleCourseClick}
        tabIndex={0}
        role="button"
        aria-label={`View details for ${course.title}`}
      >
        <div className="flex flex-col h-full p-4 gap-1.5 justify-between">
          <div className="flex items-center justify-start gap-2 mb-2">
            <span className="text-[#1B2B4B] text-xs font-semibold twhitespace-nowrap">{course.category}</span>
          </div>
          {/* Title with custom tooltip */}
          <div className="relative w-full">
            <h3
              ref={titleRef}
              className="text-xl font-bold mb-1 text-black line-clamp-2 cursor-pointer"
              onMouseEnter={() => isTitleTruncated && setShowTitleTooltip(true)}
              onMouseLeave={() => setShowTitleTooltip(false)}
              style={{ wordBreak: 'break-word' }}
            >
              {course.title}
            </h3>
            {showTitleTooltip && (
              <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 z-30 bg-black text-white text-xs rounded px-3 py-2 shadow-lg max-w-xs w-max whitespace-pre-line text-center pointer-events-none">
                {course.title}
              </div>
            )}
          </div>
          {/* Instructor and tags row */}
          <div className="flex items-center justify-between gap-2 mb-1 w-full flex-wrap">
            <p className="text-black mb-0.5 text-sm font-semibold flex items-center gap-1 flex-shrink-0 truncate max-w-[50%]">
              <FiUser className="text-gray-500 w-4 h-4" />
              {course.author?.name || course.instructor || 'Unknown'}
            </p>
            {tags.length > 0 && (
              <div className="flex items-center gap-1 flex-shrink-0">
                <span className="bg-blue-50 text-blue-800 border border-blue-100 px-2 py-0.5 rounded-full text-xs font-medium truncate max-w-[200px]">{tags[0]}</span>
                {tags.length > 1 && (
                  <span
                    className="relative group cursor-pointer text-xs text-gray-500 hover:underline"
                    onMouseEnter={() => setShowTooltip(true)}
                    onMouseLeave={() => setShowTooltip(false)}
                  >
                    + See more
                    {showTooltip && (
                      <span className="absolute left-1/2 -translate-x-1/2 top-full mt-2 z-20 bg-black text-white text-xs rounded px-3 py-2 whitespace-pre-line shadow-lg min-w-[120px] max-w-xs">
                        {tags.join(', ')}
                      </span>
                    )}
                  </span>
                )}
              </div>
            )}
          </div>
          <p className="text-gray-600 text-sm mb-0.5 leading-6 line-clamp-4">{course.description}</p>
          {/* Course Type Icon(s) under description */}
          <div className="flex items-center gap-2 mt-0.5 mb-1">
            <span className="text-xs font-semibold text-gray-600">Course Type:</span>
            {renderTypeIcon()}
          </div>
          <div className="flex border-t border-gray-100 items-center justify-between text-xs text-black pt-2 mt-auto">
            <div className="flex items-center gap-1">
              <FiClock className="text-black w-4 h-4" />
              <span className="text-xs">Uploaded on: {formattedDate}</span>
            </div>
            <div className="flex gap-2">
              {action === 'Archive' ? (
                <button
                  onClick={handleArchive}
                  className="bg-[#dd1c1a] border border-[#dd1c1a] text-white py-2 px-5 rounded-lg hover:bg-[#a31621] hover:text-white transition-colors cursor-pointer"
                >
                  Archive
                </button>
              ) : course.status === 'ARCHIVED' ? (
                <div className="relative flex flex-col items-center">
                  <button
                    onClick={handleRestore}
                    onMouseEnter={() => setShowRestoreTooltip(true)}
                    onMouseLeave={() => setShowRestoreTooltip(false)}
                    className="bg-[#0077CC] text-white p-2 rounded-lg hover:bg-[#0066B3] transition-colors cursor-pointer"
                  >
                    <FiRefreshCw className="w-4 h-4" />
                  </button>
                  <div
                    className={`pointer-events-none transition-opacity duration-200 absolute left-1/2 -translate-x-1/2 bottom-full mb-3 px-3 py-2 bg-gray-900 text-white text-xs rounded shadow-lg whitespace-nowrap z-[10001] ${showRestoreTooltip ? 'opacity-100' : 'opacity-0'}`}
                  >
                    Restore Resource
                  </div>
                </div>
              ) : (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit();
                  }}
                  className="bg-[#0077CC] px-5 text-xs font-semibold cursor-pointer text-white py-2 rounded-lg hover:bg-[#0066B3] focus:bg-[#005999] duration-200 transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-200 "
                >
                  {action || 'View'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const KnowledgeBase: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Training');
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([]);
  const [myResources, setMyResources] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isFaqOpen, setIsFaqOpen] = useState(false);
  const coursesPerPage = 6;
  const router = useRouter();
  const [trainingResources, setTrainingResources] = useState<any[]>([]);
  const [archiveResources, setArchiveResources] = useState<any[]>([]);

  // Sample data for when API returns no results
  const sampleTrainingCourse = {
    id: 9999,
    title: "Introduction to Web Development",
    author: { name: "John Smith", department: "Training Department" },
    submittedDate: new Date().toISOString(),
    officialTags: ["Web Development"],
    temporaryTags: ["Frontend"],
    status: "PUBLISHED",
    category: "Training",
    description: "Learn the fundamentals of web development including HTML, CSS, and JavaScript. This course is designed for beginners with no prior experience.",
    materialType: "BOTH"
  };

  const sampleArchiveCourse = {
    id: 8888,
    title: "Advanced Data Analysis",
    author: { name: "Emily Johnson", department: "Data Science" },
    submittedDate: new Date().toISOString(),
    officialTags: ["Data Analysis"],
    temporaryTags: ["Excel", "Power BI"],
    status: "ARCHIVED",
    category: "CPE",
    description: "An in-depth look at advanced data analysis techniques using modern tools. This course was archived on completion of the latest version.",
    materialType: "DOCUMENT"
  };

  // Fetch My Resources data
  const fetchMyResources = async (searchQuery = '') => {
    if (activeTab !== 'My Resources') return;

    setIsLoading(true);
    setError(null);
    try {
      const { materials, pagination } = await trainingMaterialApi.list({
        page: currentPage,
        limit: coursesPerPage,
        search: searchQuery,
        status: 'PUBLISHED', // or whatever status you want for My Resources
        sortBy: 'newest'
      });

      setMyResources(materials);
    } catch (error) {
      console.error('Error fetching resources:', error);
      setError('Failed to fetch your resources');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch Training and Archive resources from API
  const fetchTrainingResources = async (searchQuery = '') => {
    setIsLoading(true);
    setError(null);
    try {
      const { materials } = await trainingMaterialApi.list({
        page: currentPage,
        limit: coursesPerPage,
        search: searchQuery,
        status: 'PUBLISHED',
        sortBy: 'newest',
      });
      setTrainingResources(materials);
    } catch (error) {
      setError('Failed to fetch training resources');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchArchiveResources = async (searchQuery = '') => {
    setIsLoading(true);
    setError(null);
    try {
      const { materials } = await trainingMaterialApi.list({
        page: currentPage,
        limit: coursesPerPage,
        search: searchQuery,
        status: 'ARCHIVED',
        sortBy: 'newest',
      });
      setArchiveResources(materials);
    } catch (error) {
      setError('Failed to fetch archived resources');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'My Resources') {
      fetchMyResources();
    } else if (activeTab === 'Archive') {
      fetchArchiveResources();
    }
  }, [activeTab, selectedCategory, selectedType, selectedTags]);

  const applyFilters = (searchQuery: string) => {
    const searchTerm = searchQuery.toLowerCase();
    let currentCourses: Course[];

    // Only apply to Training and Archive tabs
    switch (activeTab) {
      case 'Training':
        currentCourses = learningCourses;
        break;
      case 'Archive':
        currentCourses = archivedCourses;
        break;
      default:
        return; // Don't process for My Resources tab
    }

    let results = currentCourses.filter(course => {
      const matchesSearch =
        course.title.toLowerCase().includes(searchTerm) ||
        course.instructor.toLowerCase().includes(searchTerm) ||
        course.tag.toLowerCase().includes(searchTerm) ||
        course.lastViewed.toLowerCase().includes(searchTerm);

      const matchesCategory = !selectedCategory || course.category === selectedCategory;
      const matchesType = !selectedType || (course as any).type === selectedType;
      const matchesTags = selectedTags.length === 0 || selectedTags.includes(course.tag);

      return matchesSearch && matchesCategory && matchesType && matchesTags;
    });

    setFilteredCourses(results);
  };

  const handleSearch = (query: string, setResultCount?: (count: number) => void) => {
    if (activeTab === 'My Resources') {
      fetchMyResources(query);
    } else {
      applyFilters(query);
      if (setResultCount) {
        setResultCount(filteredCourses.length);
      }
    }
  };

  const handleArchive = async (materialId: number) => {
    try {
      await trainingMaterialApi.archive(materialId);
      // Refresh the materials list
      fetchMyResources();
    } catch (error) {
      console.error('Error archiving material:', error);
    }
  };

  const handleRestore = async (materialId: number) => {
    try {
      await trainingMaterialApi.restore(materialId);
      // Refresh the materials list
      fetchArchiveResources();
    } catch (error) {
      console.error('Error restoring material:', error);
    }
  };

  const renderCourses = () => {
    if (activeTab === 'My Resources') {
      if (isLoading) {
        return (
          <div className="col-span-3 flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0077CC]"></div>
          </div>
        );
      }

      if (error) {
        return (
          <div className="col-span-3 bg-red-50 text-red-600 p-4 rounded-lg">
            {error}
          </div>
        );
      }

      if (myResources.length === 0) {
        return (
          <NoDataState 
            message="No Resources Available" 
            subMessage="You haven't uploaded any training materials yet. Start by uploading your first resource."
          />
        );
      }

      return myResources.map((material) => (
        <CourseCard
          key={material.id}
          course={material}
          action="Archive"
          onEdit={async () => Promise.resolve()}
          onArchive={() => handleArchive(material.id)}
        />
      ));
    }

    if (activeTab === 'Training') {
      // Training tab only shows sample card, no API fetch needed
      return <CourseCard key="sample-training" course={sampleTrainingCourse} />;
    }
    
    if (activeTab === 'Archive') {
      if (isLoading) {
        return (
          <div className="col-span-3 flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0077CC]"></div>
          </div>
        );
      }
      if (error) {
        return (
          <div className="col-span-3 bg-red-50 text-red-600 p-4 rounded-lg">
            {error}
          </div>
        );
      }
      // Show sample card if no API resources
      if (!archiveResources || archiveResources.length === 0) {
        // Return the sample card instead of no data message
        return <CourseCard key="sample-archive" course={sampleArchiveCourse} />;
      }
      return archiveResources.map((course: any) => (
        <CourseCard 
          key={course.id} 
          course={course} 
          onRestore={() => handleRestore(course.id)}
        />
      ));
    }

    // For Training and Archive tabs
    const currentCourses = filteredCourses.slice(
      (currentPage - 1) * coursesPerPage,
      currentPage * coursesPerPage
    );

    if (filteredCourses.length === 0) {
      const hasActiveFilters = selectedCategory || selectedType || selectedTags.length > 0;
      return (
        <NoDataState 
          message={hasActiveFilters ? "No Matching Resources Found" : "No Resources Available"}
          subMessage={hasActiveFilters 
            ? "Try adjusting your filters or search criteria to find what you're looking for."
            : activeTab === 'Training' 
              ? "There are no training courses available at the moment. Check back later for updates."
              : "There are no archived courses available at the moment."}
        />
      );
    }

    return currentCourses.map((course, index) => (
      <CourseCard
        key={index}
        course={course}
        onEdit={async () => Promise.resolve()}
        onArchive={() => {}}
      />
    ));
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (activeTab === 'My Resources') {
      fetchMyResources();
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const totalPages = Math.ceil(filteredCourses.length / coursesPerPage);
  const startIndex = (currentPage - 1) * coursesPerPage;
  const endIndex = startIndex + coursesPerPage;
  const currentCourses = filteredCourses.slice(startIndex, endIndex);

  return (
    <div className="p-4">
      <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:justify-between sm:items-center mb-4">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <div>
          <Link href="/main/employee/Dashboard" className="flex gap-1 items-center text-md hover:tracking-wider hover:underline duration-300 text-gray-700">
            <FiBookOpen/>
            <span>Browse More Courses</span>
          </Link>
        </div>
      </div>

      <p className="text-black text-md mb-4">Manage your own materials and ongoing training</p>

      <div className="mb-8">
        <div className="flex border-b border-gray-200">
          <button
            className={`px-6 py-3 text-sm cursor-pointer font-medium ${
              activeTab === 'Training'
                ? 'text-[#0077CC] border-b-2 border-[#0077CC] bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('Training')}
          >
            Training
          </button>
          <button
            className={`px-6 py-3 text-sm cursor-pointer font-medium ${
              activeTab === 'My Resources'
                ? 'text-[#0077CC] border-b-2 border-[#0077CC] bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('My Resources')}
          >
            My Resources
          </button>
          <button
            className={`px-6 py-3 text-sm cursor-pointer font-medium ${
              activeTab === 'Archive'
                ? 'text-[#0077CC] border-b-2 border-[#0077CC] bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('Archive')}
          >
            Archive
          </button>
        </div>
      </div>

      <div className="mb-6 flex items-center justify-between">
        <div className="w-full">
          <h2 className="text-xl font-semibold text-gray-900 ">
            {activeTab === 'Training' ? 'Your Courses' :
             activeTab === 'My Resources' ? 'Your Uploaded Courses' :
             'Archived Courses'}
          </h2>
          <div className="flex gap-2 p-3 max-w-full items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              <FilterDropdown
                title="Category"
                options={categories}
                selected={selectedCategory}
                onSelect={(value) => setSelectedCategory(value as string)}
                onClear={() => setSelectedCategory(null)}
              />
              <FilterDropdown
                title="Type"
                options={types}
                selected={selectedType}
                onSelect={(value) => setSelectedType(value as string)}
                onClear={() => setSelectedType(null)}
              />
              <FilterDropdown
                title="Tags"
                options={tags}
                selected={selectedTags}
                onSelect={(value) => setSelectedTags(value as string[])}
                onClear={() => setSelectedTags([])}
                multiple={true}
              />
            </div>
            <div className="sm:w-[350px] md:w-[400px]">
              <SearchBar
                onSearch={handleSearch}
                placeholder={`Search your ${activeTab.toLowerCase()}...`}
                title=""
                subtitle=""
                showResultCount={false}
                className="bg-transparent text-gray-900 shadow-sm"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {renderCourses()}
      </div>

      {activeTab === 'My Resources' ? (
        // For My Resources tab (API data)
        myResources.length > coursesPerPage && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                currentPage > 1
                  ? 'bg-white text-[#0077CC] border border-[#0077CC] hover:bg-[#0077CC] hover:text-white shadow-sm'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <span>Previous</span>
            </button>
            
            <div className="flex items-center gap-2">
              {Array.from({ length: Math.ceil(myResources.length / coursesPerPage) }, (_, i) => i + 1).map((pageNum) => (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`w-10 h-10 rounded-lg transition-all duration-200 ${
                    currentPage === pageNum
                      ? 'bg-[#0077CC] text-white shadow-md'
                      : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                  }`}
                >
                  {pageNum}
                </button>
              ))}
            </div>

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === Math.ceil(myResources.length / coursesPerPage)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                currentPage < Math.ceil(myResources.length / coursesPerPage)
                  ? 'bg-white text-[#0077CC] border border-[#0077CC] hover:bg-[#0077CC] hover:text-white shadow-sm'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <span>Next</span>
            </button>
          </div>
        )
      ) : (
        // For Training and Archive tabs (placeholder data)
        filteredCourses.length > coursesPerPage && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                currentPage > 1
                  ? 'bg-white text-[#0077CC] border border-[#0077CC] hover:bg-[#0077CC] hover:text-white shadow-sm'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <span>Previous</span>
            </button>
            
            <div className="flex items-center gap-2">
              {Array.from({ length: Math.ceil(filteredCourses.length / coursesPerPage) }, (_, i) => i + 1).map((pageNum) => (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`w-10 h-10 rounded-lg transition-all duration-200 ${
                    currentPage === pageNum
                      ? 'bg-[#0077CC] text-white shadow-md'
                      : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                  }`}
                >
                  {pageNum}
                </button>
              ))}
            </div>

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === Math.ceil(filteredCourses.length / coursesPerPage)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                currentPage < Math.ceil(filteredCourses.length / coursesPerPage)
                  ? 'bg-white text-[#0077CC] border border-[#0077CC] hover:bg-[#0077CC] hover:text-white shadow-sm'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <span>Next</span>
            </button>
          </div>
        )
      )}
    </div>
  );
};

export default KnowledgeBase;
