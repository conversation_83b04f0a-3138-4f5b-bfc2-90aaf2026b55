import { useState, useEffect } from 'react';
import { divisions, units } from '../../../data/organizationData';
import type { Unit as OrgUnit } from '../../../data/organizationData';

export const useParticipationData = (timeFilter: string, selectedUnit: string) => {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState({
    summary: {
      participants: {
        total: 0,
        active: 0,
        trend: 0
    },
      completionRate: {
        rate: 0,
        trend: 0
      },
      knowledgeSharing: {
        total: 0,
        trend: 0
    }
    },
    departmentProgress: [],
    contributors: []
  });

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch real data from API endpoints
        const [summaryRes, departmentsRes, contributorsRes] = await Promise.all([
          fetch(`/api/participation/summary?timeframe=${timeFilter}`),
          fetch(`/api/participation/departments?timeframe=${timeFilter}&unit=${selectedUnit}`),
          fetch(`/api/participation/contributors?timeframe=${timeFilter}&unit=${selectedUnit}`)
        ]);

        const [summary, departments, contributors] = await Promise.all([
          summaryRes.json(),
          departmentsRes.json(),
          contributorsRes.json()
        ]);

        setData({
          summary,
          departmentProgress: departments,
          contributors
        });
      } catch (error) {
        console.error('Error fetching participation data:', error);
        // Set empty state on error
        setData({
          summary: {
            participants: { total: 0, active: 0, trend: 0 },
            completionRate: { rate: 0, trend: 0 },
            knowledgeSharing: { total: 0, trend: 0 }
      },
          departmentProgress: [],
          contributors: []
        });
      } finally {
        setIsLoading(false);
    }
  };

    fetchData();
  }, [timeFilter, selectedUnit]);

  return { data, isLoading };
}; 