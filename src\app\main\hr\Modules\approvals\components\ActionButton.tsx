'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { RotateCcw } from 'lucide-react';
import { MaterialStatus } from '@prisma/client';

interface ActionButtonProps {
  materialId: number;
  status: MaterialStatus;
  isCreator?: boolean;
  onStatusChange?: () => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({ 
  materialId, 
  status, 
  isCreator,
  onStatusChange 
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleResubmit = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/training-material/${materialId}/resubmit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ comments: 'Material resubmitted for approval' })
      });

      if (!response.ok) throw new Error('Failed to resubmit material');
      
      onStatusChange?.();
      router.refresh();
    } catch (error) {
      console.error('Error resubmitting material:', error);
      alert('Failed to resubmit material. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (status === MaterialStatus.ARCHIVED && isCreator) {
    return (
      <button
        onClick={handleResubmit}
        disabled={isLoading}
        className="flex items-center gap-1 px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
      >
        <RotateCcw className="w-4 h-4" />
        <span>{isLoading ? 'Resubmitting...' : 'Resubmit'}</span>
      </button>
    );
  }

  return null;
};

export default ActionButton;