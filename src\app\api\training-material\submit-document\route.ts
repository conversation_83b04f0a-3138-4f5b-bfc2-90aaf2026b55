import { writeFile, mkdir } from 'fs/promises';
import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import crypto from 'crypto';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { 
  validateDocumentRequest,
  validateFile,
  FileValidationError 
} from '@/utils/fileValidation';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const userId = parseInt(session.user.id, 10);
    if (isNaN(userId)) {
      return NextResponse.json({ error: 'Invalid user ID format for session.' }, { status: 400 });
    }

    const submitter = await prisma.user.findUnique({
      where: { id: userId },
      select: { firstName: true, lastName: true }
    });
    if (!submitter) {
      return NextResponse.json({ error: 'Submitter not found' }, { status: 404 });
    }
    const submitterName = `${submitter.firstName} ${submitter.lastName}`.trim();

    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const type = formData.get('type') as string | null;
    const materialIdString = formData.get('materialId') as string | null;

    if (!file || !type || !materialIdString) {
      return NextResponse.json(
        { error: 'Missing required fields: file, type, or materialId' },
        { status: 400 }
      );
    }

    // Validate document request data
    const validationResult = validateDocumentRequest({ type, materialId: materialIdString });
    if (!validationResult.success || !validationResult.data) {
      console.error('[API /submit-document] Validation failed:', validationResult.error);
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error
      }, { status: 400 });
    }
    const { type: validatedType, materialId } = validationResult.data;

    // Validate file
    const fileValidationError = validateFile(file, validatedType);
    if (fileValidationError) {
      console.error(`[API /submit-document] File validation failed:`, fileValidationError);
      return NextResponse.json({
        error: fileValidationError.message,
        code: fileValidationError.code
      }, { status: 400 });
    }

    const timestamp = Date.now();
    const randomName = crypto.randomBytes(16).toString('hex');
    const fileExt = path.extname(file.name).toLowerCase();
    const fileName = `${timestamp}_${randomName}${fileExt}`;

    const baseDir = path.join(process.cwd(), 'storage', 'training-materials');
    const materialDir = path.join(baseDir, materialId.toString());
    const typeDirName = validatedType === 'certificate' ? 'certificates' : 'assessment-forms';
    const typeDir = path.join(materialDir, typeDirName);
    const filePath = path.join(typeDir, fileName);
    const fileKey = [
      'training-materials',
      materialId.toString(),
      typeDirName,
      fileName
    ].join('/');

    try {
        await mkdir(materialDir, { recursive: true });
        await mkdir(typeDir, { recursive: true });

        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);
    } catch (writeError) {
        return NextResponse.json({ error: 'Failed to save the uploaded file.' }, { status: 500 });
    }

    await prisma.$transaction(async (tx) => {
      const participantRecord = await tx.trainingParticipant.findUnique({
        where: {
          userId_trainingMaterialId: {
            userId: userId,
            trainingMaterialId: materialId,
          },
        },
      });

      if (!participantRecord) {
        throw new Error('Participant record not found for this user and training material.');
      }

      let updateData = {};
      if (validatedType === 'certificate') {
        updateData = { certificateKey: fileKey };
      } else if (validatedType === 'assessmentForm') {
        updateData = { assessmentFormKey: fileKey };
      }
      await tx.trainingParticipant.update({
        where: {
          userId_trainingMaterialId: {
            userId: userId,
            trainingMaterialId: materialId,
          },
        },
        data: updateData,
      });

      const trainingMaterial = await tx.trainingMaterial.findUnique({
        where: { id: materialId },
        select: { title: true }
      });

      if (!trainingMaterial) {
        throw new Error('Training material not found for notification.');
      }

      const adminUsers = await tx.user.findMany({
        where: {
          role: { 
            is: {
              roleName: { in: ["HR_ADMIN", "ADMIN"] }
            }
          }
        },
        select: { id: true }
      });

      const documentTypeDisplay = validatedType === 'certificate' ? 'Certificate' : 'Assessment Form';
      const notificationMessage = `User ${submitterName} has submitted a/an ${documentTypeDisplay} for the training material "${trainingMaterial.title}".`;
      const notificationType = 'PARTICIPANT_REQUIREMENTS_UPDATED';

      if (adminUsers.length > 0) {
        const notificationsData = adminUsers.map(adminUser => ({
          recipientUserId: adminUser.id,
          message: notificationMessage,
          type: notificationType,
          relatedEntityType: 'TRAINING_MATERIAL',
          relatedEntityId: materialId,
          isRead: false,
        }));
        await tx.systemNotification.createMany({
          data: notificationsData
        });
      }
    });

    return NextResponse.json({
      success: true,
      fileKey,
      message: `${validatedType} uploaded successfully for material ${materialId}.`,
    });

  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
        return NextResponse.json({ error: `Database error occurred (Code: ${error.code}).` }, { status: 500 });
    } else if (error instanceof Error) {
        return NextResponse.json({ error: error.message || 'An unexpected error occurred.' }, { status: 500 });
    }
    
    return NextResponse.json(
      { error: 'Failed to submit document due to an unknown server error.' },
      { status: 500 }
    );
  }
} 