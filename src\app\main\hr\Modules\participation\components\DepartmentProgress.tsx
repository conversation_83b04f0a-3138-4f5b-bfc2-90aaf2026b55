import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { DepartmentProgressItem } from '../types';

// Add color utilities
const divisionColors: { [key: number]: string } = {
  4: 'bg-blue-500',   // Office of the Regional Director
  5: 'bg-green-500',  // Technical Operations
  6: 'bg-purple-500', // Financial and Administrative Services
};

const getDivisionColor = (divisionId: number): string => {
  return divisionColors[divisionId] || 'bg-gray-500';
};

export const DepartmentProgress: React.FC<DepartmentProgressItem> = ({ 
  unit, 
  divisionId, 
  total, 
  current, 
  lastMonth 
}) => {
  const percentage = Math.round((current / total) * 100);
  const trend = percentage - Math.round((lastMonth / total) * 100);
  const divisionColor = getDivisionColor(divisionId).replace('bg-', 'text-');

  return (
    <div className='p-4 hover:bg-gray-50 rounded-lg transition-all duration-200'>
      <div className='flex justify-between text-sm mb-2'>
        <div className='flex items-center gap-2'>
          <div className={`w-2 h-2 rounded-full ${getDivisionColor(divisionId)}`}></div>
          <span className='font-medium text-gray-700'>{unit}</span>
        </div>
        <span className={`font-semibold ${divisionColor}`}>{percentage}%</span>
      </div>
      <div className='flex items-center justify-between text-xs text-gray-500 mb-3'>
        <span>{current} of {total} employees participating</span>
        <div className={`flex items-center gap-1 ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {trend >= 0 ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
          <span>{Math.abs(trend)}%</span>
        </div>
      </div>
      <div className='h-2.5 bg-gray-100 rounded-full overflow-hidden'>
        <div
          className={`h-full rounded-full transition-all duration-500 ease-out ${getDivisionColor(divisionId)}`}
          style={{ width: `${percentage}%`, opacity: 0.8 }}
        />
      </div>
    </div>
  );
}; 