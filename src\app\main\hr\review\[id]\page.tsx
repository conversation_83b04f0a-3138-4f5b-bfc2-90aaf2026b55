'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ReviewData as RawTrainingMaterial, CompetencyLink, CompetencyStatus } from '@/app/main/hr/review/types';
import { ArrowLeft, Clock, FileText, BookOpen, Users, CheckCircle2, XCircle } from 'lucide-react';
import ReviewOverview from '../components/reviewOverview';
import ReviewResources from '../components/reviewResources';
import ReviewParticipants from '../components/reviewParticipants';
import ReviewSubmit from '../components/reviewSubmit';
import Loader from '@/components/ui/Loader';

interface ProcessedTrainingMaterial extends RawTrainingMaterial {
  materialCompetencies: CompetencyLink[];
}

interface ApiMaterialCompetency {
  id: number;
  status: CompetencyStatus;
  competency?: { id: number; competencyName: string };
  temporaryCompetency?: { id: number; competencyName: string };
}

async function getTrainingMaterial(id: string): Promise<RawTrainingMaterial | null> {
  try {
    const response = await fetch(`/api/training-material/${id}`);
    if (!response.ok) {
      return null;
    }
    const data = await response.json();
    return data as RawTrainingMaterial;
  } catch (error) {
    return null;
  }
}

function mapCompetencies(apiCompetencies: ApiMaterialCompetency[]): CompetencyLink[] {
  if (!apiCompetencies) return [];
  return apiCompetencies.map(comp => ({
    id: comp.id,
    name: comp.competency?.competencyName || comp.temporaryCompetency?.competencyName || 'Unknown',
    status: comp.status || 'pending',
    competencyId: comp.competency?.id,
    temporaryCompetencyId: comp.temporaryCompetency?.id,
  }));
}

export default function ReviewTrainingMaterialPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const [material, setMaterial] = useState<ProcessedTrainingMaterial | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [stagedCompetencyActions, setStagedCompetencyActions] = useState<{ [id: number]: CompetencyStatus }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    if (id) {
      setIsLoading(true);
      setError(null);
      getTrainingMaterial(id)
        .then(rawData => {
          if (isMounted && rawData) {
            const processedData: ProcessedTrainingMaterial = {
              ...rawData,
              materialCompetencies: mapCompetencies(rawData.materialCompetencies as unknown as ApiMaterialCompetency[]),
            };
            setMaterial(processedData);
          } else if (isMounted) {
            setError('Failed to load training material details or material not found.');
          }
        })
        .catch(err => {
          if (isMounted) {
            setError('An unexpected error occurred while fetching data.');
          }
        })
        .finally(() => {
          if (isMounted) {
            setIsLoading(false);
          }
        });
    } else {
      if (isMounted) {
        setError('Material ID is missing in the URL.');
        setIsLoading(false);
      }
    }

    return () => { isMounted = false };
  }, [id]);

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, 4));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  const handleStageCompetencyStatusChange = useCallback((materialCompetencyId: number, status: CompetencyStatus) => {
    if (!material) return;

    setStagedCompetencyActions(prev => ({
      ...prev,
      [materialCompetencyId]: status,
    }));

    setMaterial(prevMaterial => {
      if (!prevMaterial) return null;
      return {
        ...prevMaterial,
        materialCompetencies: prevMaterial.materialCompetencies.map(comp =>
          comp.id === materialCompetencyId ? { ...comp, status } : comp
        ),
      };
    });

  }, [material]);

  const handleApprove = async () => {
    if (!material) return;

    setIsLoading(true);
    setError(null);

    // Get IDs of competencies staged for approval
    const approvedCompetencyIds = Object.entries(stagedCompetencyActions)
      .filter(([, status]) => status === 'approved')
      .map(([id]) => parseInt(id));

    // Also get IDs for rejected ones (might be useful for logging or cleanup later)
    const rejectedCompetencyIds = Object.entries(stagedCompetencyActions)
      .filter(([, status]) => status === 'rejected')
      .map(([id]) => parseInt(id));

    try {
      // 1. Approve the main training material, sending approved competency IDs
      const approveMaterialResponse = await fetch(`/api/training-material/${id}/approve`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ approvedCompetencyIds }), // Send IDs in the body
      });

      if (!approveMaterialResponse.ok) {
        const errorData = await approveMaterialResponse.text();
        throw new Error(`Failed to approve training material (${approveMaterialResponse.status}): ${errorData}`);
      }

      setStagedCompetencyActions({});
      router.push('/main/hr/Dashboard?module=approvals');

    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unexpected error occurred during approval.';
      setError(message);
      setIsLoading(false);
    }
  };

  const handleReject = async (reason: string) => {
    if (!material) return;

    setIsLoading(true);
    setError(null);

    try {
      const rejectResponse = await fetch(`/api/training-material/${id}/reject`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rejectionReason: reason }),
      });

      if (!rejectResponse.ok) {
        const errorData = await rejectResponse.text();
        throw new Error(`Failed to reject training material (${rejectResponse.status}): ${errorData}`);
      }

      setMaterial(prev => prev ? { ...prev, status: 'ARCHIVED' } : null);
      setStagedCompetencyActions({});


      setTimeout(() => {
        router.push('/main/hr/Dashboard?module=approvals');
      }, 1500);

    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unexpected error occurred during rejection.';
      setError(message);
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/main/hr/Dashboard?module=approvals');
  };

  if (isLoading && !material) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div>Loading training material details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-lg mx-auto py-8 px-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <p>{error}</p>
            </div>
            <button
              onClick={handleBack}
              className="mt-3 text-sm font-medium text-red-600 hover:text-red-800"
            >
              Back to Approvals
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!material) {
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center">Material not found or could not be loaded.</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-5xl mx-auto py-4 px-4 flex items-center justify-between">
          <button onClick={handleBack} className="flex cursor-pointer items-center text-sm text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back to Approvals
          </button>
          <div className="flex items-center space-x-4">
            <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
               material.status === 'PENDING_APPROVAL' ? 'bg-yellow-100 text-yellow-800' :
               material.status === 'PUBLISHED' ? 'bg-green-100 text-green-800' :
               material.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
               material.status === 'ARCHIVED' ? 'bg-gray-100 text-gray-800' :
               'bg-gray-100 text-gray-800'
            }`}>
                {material.status.replace('_', ' ')}
            </span>
            <div className="flex items-center text-gray-500">
              <Clock className="w-4 h-4 mr-1" />
              <span className="text-sm">Submitted on {new Date(material.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-5xl mx-auto py-8 px-4">

        <div className="bg-white rounded-lg shadow-sm  border-gray-400 p-8 mb-6">
          <div className="flex justify-between items-center relative">
            <div className="absolute left-0 top-[24px] w-full h-0.5 bg-gray-100">
              <div
                className="absolute left-0 top-0 h-full bg-blue-500 transition-all duration-300 ease-in-out"
                style={{ width: `${((currentStep - 1) / 3) * 100}%` }}
              ></div>
            </div>

            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="relative z-10 text-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 transition-colors duration-300 ease-in-out ${currentStep >= step ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-400'}`}>
                  {step === 1 && <BookOpen className="w-6 h-6" />}
                  {step === 2 && <FileText className="w-6 h-6" />}
                  {step === 3 && <Users className="w-6 h-6" />}
                  {step === 4 && <CheckCircle2 className="w-6 h-6" />}
                </div>
                <p className={`text-xs font-medium transition-colors duration-300 ease-in-out ${currentStep >= step ? 'text-blue-600' : 'text-gray-400'}`}>
                  {step === 1 && 'Overview'}
                  {step === 2 && 'Resources'}
                  {step === 3 && 'Participants'}
                  {step === 4 && 'Submit'}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className=" ">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-5">
              <Loader />
              <p className="text-gray-500">Processing your request...</p>
            </div>
          ) : (
            <div className="p-6">
              {currentStep === 1 && (
                <ReviewOverview
                  data={material}
                  nextStep={nextStep}
                  onStatusChange={handleStageCompetencyStatusChange}
                />
              )}
              {currentStep === 2 && (
                <ReviewResources
                  data={material}
                  nextStep={nextStep}
                  prevStep={prevStep}
                />
              )}
              {currentStep === 3 && (
                <ReviewParticipants
                  data={material}
                  nextStep={nextStep}
                  prevStep={prevStep}
                />
              )}
              {currentStep === 4 && (
                <ReviewSubmit
                  data={material}
                  onApprove={handleApprove}
                  onReject={handleReject}
                  prevStep={prevStep}
                  isLoading={isLoading}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}