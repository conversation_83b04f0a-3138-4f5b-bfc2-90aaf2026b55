import React from 'react'
import { Download, UserPlus } from 'lucide-react'
import Loader from '@/components/ui/Loader'

interface UserActionsProps {
  onExport: () => void;
  onAddUser: () => void;
  isLoading: boolean;
}

const UserActions: React.FC<UserActionsProps> = ({ onExport, onAddUser, isLoading }) => {
  return (
    <div className="flex gap-3">
      <button
        onClick={onExport}
        disabled={isLoading}
        className={`flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-200 hover:bg-gray-50 rounded-lg transition-colors ${
          isLoading ? 'cursor-not-allowed opacity-75' : 'cursor-pointer'
        }`}
      >
        {isLoading && <Loader className="h-4 w-4" />}
        <span>{isLoading ? 'Exporting...' : 'Export'}</span>
      </button>
      <button
        onClick={onAddUser}
        disabled={isLoading}
        className="flex items-center gap-2 px-4 py-2 text-white bg-color3 hover:bg-color3/90 rounded-lg transition-colors cursor-pointer"
      >
        <UserPlus size={18} />
        <span>Add User</span>
      </button>
    </div>
  )
}

export default UserActions