export interface FileData {
  name: string;
  file: string;
}

export interface ResourceType {
  id: number;
  resourceType: 'link' | 'file' | string; // Adjust types as needed
  fileKey?: string | null;
  fileType?: string | null;
  fileName?: string | null;
  resourceUrl?: string | null;
  description?: string | null;
  videoType?: string | null;
}

export interface Unit {
  id: number;
  unitName: string; 
  // Add other unit properties if needed
}

export interface TrainingParticipantUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  unitId: number; 
  unit?: Unit | null; // Added relation based on backend include
}

export interface TrainingParticipant {
  id: number;
  userId: number;
  trainingMaterialId: number;
  isCreator: boolean;
  certificateKey: string | null;
  assessmentFormKey: string | null;
  createdAt: string;
  updatedAt: string;
  user: TrainingParticipantUser;
}

export type CompetencyStatus = 'pending' | 'approved' | 'rejected';

export interface CompetencyLink {
  id: number; 
  name: string;
  status: CompetencyStatus; 
  competencyId?: number; 
  temporaryCompetencyId?: number; 
}

export interface ReviewData {
  id: string;
  title: string;
  description: string;
  category: string;
  type: string;
  level: string;
  language: string;
  learningObjectives: string[];
  modules: {
    id: number;
    title: string;
    description: string;
    moduleOrder: number;
    resources: ResourceType[];
  }[];
  resources: {
    name: string;
    file: string;
  }[];
  trainingParticipants: TrainingParticipant[];
  materialCompetencies: CompetencyLink[];
  certifications: {
    name: string;
    file: string;
  }[];
  complianceForms: {
    name: string;
    file: string;
  }[];
  submittedBy: string;
  submittedDate: string;
  status: string;
  department: string;
  priority: string;
  targetAudience: string;
  duration: string;
  dueBy?: string;
  assignedTo?: string;
  createdAt: string;
  updatedAt?: string;
} 