import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { MaterialStatus } from '@prisma/client';
import { Prisma } from '@prisma/client';
import { z } from 'zod';

const requestFileSchema = z.object({
  id: z.string().optional(),
  type: z.enum(['file', 'link']),
  link: z.string().url().optional(),
  description: z.string().optional(),
  videoType: z.string().optional()
});

const requestModuleSchema = z.object({
  title: z.string().min(1, 'Module title is required'),
  files: z.array(requestFileSchema)
});

const createMaterialSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  categoryId: z.number({ required_error: 'Category is required' }),
  modules: z.array(requestModuleSchema).min(1, 'At least one module is required'),
  existingCompetencyIds: z.array(z.number()).optional(),
  newCompetencyNames: z.array(z.string()).optional(),
  participantIds: z.array(z.string()).optional(),
  learningObjectives: z.array(z.string()).optional()
});

type RequestFile = z.infer<typeof requestFileSchema>;
type RequestModule = z.infer<typeof requestModuleSchema>;

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = parseInt(session.user.id.toString());

    let rawBody;
    try {
      rawBody = await request.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const validationResult = createMaterialSchema.safeParse(rawBody);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { 
      title, 
      description, 
      categoryId,
      modules = [],
      existingCompetencyIds = [],
      newCompetencyNames = [],
      participantIds = [],
      learningObjectives = []
    } = validationResult.data;
    
    if (!title) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 });
    }
    
    if (!modules || modules.length === 0) {
      return NextResponse.json({ error: 'At least one module is required' }, { status: 400 });
    }
    
    if (!categoryId) {
      return NextResponse.json({ error: 'Category is required' }, { status: 400 });
    }
    

    const uniqueLinksMap = new Map<string, { description?: string; fileType: string }>();
    
    modules.forEach((module: RequestModule) => {
      module.files.forEach((file: RequestFile) => {
        if (file.type === 'link' && file.link) {
          if (!uniqueLinksMap.has(file.link)) {
            uniqueLinksMap.set(file.link, {
              description: file.description || file.link, 
              fileType: file.videoType === 'youtube' ? 'youtube' : 'link' 
            });
          }
        }
      });
    });

    const uniqueLinkKeys = Array.from(uniqueLinksMap.keys());


    const trainingMaterial = await prisma.$transaction(async (tx) => {

      const resourcePromises = uniqueLinkKeys.map(linkKey => {
        const resourceData = uniqueLinksMap.get(linkKey)!;
        return tx.trainingResource.upsert({
          where: { fileKey: linkKey },
          update: { 
            fileName: resourceData.description, 
            fileType: resourceData.fileType,
            resourceType: 'link'
          },
          create: {
            fileKey: linkKey,
            fileName: resourceData.description,
            fileType: resourceData.fileType,
            resourceType: 'link'
          }
        });
      });
      
      const createdOrFoundResources = await Promise.all(resourcePromises);

      const resourceIdMap = new Map<string, number>();
      createdOrFoundResources.forEach((resource) => {
        if (resource.fileKey) {
          resourceIdMap.set(resource.fileKey, resource.id);
        }
      });
      
      const tempCompetencyPromises = newCompetencyNames.map(async (name: string) => {

        const existingTempComp = await tx.temporaryCompetency.findFirst({
          where: {
            competencyName: name,
            status: 'PENDING'
          }
        });

        if (existingTempComp) {
          return existingTempComp;
        }

        return tx.temporaryCompetency.create({
          data: {
            competencyName: name,
            status: 'PENDING',
            createdByUserId: userId
          }
        });
      });

      const createdOrFoundTempCompetencies = await Promise.all(tempCompetencyPromises);
      const tempCompetencyIds = createdOrFoundTempCompetencies.map((comp: { id: number }) => comp.id);

      const material = await tx.trainingMaterial.create({
        data: {
          title,
          description,
          categoryId,
          status: MaterialStatus.PENDING_APPROVAL,
          objectives: learningObjectives.length > 0 ? {
            create: learningObjectives.map((objectiveText: string, index: number) => ({
              text: objectiveText,
              displayOrder: index + 1
            }))
          } : undefined,
          modules: {
            create: modules.map((module: RequestModule, index: number) => ({
              title: module.title,
              moduleOrder: index + 1,
              resources: {
                connect: module.files
                  .filter((file: RequestFile) => file.type === 'link' && file.link && resourceIdMap.has(file.link))
                  .map((file: RequestFile) => ({ id: resourceIdMap.get(file.link!)! }))
              }
            }))
          },
          materialCompetencies: (existingCompetencyIds.length > 0 || tempCompetencyIds.length > 0) ? {
            create: [
              ...existingCompetencyIds.map((competencyId: number) => ({
                competency: { connect: { id: competencyId } }
              })),
              ...tempCompetencyIds.map((tempId: number) => ({
                temporaryCompetency: { connect: { id: tempId } }
              }))
            ]
          } : undefined,
          trainingParticipants: {
            create: [
              {
                userId,
                isCreator: true
              },
              ...participantIds
                .filter((id: string | number) => id !== userId.toString())
                .map((id: string | number) => ({
                  userId: parseInt(id.toString()),
                  isCreator: false
                }))
            ]
          },
          materialApprovals: {
            create: {
              approverUserId: 2, 
              status: MaterialStatus.PENDING_APPROVAL
            }
          },
          materialStatusLogs: {
            create: {
              changedByUserId: userId,
              oldStatus: null,
              newStatus: MaterialStatus.PENDING_APPROVAL,
              reason: 'Initial submission for approval'
            }
          }
        }
      });
      
      return material;
    });
    
    
    return NextResponse.json({
      success: true,
      message: 'Training material submitted for approval',
      trainingMaterialId: trainingMaterial.id
    });
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
         return NextResponse.json({ error: 'A resource conflict occurred (e.g., duplicate link or competency). Please check input and try again.' }, { status: 409 });
      } 
       return NextResponse.json({ error: `Database error occurred (Code: ${error.code}).` }, { status: 500 });
    } else if (error instanceof z.ZodError) {
        return NextResponse.json({ error: 'Validation error processing request.' }, { status: 400 });
    } else if (error instanceof Error) {
        return NextResponse.json({ error: error.message || 'An unexpected error occurred.' }, { status: 500 });
    }

    return NextResponse.json(
      { error: 'Failed to create training material due to an unknown server error.' },
      { status: 500 }
    );
  }
} 