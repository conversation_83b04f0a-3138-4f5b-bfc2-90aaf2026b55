import React from 'react';
import { X } from 'lucide-react';
import { Log } from '../../services/logsService';
import { colors } from '@/app/constants/colors';

interface LogDetailModalProps {
  log: Log | null;
  onClose: () => void;
}

const LogDetailModal: React.FC<LogDetailModalProps> = ({ log, onClose }) => {
  if (!log) return null;

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return colors.status.success;
      case 'failed':
        return colors.status.error;
      case 'warning':
        return colors.status.warning;
      default:
        return { bg: 'bg-gray-100', text: 'text-gray-800' };
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'login':
        return colors.eventType.login;
      case 'user':
        return colors.eventType.user;
      case 'file':
        return colors.eventType.file;
      case 'security':
        return colors.eventType.security;
      case 'system':
        return colors.eventType.system;
      default:
        return { bg: 'bg-gray-100', text: 'text-gray-800' };
    }
  };

  const statusColors = getStatusColor(log.status);
  const eventTypeColors = getEventTypeColor(log.eventType);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">Log Details</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-500">Event ID</label>
              <p className="mt-1 text-lg font-medium text-gray-900">{log.id}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-500">Timestamp</label>
              <p className="mt-1 text-lg text-gray-900">
                {new Date(log.timestamp).toLocaleString()}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-500">User</label>
              <p className="mt-1 text-lg text-gray-900">{log.user}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-500">IP Address</label>
              <p className="mt-1 text-lg text-gray-900">{log.ipAddress}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-500">Event Type</label>
              <span
                className="mt-1 inline-block px-3 py-1 rounded-full text-sm font-medium"
                style={{
                  backgroundColor: eventTypeColors.bg,
                  color: eventTypeColors.text,
                }}
              >
                {log.eventType}
              </span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-500">Status</label>
              <span
                className="mt-1 inline-block px-3 py-1 rounded-full text-sm font-medium"
                style={{
                  backgroundColor: statusColors.bg,
                  color: statusColors.text,
                }}
              >
                {log.status}
              </span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-500">Description</label>
            <p className="mt-1 text-lg text-gray-900">{log.description}</p>
          </div>

          <div className="mt-8 pt-6 border-t">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(log, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50 rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default LogDetailModal; 