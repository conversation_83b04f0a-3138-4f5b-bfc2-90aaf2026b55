// Consolidated type definitions for the Create Course module

// For Competency Input/Suggestions
export interface CompetencySuggestion {
  id: string | number; // 'new' for pending, number for existing
  name: string;
  sanitizedName?: string; // Optional, might be generated on the fly
  similarity?: number;
  isExisting?: boolean;
  isNew?: boolean;
}

// For Module Resources/Files
export interface FileData {
  id: string; // Unique ID for the file/resource entry
  type: string; // e.g., 'pdf', 'video', 'link'
  title?: string; // User-defined title (might be optional)
  description: string;
  file?: File; // Actual File object (for uploads)
  link?: string; // URL for external links
  videoType?: string; // e.g., 'youtube', 'vimeo'
  videoId?: string;
}

// For Course Modules
export interface Module {
  id: number | string; // Can be a temporary string ID before saving
  title: string;
  isOpen?: boolean; // For UI state
  files: FileData[];
}

// Add export keyword
export interface Category {
  id: number;
  categoryName: string;
}

// For Participants
export interface Participant {
  id: string; // User ID
  name: string;
  email: string;
}

// For the main form data aggregated across steps
export interface FormData {
  title: string;
  description: string;
  category: string; // Name of the category
  categoryId?: number; // ID of the category
  existingCompetencyIds: number[]; // Array of EXISTING competency IDs
  newCompetencyNames: string[]; // Array of NEW competency names
  existingCompetencies: CompetencySuggestion[]; // Full competency data
  learningObjectives: string[];
  modules: Module[];
  participants: Participant[];
  certifications: File[];
  complianceForms: File[];
}

// You might also define interfaces for API payloads if they differ significantly 