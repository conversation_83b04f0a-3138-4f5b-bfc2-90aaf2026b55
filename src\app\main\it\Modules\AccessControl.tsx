"use client"

import React, { useState } from 'react';
import RoleHeader from './components/AccessControl/RoleHeader';
import TabNavigation from './components/AccessControl/TabNavigation';
import RolesList from './components/AccessControl/RolesList';
import EditRoleModal from './components/AccessControl/EditRoleModal';
import PermissionsMatrix from './components/AccessControl/PermissionsMatrix';
import type { Role } from './components/AccessControl/RoleItem';

const AccessControl = () => {
  const [activeTab, setActiveTab] = useState('roles');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  
  // Sample data - in a real app, this would come from an API
  const roles: Role[] = [
    {
      id: '1',
      name: 'IT',
      description: 'Full system access with all technical privileges',
      users: 3,
      permissionLevel: 'Super Admin'
    },
    {
      id: '2',
      name: 'HR',
      description: 'Manage employee records, recruitment, and HR processes',
      users: 8,
      permissionLevel: 'Admin'
    },
    {
      id: '3',
      name: 'Training Unit',
      description: 'Manage training materials, courses, and learning resources',
      users: 12,
      permissionLevel: 'Admin'
    },
    {
      id: '4',
      name: 'Employee',
      description: 'Access and participate in training and company resources',
      users: 150,
      permissionLevel: 'Standard'
    }
  ];

  const handleAddRole = () => {
    setSelectedRole(null);
    setIsEditModalOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setIsEditModalOpen(true);
  };

  const handleSaveRole = (updatedRole: Role) => {
    // In a real app, this would update the role in the backend
    console.log('Save role:', updatedRole);
    setIsEditModalOpen(false);
    setSelectedRole(null);
  };

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 min-h-0 bg-white rounded-lg shadow-sm border border-gray-100">
        <div className="h-full flex flex-col">
          <div className="px-4 sm:px-6 pt-4 sm:pt-6 pb-0">
            <div className="max-w-7xl mx-auto w-full">
              <RoleHeader onAddRole={handleAddRole} />
              <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
            </div>
          </div>

          <div className="flex-1 min-h-0 px-4 sm:px-6 pb-4 sm:pb-6">
            <div className="max-w-7xl mx-auto w-full h-full">
              {activeTab === 'roles' && (
                <div className="h-full overflow-hidden rounded-lg">
                  <div className="h-full overflow-x-auto">
                    <div className="min-w-full inline-block align-middle">
                      <RolesList 
                        roles={roles} 
                        onEditRole={handleEditRole}
                      />
                    </div>
                  </div>
                </div>
              )}
              
              {activeTab === 'permissions' && (
                <div className="h-full">
                  <PermissionsMatrix />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <EditRoleModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedRole(null);
        }}
        role={selectedRole}
        onSave={handleSaveRole}
      />
    </div>
  );
};

export default AccessControl;
