import React from 'react';
import Accordion from '@/components/ui/Accordion';

interface FAQQuestion {
  title: string;
  content: string;
}

const FAQContents: React.FC = () => {
  const Questions1st: FAQQuestion[] = [
    {
      title: 'How do I search for training materials?',
      content: "Access the 'Home' page at the sidebar then you can view it at the upper right corner . By typing keyword or lettersm, a list of results will display"
    },
    {
      title: 'Can I access training materials from other departments?',
      content: 'Yes, Department-specific materials are visible if you are logged in'
    },
    {
      title: 'How do I resume my training from where I left off?',
      content: "The system automatically saves your progress. When you open a training material you've previously started, you'll see a 'Continue Learning' button that takes you to your last position."
    },
    {
      title: 'How can I download shared resources?',
      content: 'When viewing any resource, look for the download icon in the top-right corner of the document viewer. Click it to download the file to your device.'
    },
  ];

  const Questions2nd: FAQQuestion[] = [
    {
      title: 'How do I upload resources to the system',
      content: "Click the '+ Cream New Course' button in the top navigation bar. You can drag and drop files or browse to select them from your device."
    },
    {
      title: '',
      content: ''
    },
    {
      title: '',
      content: ''
    },
    {
      title: '',
      content: ''
    },
  ];

  return (
    <div className='p-10 mx-auto'>
      <div>
        <h1 className='font-semibold text-lg'>Knowledge & Resources</h1>
      </div>
      {Questions1st.map((question, index) => (
        <Accordion key={index} title={question.title} content={question.content} />
      ))}
      <div>
        <div>
          <h1 className='font-semibold text-lg mt-3'>File Upload & Organization</h1>
        </div>
        {Questions2nd.map((question, index) => (
          <Accordion key={index} title={question.title} content={question.content} />
        ))}
      </div>
    </div>
  );
};

export default FAQContents; 