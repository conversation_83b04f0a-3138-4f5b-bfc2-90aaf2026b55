import React from 'react';
import { BookOpenText, TrendingUp, TrendingDown } from 'lucide-react';
import { ContributorCardProps } from '../types';

// Add color utilities
const divisionColors: { [key: number]: string } = {
  4: 'bg-blue-500',   // Office of the Regional Director
  5: 'bg-green-500',  // Technical Operations
  6: 'bg-purple-500', // Financial and Administrative Services
};

const getDivisionColor = (divisionId: number): string => {
  return divisionColors[divisionId] || 'bg-gray-500';
};

export const ContributorCard: React.FC<ContributorCardProps> = ({ 
  name, 
  unit, 
  divisionId, 
  resources, 
  rank, 
  trend
}) => (
  <div className='p-4 hover:bg-gray-50 rounded-lg transition-all duration-200'>
    <div className='flex justify-between items-center'>
      <div className='flex justify-between items-center gap-3 w-full'>
        <div className='relative flex items-center gap-3'>
          <div className={`w-10 h-10 ${getDivisionColor(divisionId)} rounded-full flex items-center justify-center`}>
            <span className='text-sm font-bold text-white'>{rank}</span>
          </div>
          <div>
            <p className='text-sm font-semibold text-gray-800'>{name}</p>
            <div className='flex items-center gap-1.5 mt-0.5'>
              <span className='text-xs text-gray-500'>{unit}</span>
            </div>
          </div>
        </div>
        
        <div className='flex items-center gap-1 text-sm text-color3'>
          <BookOpenText size={12} />
          <span className='font-medium'>{resources} resources</span>
        </div>
        <div className={`flex items-center gap-1 text-xs ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {trend >= 0 ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
          <span>{Math.abs(trend)}%</span>
        </div>
      </div>
    </div>
  </div>
); 