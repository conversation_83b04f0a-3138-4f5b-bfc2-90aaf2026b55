-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "EmployeeType" AS ENUM ('COS', 'PERMANENT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "MaterialStatus" AS ENUM ('DRAFT', 'PENDING_APPROVAL', 'PUBLISHED', 'ARCHIVED');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "ProgressStatus" AS ENUM ('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "PermissionName" AS ENUM ('CREATE_TRAINING_MATERIAL', 'EDIT_OWN_TRAINING_MATERIAL', 'EDIT_ANY_TRAINING_MATERIAL', 'DELETE_OWN_TRAINING_MATERIAL', 'DELETE_ANY_TRAINING_MATERIAL', 'PUBLISH_TRAINING_MATERIAL', 'ARCHIVE_TRAINING_MATERIAL', 'APPROVE_TRAINING_MATERIAL', 'VIEW_PUBLISHED_TRAINING_MATERIAL', 'VIEW_ALL_TRAINING_MATERIAL', 'MANAGE_MODULES', 'UPLOAD_FILESS', 'VIEW_OWN_PROGRESS', 'VIEW_ALL_USER_PROGRESS', 'MANAGE_USERS', 'MANAGE_ROLES_PERMISSIONS', 'TAG_PARTICIPANTS', 'MANAGE_CATEGORIES', 'MANAGE_COMPETENCIES');

-- CreateTable
CREATE TABLE "divisions" (
    "id" SERIAL NOT NULL,
    "divisionName" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "divisions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "units" (
    "id" SERIAL NOT NULL,
    "unitName" TEXT NOT NULL,
    "divisionId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "units_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "roles" (
    "id" SERIAL NOT NULL,
    "roleName" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" SERIAL NOT NULL,
    "unitId" INTEGER NOT NULL,
    "roleId" INTEGER NOT NULL,
    "firstName" VARCHAR(100) NOT NULL,
    "lastName" VARCHAR(100) NOT NULL,
    "password" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "employeeType" "EmployeeType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastLoginAt" TIMESTAMP(3),
    "failedLoginAttempts" INTEGER NOT NULL DEFAULT 0,
    "accountLockedUntil" TIMESTAMP(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "permissions" (
    "id" SERIAL NOT NULL,
    "name" "PermissionName" NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rolePermissions" (
    "id" SERIAL NOT NULL,
    "roleId" INTEGER NOT NULL,
    "permissionId" INTEGER NOT NULL,

    CONSTRAINT "rolePermissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userPermissions" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "permissionId" INTEGER NOT NULL,

    CONSTRAINT "userPermissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userSessions" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "token" TEXT NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "userAgent" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "userSessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "categories" (
    "id" SERIAL NOT NULL,
    "categoryName" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "competencies" (
    "id" SERIAL NOT NULL,
    "competencyName" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "competencies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "training_material" (
    "id" SERIAL NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "categoryId" INTEGER NOT NULL,
    "status" "MaterialStatus" NOT NULL DEFAULT 'DRAFT',
    "publishedAt" TIMESTAMP(3),
    "materialDetails" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "training_material_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "material_competencies" (
    "training_material_id" INTEGER NOT NULL,
    "competency_id" INTEGER NOT NULL,
    "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "material_competencies_pkey" PRIMARY KEY ("training_material_id","competency_id")
);

-- CreateTable
CREATE TABLE "training_participants" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "isCreator" BOOLEAN NOT NULL DEFAULT false,
    "certificateKey" VARCHAR(512),
    "assessmentFormKey" VARCHAR(512),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "training_participants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "modules" (
    "id" SERIAL NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "moduleOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "modules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "moduleFiles" (
    "id" SERIAL NOT NULL,
    "moduleId" INTEGER NOT NULL,
    "fileName" VARCHAR(255) NOT NULL,
    "fileKey" VARCHAR(512) NOT NULL,
    "fileType" VARCHAR(50) NOT NULL,
    "file_size" BIGINT,
    "duration" INTEGER,
    "totalPages" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "moduleFiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userTrainingProgress" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "status" "ProgressStatus" NOT NULL DEFAULT 'NOT_STARTED',
    "progressPercentage" INTEGER NOT NULL DEFAULT 0,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "lastAccessedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "userTrainingProgress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userModuleProgress" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "moduleId" INTEGER NOT NULL,
    "status" "ProgressStatus" NOT NULL DEFAULT 'NOT_STARTED',
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "lastAccessedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "userModuleProgress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userFileProgress" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "moduleFileId" INTEGER NOT NULL,
    "progress" INTEGER NOT NULL DEFAULT 0,
    "lastPage" INTEGER,
    "lastTimestamp" INTEGER,
    "status" "ProgressStatus" NOT NULL DEFAULT 'NOT_STARTED',
    "completedAt" TIMESTAMP(3),
    "lastAccessedAt" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "userFileProgress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userSearches" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "searchQuery" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "userSearches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "auditLogs" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "action" VARCHAR(255) NOT NULL,
    "targetEntity" TEXT,
    "targetId" INTEGER,
    "details" JSONB,
    "ipAddress" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "auditLogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "materialApprovals" (
    "id" SERIAL NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "approverUserId" INTEGER NOT NULL,
    "status" "MaterialStatus" NOT NULL DEFAULT 'PENDING_APPROVAL',
    "comments" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "materialApprovals_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "materialStatusLogs" (
    "id" SERIAL NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "changedByUserId" INTEGER,
    "oldStatus" TEXT,
    "newStatus" TEXT NOT NULL,
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "materialStatusLogs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "systemNotifications" (
    "id" SERIAL NOT NULL,
    "recipientUserId" INTEGER,
    "message" TEXT NOT NULL,
    "type" VARCHAR(100) NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "relatedEntityType" TEXT,
    "relatedEntityId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "systemNotifications_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "divisions_divisionName_key" ON "divisions"("divisionName");

-- CreateIndex
CREATE UNIQUE INDEX "units_unitName_key" ON "units"("unitName");

-- CreateIndex
CREATE UNIQUE INDEX "units_divisionId_unitName_key" ON "units"("divisionId", "unitName");

-- CreateIndex
CREATE UNIQUE INDEX "roles_roleName_key" ON "roles"("roleName");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "permissions_name_key" ON "permissions"("name");

-- CreateIndex
CREATE UNIQUE INDEX "rolePermissions_roleId_permissionId_key" ON "rolePermissions"("roleId", "permissionId");

-- CreateIndex
CREATE UNIQUE INDEX "userPermissions_userId_permissionId_key" ON "userPermissions"("userId", "permissionId");

-- CreateIndex
CREATE UNIQUE INDEX "categories_categoryName_key" ON "categories"("categoryName");

-- CreateIndex
CREATE UNIQUE INDEX "competencies_competencyName_key" ON "competencies"("competencyName");

-- CreateIndex
CREATE UNIQUE INDEX "training_participants_userId_trainingMaterialId_key" ON "training_participants"("userId", "trainingMaterialId");

-- CreateIndex
CREATE UNIQUE INDEX "modules_trainingMaterialId_moduleOrder_key" ON "modules"("trainingMaterialId", "moduleOrder");

-- CreateIndex
CREATE UNIQUE INDEX "moduleFiles_fileKey_key" ON "moduleFiles"("fileKey");

-- CreateIndex
CREATE UNIQUE INDEX "userTrainingProgress_userId_trainingMaterialId_key" ON "userTrainingProgress"("userId", "trainingMaterialId");

-- CreateIndex
CREATE UNIQUE INDEX "userModuleProgress_userId_moduleId_key" ON "userModuleProgress"("userId", "moduleId");

-- CreateIndex
CREATE UNIQUE INDEX "userFileProgress_userId_moduleFileId_key" ON "userFileProgress"("userId", "moduleFileId");

-- AddForeignKey
ALTER TABLE "units" ADD CONSTRAINT "units_divisionId_fkey" FOREIGN KEY ("divisionId") REFERENCES "divisions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "units"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rolePermissions" ADD CONSTRAINT "rolePermissions_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rolePermissions" ADD CONSTRAINT "rolePermissions_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userPermissions" ADD CONSTRAINT "userPermissions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userPermissions" ADD CONSTRAINT "userPermissions_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userSessions" ADD CONSTRAINT "userSessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "training_material" ADD CONSTRAINT "training_material_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "material_competencies" ADD CONSTRAINT "material_competencies_training_material_id_fkey" FOREIGN KEY ("training_material_id") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "material_competencies" ADD CONSTRAINT "material_competencies_competency_id_fkey" FOREIGN KEY ("competency_id") REFERENCES "competencies"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "training_participants" ADD CONSTRAINT "training_participants_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "training_participants" ADD CONSTRAINT "training_participants_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "modules" ADD CONSTRAINT "modules_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "moduleFiles" ADD CONSTRAINT "moduleFiles_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "modules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userTrainingProgress" ADD CONSTRAINT "userTrainingProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userTrainingProgress" ADD CONSTRAINT "userTrainingProgress_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userModuleProgress" ADD CONSTRAINT "userModuleProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userModuleProgress" ADD CONSTRAINT "userModuleProgress_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "modules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userFileProgress" ADD CONSTRAINT "userFileProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userFileProgress" ADD CONSTRAINT "userFileProgress_moduleFileId_fkey" FOREIGN KEY ("moduleFileId") REFERENCES "moduleFiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userSearches" ADD CONSTRAINT "userSearches_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auditLogs" ADD CONSTRAINT "auditLogs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "materialApprovals" ADD CONSTRAINT "materialApprovals_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "materialApprovals" ADD CONSTRAINT "materialApprovals_approverUserId_fkey" FOREIGN KEY ("approverUserId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "materialStatusLogs" ADD CONSTRAINT "materialStatusLogs_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "materialStatusLogs" ADD CONSTRAINT "materialStatusLogs_changedByUserId_fkey" FOREIGN KEY ("changedByUserId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "systemNotifications" ADD CONSTRAINT "systemNotifications_recipientUserId_fkey" FOREIGN KEY ("recipientUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
