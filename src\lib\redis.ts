import Redis from 'ioredis';

// Check if Red<PERSON> is configured in the environment
const isRedisConfigured = process.env.REDIS_URL !== undefined;

// Configure Redis client with proper error handling
const getRedisClient = () => {
  if (!isRedisConfigured) {
    console.warn('Redis is not configured. Using mock implementation.');
    return getMockRedisClient();
  }

  const options = {
    // Required environment variable
    host: process.env.REDIS_URL || 'localhost',
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : 6379,
    
    // Optional configuration
    tls: process.env.REDIS_TLS_ENABLED === 'true' ? {} : undefined,
    username: process.env.REDIS_USERNAME || undefined,
    password: process.env.REDIS_PASSWORD || undefined,
    retryStrategy: (times: number) => Math.min(times * 50, 2000),
  };

  const redis = new Redis(options);

  redis.on('error', (err) => {
    console.error('Redis connection error:', err);
  });

  redis.on('connect', () => {
  
  });

  return redis;
};

// Mock implementation for when Redis is not configured
const getMockRedisClient = () => {
  const storage = new Map<string, string>();
  
  return {
    set: async (key: string, value: string, ...args: any[]): Promise<any> => {
      storage.set(key, value);
      return 'OK';
    },
    setex: async (key: string, ttl: number, value: string): Promise<any> => {
      storage.set(key, value);
      return 'OK';
    },
    get: async (key: string): Promise<string | null> => {
      return storage.get(key) || null;
    },
    del: async (key: string | string[]): Promise<number> => {
      if (Array.isArray(key)) {
        let count = 0;
        for (const k of key) {
          if (storage.delete(k)) count++;
        }
        return count;
      }
      return storage.delete(key) ? 1 : 0;
    },
    scan: async (...args: any[]): Promise<[string, string[]]> => {
      return ['0', Array.from(storage.keys())];
    },
    on: (event: string, callback: (...args: any[]) => void) => {
      return { event, callback }; // Just return something for typings
    },
    // Add other methods as needed to mock Redis functionality
  } as unknown as Redis;
};

// Create a singleton instance
const redis = getRedisClient();

export default redis; 