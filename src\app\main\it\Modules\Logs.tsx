"use client"

import React, { useState } from 'react';
import { Download, RotateCw, Search, Calendar, AlertCircle } from 'lucide-react';
import LogsTable from './components/LogsComponents/LogsTable';
import { useLogsManagement } from '@/app/main/it/Modules/hooks/useLogsManagement';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { colors } from '@/app/constants/colors';

// Mock data for initial development
const mockLogs = [
  {
    id: 'AUD-10458',
    timestamp: '2023-10-15 14:32:45',
    user: '<EMAIL>',
    eventType: 'Login',
    ipAddress: '************',
    description: 'User login successful',
    status: 'Success'
  },
  {
    id: 'AUD-10457',
    timestamp: '2023-10-15 14:30:12',
    user: 'unknown',
    eventType: 'Login',
    ipAddress: '************',
    description: 'Failed login attempt for user admin',
    status: 'Failed'
  },
  {
    id: 'AUD-10456',
    timestamp: '2023-10-15 14:15:33',
    user: '<EMAIL>',
    eventType: 'User',
    ipAddress: '************',
    description: 'User account created: <EMAIL>',
    status: 'Success'
  },
  {
    id: 'AUD-10455',
    timestamp: '2023-10-15 13:45:21',
    user: '<EMAIL>',
    eventType: 'File',
    ipAddress: '************',
    description: 'File uploaded: Marketing Campaign.pptx',
    status: 'Success'
  },
  {
    id: 'AUD-10454',
    timestamp: '2023-10-15 13:22:05',
    user: 'system',
    eventType: 'Security',
    ipAddress: '***********',
    description: 'Suspicious file upload detected and quarantined',
    status: 'Warning'
  },
  {
    id: 'AUD-10453',
    timestamp: '2023-10-15 12:58:42',
    user: '<EMAIL>',
    eventType: 'System',
    ipAddress: '************',
    description: 'System settings updated: Password policy',
    status: 'Success'
  },
  {
    id: 'AUD-10452',
    timestamp: '2023-10-15 12:45:18',
    user: '<EMAIL>',
    eventType: 'File',
    ipAddress: '************',
    description: 'File deleted: Old Project Proposal.docx',
    status: 'Success'
  }
];

// Add custom styles for DatePicker
const datePickerStyles = `
  .react-datepicker__input-container input {
    padding: 0.5rem 1rem;
    border: 1px solid #E5E7EB;
    border-radius: 0.5rem;
    width: 100%;
    outline: none;
  }
  .react-datepicker__input-container input:focus {
    border-color: ${colors.primary.color3};
    box-shadow: 0 0 0 2px ${colors.primary.color3}33;
  }
`;

const Logs = () => {
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [eventTypeFilter, setEventTypeFilter] = useState('All Events');
  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isRotating, setIsRotating] = useState(false);

  const {
    logs,
    totalItems,
    totalPages,
    currentPage,
    setCurrentPage,
    isLoading,
    error,
    refreshLogs,
    exportLogs,
  } = useLogsManagement({
    itemsPerPage,
    searchQuery,
    eventTypeFilter,
    startDate: startDate?.toISOString(),
    endDate: endDate?.toISOString(),
    statusFilter,
  });

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleDateChange = (date: Date | null, isStart: boolean) => {
    if (isStart) {
      setStartDate(date);
    } else {
      setEndDate(date);
    }
    setCurrentPage(1);
  };

  const handleRefreshClick = async () => {
    if (isLoading || isRotating) return;
    
    setIsRotating(true);
    await refreshLogs();
    
    // Keep rotating for 3 seconds after refresh starts
    setTimeout(() => {
      setIsRotating(false);
    }, 3000);
  };

  return (
    <div className="w-full space-y-6">
      <style>{datePickerStyles}</style>
      <div>
        <h1 className="text-2xl font-bold text-gray-800">Audit Logs</h1>
        <p className="text-gray-500">View and analyze system activity logs for security and compliance.</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2 text-red-700">
          <AlertCircle className="w-5 h-5" />
          <span>{error.message}</span>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">System Activity Logs</h2>
              <p className="text-sm text-gray-500">Comprehensive logs of all system activities and events</p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={exportLogs}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download className="w-4 h-4" />
                Export
              </button>
              <button
                onClick={handleRefreshClick}
                disabled={isLoading || isRotating}
               
                className={`group flex items-center gap-2 px-4 py-2 text-white rounded-lg bg-color3 hover:bg-color3/90 active:bg-color3/80 transition-all duration-300 ${
                  (isLoading || isRotating) ? 'opacity-75 cursor-not-allowed' : ''
                }`}
              >
                <span className={`transform transition-transform duration-700 ${isLoading || isRotating ? 'animate-spin' : ''}`}>
                  <RotateCw className="w-4 h-4" />
                </span>
                {isLoading ? 'Refreshing...' : isRotating ? 'Refreshing...' : 'Refresh'}
              </button>
            </div>
          </div>

          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search logs..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                style={{
                  '--tw-ring-color': colors.primary.color3,
                } as React.CSSProperties}
                value={searchQuery}
                onChange={handleSearch}
              />
            </div>

            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-gray-400" />
                <DatePicker
                  selected={startDate}
                  onChange={(date: Date | null) => handleDateChange(date, true)}
                  selectsStart
                  startDate={startDate}
                  endDate={endDate}
                  placeholderText="Start Date"
                  className="w-full"
                />
                <DatePicker
                  selected={endDate}
                  onChange={(date: Date | null) => handleDateChange(date, false)}
                  selectsEnd
                  startDate={startDate}
                  endDate={endDate}
                  minDate={startDate || undefined}
                  placeholderText="End Date"
                  className="w-full"
                />
              </div>

              <select
                value={eventTypeFilter}
                onChange={(e) => {
                  setEventTypeFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                style={{
                  '--tw-ring-color': colors.primary.color3,
                } as React.CSSProperties}
              >
                <option>All Events</option>
                <option>Login</option>
                <option>User</option>
                <option>File</option>
                <option>Security</option>
                <option>System</option>
              </select>

              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                style={{
                  '--tw-ring-color': colors.primary.color3,
                } as React.CSSProperties}
              >
                <option value="">All Status</option>
                <option value="Success">Success</option>
                <option value="Failed">Failed</option>
                <option value="Warning">Warning</option>
              </select>

              <select
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                style={{
                  '--tw-ring-color': colors.primary.color3,
                } as React.CSSProperties}
              >
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
                <option value={100}>100 per page</option>
              </select>
            </div>
          </div>

          <LogsTable
            logs={logs}
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={setCurrentPage}
            isLoading={isLoading}
            searchQuery={searchQuery}
          />
        </div>
      </div>
    </div>
  );
};

export default Logs;
