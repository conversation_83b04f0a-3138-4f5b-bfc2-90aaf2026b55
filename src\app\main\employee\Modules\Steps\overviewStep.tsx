"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/Button"
import MultiInput from "@/components/ui/multiInput"
import { useState, useEffect, useCallback } from "react"
import { toast, Toaster } from "sonner"
import CompetencyInput from './CompetencyInput'
import { FormData, CompetencySuggestion, Category } from '../types'
import { debounce } from 'lodash'
import Loader from '@/components/ui/Loader'

// Predefined options
const EXAMPLE_OBJECTIVE = "Example: Understand the fundamentals of React and its core concepts"

interface OverviewStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
}

// Function to sanitize competency name
const sanitizeCompetencyName = (name: string): string => {
  return name.toLowerCase().replace(/[^\w\s]/g, '').trim();
};

export default function OverviewStep({ formData, updateFormData, nextStep }: OverviewStepProps) {
  // Local state for competencies
  const [selectedTags, setSelectedTags] = useState<CompetencySuggestion[]>([])
  // Local state for objectives
  const [localObjectives, setLocalObjectives] = useState<string[]>([])
  const [isCategoryLoading, setIsCategoryLoading] = useState(true)
  const [categories, setCategories] = useState<Category[]>([])
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  // Add this flag to track manual removals
  const [tagJustRemoved, setTagJustRemoved] = useState(false)
  
  // Debounced update function for competencies only
  const debouncedUpdateCompetencies = useCallback(
    debounce((data: Partial<FormData>) => {
      updateFormData(data)
    }, 300),
    [updateFormData]
  )

  // Initialize local objectives from formData on mount
  useEffect(() => {
    if (formData.learningObjectives && localObjectives.length === 0) {
      setLocalObjectives(formData.learningObjectives)
    }
  }, [formData.learningObjectives, localObjectives.length])

  // Initialize competencies from formData on mount
  useEffect(() => {
    if (formData.existingCompetencies && formData.newCompetencyNames && selectedTags.length === 0) {
      const existingTags = formData.existingCompetencies
      const newTags = formData.newCompetencyNames.map(name => ({
        id: 'new',
        name,
        isNew: true
      }))
      setSelectedTags([...existingTags, ...newTags])
    }
  }, [formData.existingCompetencies, formData.newCompetencyNames, selectedTags.length])

  // Effect to synchronize formData.competencies with local selectedTags state
  useEffect(() => {
    // Skip if a tag was just manually removed, as that's handled by the other effect
    if (tagJustRemoved) return;

    const existingIds = selectedTags
      .filter(t => t.id !== 'new')
      .map(t => t.id as number)
    
    const newNames = selectedTags
      .filter(t => t.id === 'new')
      .map(t => t.name)

    const existingCompetencies = selectedTags.filter(t => t.id !== 'new')

    // Update if values have changed, including empty state
    if (
      JSON.stringify(existingIds) !== JSON.stringify(formData.existingCompetencyIds) ||
      JSON.stringify(newNames) !== JSON.stringify(formData.newCompetencyNames)
    ) {
      debouncedUpdateCompetencies({ 
        existingCompetencyIds: existingIds, 
        newCompetencyNames: newNames,
        existingCompetencies
      })
    }
  }, [selectedTags, formData.existingCompetencyIds, formData.newCompetencyNames, debouncedUpdateCompetencies, tagJustRemoved])

  // Effect to synchronize formData.learningObjectives with local localObjectives state
  useEffect(() => {
    // Only update if values have changed
    if (JSON.stringify(localObjectives) !== JSON.stringify(formData.learningObjectives)) {
      updateFormData({ learningObjectives: localObjectives })
    }
  }, [localObjectives, formData.learningObjectives, updateFormData])

  // Separate effect for loading categories
  useEffect(() => {
    let isMounted = true;
    
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await response.json();
        if (isMounted) {
          setCategories(data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        if (isMounted) {
          toast.error('Failed to load categories');
        }
      } finally {
         if (isMounted) {
            setIsCategoryLoading(false);
         }
      }
    };

    fetchCategories();
    
    return () => {
      isMounted = false;
    };
  }, []);

  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.title.trim()) {
      errors.title = 'Title is required'
    } else if (formData.title.length < 5) {
      errors.title = 'Title must be at least 5 characters'
    }
    
    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    } else if (formData.description.length < 20) {
      errors.description = 'Description must be at least 20 characters'
    }
    
    if (!formData.category) {
      errors.category = 'Category is required'
    }
    
    if (selectedTags.length === 0) {
      errors.competencies = 'At least one competency is required'
    }
    
    if (localObjectives.length === 0) {
      errors.objectives = 'At least one learning objective is required'
    } else if (localObjectives.some(obj => !obj.trim())) {
      errors.objectives = 'All objectives must be filled out or removed'
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form. Make sure all objectives are filled out or removed.')
      return
    }
    
    nextStep()
  }

  const handleBasicInfoChange = useCallback((field: keyof FormData, value: string | string[]) => {
    // Clear validation error for the field being changed
    setValidationErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field]
      return newErrors
    })

    if (field === 'category' && typeof value === 'string') {
      const selectedCategory = categories.find(cat => cat.categoryName === value)
      updateFormData({ 
        category: value,
        categoryId: selectedCategory ? selectedCategory.id : undefined
      })
    } else if (field === 'learningObjectives' && Array.isArray(value)) {
      setLocalObjectives(value)
    } else if (typeof value === 'string' && (field === 'title' || field === 'description')) {
      updateFormData({ [field]: value })
    }
  }, [categories, updateFormData])

  const handleAddTag = useCallback((tagToAdd: CompetencySuggestion) => {
    const competencyName = tagToAdd.name;
    const sanitizedSelectedName = sanitizeCompetencyName(competencyName);
    
    if (selectedTags.some(t => sanitizeCompetencyName(t.name) === sanitizedSelectedName)) {
      return;
    }
    setSelectedTags(prev => [...prev, tagToAdd]);
  }, []);

  const handleTagRemove = useCallback((tagNameToRemove: string) => {
    setSelectedTags(prev => {
      const newTags = prev.filter(t => t.name !== tagNameToRemove);
      // Immediately update form data when removing the last tag
      if (newTags.length === 0) {
        updateFormData({
          existingCompetencyIds: [],
          newCompetencyNames: [],
          existingCompetencies: []
        });
      }
      return newTags;
    });
    setTagJustRemoved(true);
  }, [updateFormData]);

  if (isCategoryLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader />
        <span className="ml-2">Loading setup...</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit}>
      <Toaster position="top-right" />
      {/* Overview and Details Section */}
      <div className="space-y-8">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
            <span className="flex items-center justify-center w-8 h-8 rounded-full bg-indigo-100 text-base">1</span>
            Overview & Details
          </h2>
          <p className="mt-2 text-sm text-gray-500">
            Provide an overview and descriptive details about the training materials.
          </p>
        </div>

        <div className="space-y-6">
          {/* Basic Information Section */}
          <div className="bg-gray-50 p-8 rounded-lg space-y-6">
            <h3 className="font-medium text-lg text-gray-900">Basic Course Information</h3>
            
            <div className="space-y-2">
              <Label htmlFor="title" className="block">Course Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleBasicInfoChange('title', e.target.value)}
                placeholder="Enter course title"
                required
                className={`w-full bg-white ${validationErrors.title ? 'border-red-500' : ''}`}
              />
              {validationErrors.title && (
                <p className="text-sm text-red-500">{validationErrors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="block">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleBasicInfoChange('description', e.target.value)}
                placeholder="Provide a detailed description of the training materials"
                required
                className={`w-full bg-white h-40 ${validationErrors.description ? 'border-red-500' : ''}`}
              />
              {validationErrors.description && (
                <p className="text-sm text-red-500">{validationErrors.description}</p>
              )}
            </div>

            {/* Category Section */}
            <div className="space-y-3">
              <Label htmlFor="category" className="block">Category</Label>
              <select
                id="category"
                value={formData.category}
                onChange={(e) => handleBasicInfoChange('category', e.target.value)}
                className={`w-full p-2 border ${validationErrors.category ? 'border-red-500' : 'border-black'} text-gray-500 rounded-md bg-white`}
                required
                disabled={isCategoryLoading}
              >
                <option value="" disabled>Select category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.categoryName}>
                    {category.categoryName}
                  </option>
                ))}
              </select>
              {validationErrors.category && (
                <p className="text-sm text-red-500">{validationErrors.category}</p>
              )}
              <div>
                <p className="ml-2 text-xs text-gray-500">Note: Select the category that best describes the training materials.</p>
              </div>
            </div>

            <div className="space-y-2">
              <CompetencyInput 
                selectedTags={selectedTags}
                onTagAdd={handleAddTag}
                onTagRemove={handleTagRemove}
                error={validationErrors.competencies}
              />
            </div>
          </div>

          {/* Learning Objectives and Prerequisites Section */}
          <div className="w-full">
            {/* Learning Objectives */}
            <div className="bg-gray-50 p-8 rounded-lg space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-lg font-medium text-gray-900">Learning Objectives</Label>
                </div>
                
                {/* Example section */}
                <div className="bg-blue-50 border border-blue-100 rounded-md p-4">
                  <div className="flex items-start gap-3">
                    <div className="p-1">
                      <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-900">Example Objective</p>
                      <p className="text-sm text-blue-700">{EXAMPLE_OBJECTIVE}</p>
                    </div>
                  </div>
                </div>
                {/* Input section */}
                <div className="space-y-2">
                  <MultiInput
                    values={localObjectives}
                    onChange={(values) => handleBasicInfoChange('learningObjectives', values)}
                    placeholder="Type an objective and press Enter..."
                    required
                    error={validationErrors.objectives}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <Button className="bg-color3 cursor-pointer text-white px-8 py-2.5 rounded-md hover:bg-color3/95 text-base" type="submit">
            Continue
          </Button>
        </div>
      </div>
    </form>
  )
}
