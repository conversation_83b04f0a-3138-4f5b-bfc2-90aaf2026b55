import { z } from 'zod';

// Type definitions for progress tracking
export interface MaterialProgressData {
  userId: string;
  courseId: string;
  moduleId: string;
  stepId: string;
  materialId: string;
  materialName: string;
  materialType: 'pdf' | 'youtube' | 'video' | 'document' | 'text' | 'quiz' | string;
  progress: number;
  lastPosition?: number;
  isViewed: boolean;
  lastUpdated?: number;
}

export interface StepProgressData {
  userId: string;
  courseId: string;
  moduleId: string;
  stepId: string;
  isCompleted: boolean;
  lastUpdated?: number;
}

export interface ModuleProgressData {
  userId: string;
  courseId: string;
  moduleId: string;
  isCompleted: boolean;
  lastUpdated?: number;
}

export interface CourseProgressData {
  userId: string;
  courseId: string;
  totalSteps: number;
  completedSteps: number;
  progress: number;
  isCompleted: boolean;
  lastUpdated?: number;
}

// Throttle function to prevent excessive API calls
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let inThrottle = false;
  let lastResult: any;

  return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    if (!inThrottle) {
      inThrottle = true;
      lastResult = await func(...args);
      setTimeout(() => (inThrottle = false), limit);
    }
    return lastResult;
  };
}

// Helper function to retry failed operations
const retry = async <T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 1) throw error;
    console.log(`Operation failed, retrying in ${delay}ms... (${retries-1} retries left)`);
    await new Promise(resolve => setTimeout(resolve, delay));
    return retry(fn, retries - 1, delay);
  }
};

// API functions for progress tracking
export const cacheProgress = async <T>(type: 'material' | 'step' | 'module' | 'course', data: T): Promise<boolean> => {
  try {
    if (type === 'material') {
      const materialData = data as unknown as MaterialProgressData;
      console.log(`1.) Viewing Current Material ID: ${materialData.materialId} Name: ${materialData.materialName}`);
      console.log(`2.) Checking Database for existing data`);

      // Special handling for image files
      const isImageFile = ['jpg', 'jpeg', 'png'].includes(materialData.materialType);
      if (isImageFile) {
        console.log(`Special handling for image file in cacheProgress: ${materialData.materialName} [${materialData.materialType}]`);
        // Ensure isViewed is set to true for image files
        materialData.isViewed = true;
        materialData.progress = 100;
      }
    }

    // Add timestamp to ensure data is fresh
    const timestamp = Date.now();

    // Determine if this is an image file (for special handling)
    const isImageFile = type === 'material' && ['jpg', 'jpeg', 'png'].includes((data as any).materialType);
    
    // Use retry mechanism for image files to ensure persistence
    const fetchFn = async () => {
      const response = await fetch('/api/progress/cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          data: {
            ...data,
            lastUpdated: timestamp,
          },
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Cache response error:', errorData);
        throw new Error('Failed to cache progress');
      }
      
      return response;
    };
    
    // Execute the fetch with retry for images
    const response = isImageFile 
      ? await retry(fetchFn, 3, 500)  // Retry up to 3 times with 500ms delay for images
      : await fetchFn();              // No retry for other material types

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Failed to cache progress:', errorData);
      return false;
    }

    if (type === 'material') {
      const materialData = data as unknown as MaterialProgressData;
      console.log(`4.) Caching progress for Material ID: ${materialData.materialId} Type: ${materialData.materialType}`);

      // For image files, log additional information
      if (['jpg', 'jpeg', 'png'].includes(materialData.materialType)) {
        console.log(`Image file progress cached - ID: ${materialData.materialId}, Progress: ${materialData.progress}%, isViewed: ${materialData.isViewed}`);
      }
    }

    return true;
  } catch (error) {
    console.error('Error caching progress:', error);
    return false;
  }
};

// Throttled version to prevent excessive API calls
export const throttledCacheProgress = throttle(cacheProgress, 2000);

// Function to track material progress
export const trackMaterialProgress = async (data: MaterialProgressData): Promise<boolean> => {
  // Special case: Bypass throttling for image files to ensure they're always cached
  if (['jpg', 'jpeg', 'png', 'gif'].includes(data.materialType)) {
    console.log(`Bypassing throttle for image file: ${data.materialName} [${data.materialType}]`);
    return cacheProgress('material', data);
  }
  return throttledCacheProgress('material', data);
};

// Function to track step completion
export const trackStepCompletion = async (data: StepProgressData): Promise<boolean> => {
  return throttledCacheProgress('step', data);
};

// Function to track module completion
export const trackModuleCompletion = async (data: ModuleProgressData): Promise<boolean> => {
  return throttledCacheProgress('module', data);
};

// Function to track course progress
export const trackCourseProgress = async (data: CourseProgressData): Promise<boolean> => {
  return throttledCacheProgress('course', data);
};

// Function to calculate PDF progress
export const calculatePdfProgress = (currentPage: number, totalPages: number): number => {
  if (totalPages <= 0) return 0;
  return Math.min(Math.round((currentPage / totalPages) * 100), 100);
};

// Function to persist all cached progress to the database
export const persistProgress = async (
  options: {
    type?: 'material' | 'all';
    userId?: string;
    courseId?: string;
    materialType?: string; // Added to track specific material types
    isImageFile?: boolean; // Flag to indicate if this is for an image file
  } = {}
): Promise<{
  success: boolean;
  total: number;
  persisted: number;
  failed: number;
} | null> => {
  try {
    console.log(`5.) Persisting Data to db - Type: ${options.type || 'all'}, CourseId: ${options.courseId || 'all'}, MaterialType: ${options.materialType || 'all'}, IsImage: ${options.isImageFile ? 'true' : 'false'}`);

    // For image files, add a small delay to ensure caching has completed
    if (options.isImageFile) {
      console.log('Adding additional delay for image file persistence to ensure caching has completed');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Add a timestamp to ensure the request is unique
    const requestOptions = {
      ...options,
      timestamp: Date.now()
    };

    const response = await fetch('/api/progress/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestOptions),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Failed to persist progress:', errorData);
      return null;
    }

    const result = await response.json();
    console.log(`Persistence complete - Total: ${result.total}, Persisted: ${result.persisted}, Failed: ${result.failed}`);

    // If no items were persisted, log a warning
    if (result.total === 0 || result.persisted === 0) {
      console.warn(`No items were persisted. This could indicate that no items were found in Redis or that the Redis keys don't match the expected format.`);
    }

    return result;
  } catch (error) {
    console.error('Error persisting progress:', error);
    return null;
  }
};