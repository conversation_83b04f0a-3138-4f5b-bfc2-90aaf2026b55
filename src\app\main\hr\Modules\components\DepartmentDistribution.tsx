import React, { useState, useEffect } from 'react';
import { Bar, Radar } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { Users, Gauge } from 'lucide-react';

interface DepartmentDistributionProps {
  className?: string;
  timeframe?: string;
}

interface DepartmentData {
  name: string;
  headcount: number;
  turnover: number;
  distribution: number;
}

interface DepartmentMetricsData {
  departments: DepartmentData[];
  totalHeadcount: number;
  averageTurnover: number;
}

const DepartmentDistribution: React.FC<DepartmentDistributionProps> = ({ 
  className,
  timeframe = 'This Week'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<DepartmentMetricsData | null>(null);

  useEffect(() => {
    const fetchDepartmentData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/hr-analytics/departments');
        if (!response.ok) throw new Error('Failed to fetch department data');
        const departmentData = await response.json();
        setData(departmentData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching department data');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDepartmentData();
  }, [timeframe]);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className} flex justify-center items-center min-h-[400px]`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className} flex flex-col justify-center items-center min-h-[400px]`}>
        <Users className="w-12 h-12 text-red-500 mb-4" />
        <p className="text-red-500 text-center">
          {error || 'No department data available'}
        </p>
      </div>
    );
  }

  const headcountData: ChartData<'bar'> = {
    labels: data.departments.map(dept => dept.name),
    datasets: [{
      label: 'Headcount',
      data: data.departments.map(dept => dept.headcount),
      backgroundColor: '#3B82F6',
      borderColor: '#2563EB',
      borderWidth: 1
    }]
  };

  const turnoverData: ChartData<'bar'> = {
    labels: data.departments.map(dept => dept.name),
    datasets: [{
      label: 'Turnover Rate',
      data: data.departments.map(dept => dept.turnover),
      backgroundColor: '#F59E0B',
      borderColor: '#D97706',
      borderWidth: 1
    }]
  };

  const distributionData: ChartData<'radar'> = {
    labels: data.departments.map(dept => dept.name),
    datasets: [{
      label: 'Distribution',
      data: data.departments.map(dept => dept.distribution),
      backgroundColor: 'rgba(99, 102, 241, 0.2)',
      borderColor: '#6366F1',
      borderWidth: 2,
      pointBackgroundColor: '#6366F1'
    }]
  };

  const barOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true
      }
    },
    plugins: {
      legend: {
        display: false
      }
    }
  };

  const radarOptions: ChartOptions<'radar'> = {
    responsive: true,
    scales: {
      r: {
        beginAtZero: true,
        ticks: {
          stepSize: 20
        }
      }
    },
    plugins: {
      legend: {
        display: false
      }
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-800">Department Distribution</h2>
          <p className="text-sm text-gray-500">Workforce distribution across departments</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-800">{data.totalHeadcount}</div>
          <div className="text-sm text-gray-500">Total Employees</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Department Headcount</h3>
          <div className="h-[300px]">
            <Bar data={headcountData} options={barOptions} />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Turnover Rate</h3>
          <div className="h-[300px]">
            <Bar data={turnoverData} options={barOptions} />
          </div>
        </div>

        <div className="lg:col-span-2">
          <h3 className="text-sm font-medium text-gray-700 mb-4">Workforce Distribution</h3>
          <div className="h-[400px]">
            <Radar data={distributionData} options={radarOptions} />
          </div>
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-sm font-medium text-gray-700 mb-4">Department Overview</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Headcount</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Distribution</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Turnover Rate</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.departments.map((dept, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{dept.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{dept.headcount}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{dept.distribution}%</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{dept.turnover}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DepartmentDistribution; 