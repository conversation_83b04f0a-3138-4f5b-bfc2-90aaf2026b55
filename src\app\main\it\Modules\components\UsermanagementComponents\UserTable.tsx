import React, { useState, useRef, useEffect } from 'react'
import { MoreHorizontal } from 'lucide-react'
import UserDropdown from './UserDropdown'
import EditUserModal from './EditUserModal'
import HighlightedText from './HighlightedText'

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  department: string;
  status: string;
  lastLogin: string;
}

interface UserTableProps {
  users: User[];
  onUserAction: (userId: string, action: string) => void;
  searchQuery: string;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalItems: number;
  onPageChange: (page: number) => void;
}

const UserTable: React.FC<UserTableProps> = ({ 
  users, 
  onUserAction, 
  searchQuery,
  currentPage,
  totalPages,
  itemsPerPage,
  totalItems,
  onPageChange
}) => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const dropdownButtonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'away':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'it admin':
        return 'bg-blue-100 text-blue-800';
      case 'hr admin':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleActionClick = (userId: string, buttonRef: HTMLButtonElement | null) => {
    if (buttonRef) {
      dropdownButtonRefs.current[userId] = buttonRef;
    }
    setActiveDropdown(activeDropdown === userId ? null : userId);
  };

  const handleEditClick = (user: User) => {
    setSelectedUser(user);
    setEditModalOpen(true);
    setActiveDropdown(null);
  };

  const handleDeactivateClick = (userId: string) => {
    onUserAction(userId, 'deactivate');
    setActiveDropdown(null);
  };

  const handleSaveUser = (updatedUser: User) => {
    onUserAction(updatedUser.id, 'update');
    setEditModalOpen(false);
    setSelectedUser(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100">
      <div className="relative">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th className="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm text-gray-900">
                    <HighlightedText 
                      text={`${user.firstName} ${user.lastName}`} 
                      searchQuery={searchQuery}
                    />
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-500">
                    <HighlightedText 
                      text={user.email} 
                      searchQuery={searchQuery}
                    />
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 text-xs rounded-full ${getRoleColor(user.role)}`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-500">
                    <HighlightedText 
                      text={user.department} 
                      searchQuery={searchQuery}
                    />
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(user.status)}`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-500">{user.lastLogin}</td>
                  <td className="py-3 px-4 text-center">
                    <div className="relative">
                      <button
                        ref={(el) => {
                          dropdownButtonRefs.current[user.id] = el;
                        }}
                        className="text-gray-400 hover:text-gray-600 cursor-pointer"
                        onClick={(e) => handleActionClick(user.id, e.currentTarget)}
                        aria-label="User actions"
                      >
                        <MoreHorizontal size={20} />
                      </button>
                      <UserDropdown
                        isOpen={activeDropdown === user.id}
                        onClose={() => setActiveDropdown(null)}
                        onEdit={() => handleEditClick(user)}
                        onDeactivate={() => handleDeactivateClick(user.id)}
                        buttonRef={{ current: dropdownButtonRefs.current[user.id] }}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} users
          </div>
          <div className="flex gap-2">
            <button 
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 border border-gray-300 rounded-md text-sm ${
                currentPage === 1
                  ? 'text-gray-400 bg-gray-50'
                  : 'text-gray-700 bg-white hover:bg-gray-50'
              } cursor-pointer`}
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 border border-gray-300 rounded-md text-sm ${
                currentPage === totalPages
                  ? 'text-gray-400 bg-gray-50'
                  : 'text-gray-700 bg-white hover:bg-gray-50'
              } cursor-pointer`}
            >
              Next
            </button>
          </div>
        </div>
      </div>

      {selectedUser && (
        <EditUserModal
          isOpen={editModalOpen}
          onClose={() => {
            setEditModalOpen(false);
            setSelectedUser(null);
          }}
          user={selectedUser}
          onSave={handleSaveUser}
        />
      )}
    </div>
  )
}

export default UserTable