'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { FiClock, FiChevronDown, FiBookOpen, FiVideo, FiFileText, FiUser } from 'react-icons/fi';
import SearchBar from '@/components/ui/SearchBar';
import Modal from '@/components/ui/modal';
import { format } from 'date-fns';
import type { JSX } from 'react';
import Loader from '@/components/ui/Loader';

// Types
interface Course {
  id: number;
  title: string;
  author: {
    id: number;
    name: string;
    department: string;
  } | null;
  submittedDate: string;
  officialTags: string[];
  temporaryTags: string[];
  status: 'DRAFT' | 'PENDING_APPROVAL' | 'PUBLISHED' | 'ARCHIVED';
  category: string;
  description: string;
  materialType?: 'VIDEO' | 'DOCUMENT' | 'BOTH';
}

interface Competency {
  id: number;
  competencyName: string;
  description: string | null;
}

interface CourseCardProps {
  course: Course;
}

interface FilterDropdownProps {
  title: string;
  options: string[];
  selected: string | string[] | null;
  onSelect: (value: any) => void;
  onClear: () => void;
  multiple?: boolean;
}

interface ApiResponse {
  materials: Course[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// CourseCard Component
const CourseCard = ({ course }: CourseCardProps) => {
  const router = useRouter();
  const formattedDate = format(new Date(course.submittedDate), 'MM/dd/yyyy');
  const tags = [...(course.officialTags || []), ...(course.temporaryTags || [])];
  const [showTooltip, setShowTooltip] = useState(false);
  const [showTitleTooltip, setShowTitleTooltip] = useState(false);

  const handleCourseClick = () => {
    router.push(`/main/employee/Dashboard?module=courseDetails&id=${course.id}`);
  };

  // Icon and tooltip for course type
  const renderTypeIcon = () => {
    if (course.materialType === 'VIDEO') {
      return (
        <span className="group relative flex items-center">
          <FiVideo className="text-green-500 w-5 h-5" />
          <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
            Video Course
          </span>
        </span>
      );
    } else if (course.materialType === 'DOCUMENT') {
      return (
        <span className="group relative flex items-center">
          <FiFileText className="text-[#0077CC] w-5 h-5" />
          <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
            Document Based
          </span>
        </span>
      );
    } else if (course.materialType === 'BOTH') {
      return (
        <span className="flex gap-2 items-center">
          <span className="group relative flex items-center">
            <FiVideo className="text-green-500 w-5 h-5" />
            <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
              Video Course
            </span>
          </span>
          <span className="group relative flex items-center">
            <FiFileText className="text-[#0077CC] w-5 h-5" />
            <span className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 hidden group-hover:block bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-20">
              Document Based
            </span>
          </span>
        </span>
      );
    }
    return null;
  };

  return (
    <div
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-100 flex flex-col h-full min-h-[280px] md:min-h-[300px] w-full max-w-full"
      onClick={handleCourseClick}
      tabIndex={0}
      role="button"
      aria-label={`View details for ${course.title}`}
    >
      <div className="flex flex-col h-full p-4 gap-1.5 justify-between">
        <div className="flex items-center justify-start gap-2 mb-2">
          <span className="text-[#1B2B4B] text-xs font-semibold twhitespace-nowrap">{course.category}</span>
        </div>
        {/* Title with custom tooltip */}
        <div className="relative w-full">
          <h3
            className="text-xl font-bold mb-1 text-black line-clamp-2 cursor-pointer"
            onMouseEnter={() => setShowTitleTooltip(true)}
            onMouseLeave={() => setShowTitleTooltip(false)}
            style={{ wordBreak: 'break-word' }}
          >
            {course.title}
          </h3>
          {showTitleTooltip && (
            <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 z-[9999] bg-black text-white text-xs rounded px-3 py-2 shadow-lg max-w-xs w-max whitespace-pre-line text-center pointer-events-none">
              {course.title}
            </div>
          )}
        </div>
        {/* Instructor and tags row */}
        <div className="flex items-center justify-between gap-2 mb-1 w-full flex-wrap">
          <p className="text-black mb-0.5 text-sm font-semibold flex items-center gap-1 flex-shrink-0 truncate max-w-[50%]">
            <FiUser className="text-gray-500 w-4 h-4" />
            {course.author?.name}
          </p>
          {tags.length > 0 && (
            <div className="flex items-center gap-1 flex-shrink-0">
              <span className="bg-blue-50 text-blue-800 border border-blue-100 px-2 py-0.5 rounded-full text-xs font-medium truncate max-w-[200px]">{tags[0]}</span>
              {tags.length > 1 && (
                <span
                  className="relative group cursor-pointer text-xs text-gray-500 hover:underline "
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                >
                  + See more
                  {showTooltip && (
                    <span className="absolute z-[9999] left-1/2 -translate-x-1/2 top-full mt-2 bg-black text-white text-xs rounded px-3 py-2 whitespace-pre-line shadow-lg min-w-[120px] max-w-xs">
                      {tags.join(', ')}
                    </span>
                  )}
                </span>
              )}
            </div>
          )}
        </div>
        <p className="text-gray-600 text-sm mb-0.5 leading-6 line-clamp-4">{course.description}</p>
        {/* Course Type Icon(s) under description */}
        <div className="flex items-center gap-2 mt-0.5 mb-1">
          <span className="text-xs font-semibold text-gray-600">Course Type:</span>
          {renderTypeIcon()}
        </div>
        <div className="flex border-t border-gray-100 items-center justify-between text-xs text-black pt-2 mt-auto">
          <div className="flex items-center gap-1">
            <FiClock className="text-black w-4 h-4" />
            <span className="text-xs">Uploaded on: {formattedDate}</span>
          </div>
          <button
            className="bg-[#0077CC] px-5 text-xs font-semibold  text-white py-2 rounded-lg hover:bg-[#0066B3] focus:bg-[#005999] duration-200 transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-200 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/main/employee/Dashboard?module=courseDetails&id=${course.id}`);
            }}
          >
            View
          </button>
        </div>
      </div>
    </div>
  );
};

// FilterDropdown Component
const FilterDropdown = ({
  title,
  options,
  selected,
  onSelect,
  onClear,
  multiple = false
}: FilterDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (option: string) => {
    if (multiple) {
      if (Array.isArray(selected)) {
        const isSelected = selected.includes(option);
        if (isSelected) {
          onSelect(selected.filter(item => item !== option));
        } else {
          onSelect([...selected, option]);
        }
      } else {
        onSelect([option]);
      }
    } else {
      onSelect(option === selected ? null : option);
      setIsOpen(false);
    }
  };

  return (
    <div className='relative'>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 cursor-pointer py-2 bg-white z-40 border border-gray-200 rounded-lg text-sm text-black hover:bg-gray-50"
      >
        {!multiple && selected ? (
          <span className="font-medium text-[#0077CC]">{selected}</span>
        ) : (
          <span className="font-medium">{title}</span>
        )}
        {Array.isArray(selected) && selected.length > 0 && (
          <span className="flex items-center gap-1 text-[#0077CC] bg-blue-50 px-2 py-0.5 rounded-full text-xs">
            {selected.length} selected
          </span>
        )}
        <FiChevronDown className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-[15]">
          <div className="py-1">
            {options.map((option) => (
              <button
                key={option}
                onClick={() => handleSelect(option)}
                className={`w-full text-left px-4 py-2 text-sm cursor-pointer ${
                  multiple
                    ? Array.isArray(selected) && selected.includes(option)
                      ? 'bg-blue-50 text-[#0077CC]'
                      : 'text-black hover:bg-gray-50'
                    : selected === option
                    ? 'bg-blue-50 text-[#0077CC]'
                    : 'text-black hover:bg-gray-50'
                }`}
              >
                {multiple && (
                  <span className="inline-block w-4 mr-2">
                    {Array.isArray(selected) && selected.includes(option) && '✓'}
                  </span>
                )}
                {option}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Fetch competencies function
const fetchCompetencies = async (): Promise<Competency[]> => {
  try {
    const response = await fetch('/api/competencies/list');
    if (!response.ok) {
      throw new Error('Failed to fetch competencies');
    }
    const data = await response.json();
    return data.competencies;
  } catch (error) {
    console.error('Error fetching competencies:', error);
    return [];
  }
};

// Fetch courses function
const fetchCourses = async (
  page: number,
  searchQuery: string = '',
  selectedCategory: string | null = null,
  selectedType: string | null = null,
  selectedCompetencies: string[] = []
): Promise<ApiResponse> => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: '6',
      search: searchQuery,
      ...(selectedCategory && { category: selectedCategory }),
      ...(selectedType && { type: selectedType }),
      ...(selectedCompetencies.length > 0 && { competencies: selectedCompetencies.join(',') }),
      status: 'PUBLISHED'
    });

    const response = await fetch(`/api/training-material/list?${params}`);
    if (!response.ok) {
      throw new Error('Failed to fetch courses');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw error;
  }
};

// NoDataState Component
const NoDataState: React.FC<{ message: string; subMessage?: string }> = ({ message, subMessage }) => (
  <div className="col-span-3 flex flex-col items-center justify-center py-12 px-4 text-center">
    <div className="w-16 h-16 mb-4 text-gray-400">
      <FiBookOpen className="w-full h-full" />
    </div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">{message}</h3>
    {subMessage && <p className="text-sm text-gray-500">{subMessage}</p>}
  </div>
);

// Main HomePage Component
const HomePage = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    totalPages: 1,
    totalItems: 0,
    hasNextPage: false,
    hasPrevPage: false
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [selectedCompetencies, setSelectedCompetencies] = useState<string[]>([]);
  const [competencies, setCompetencies] = useState<Competency[]>([]);
  const [isFaqOpen, setIsFaqOpen] = useState(false);

  const toggleModal = () => setIsFaqOpen(!isFaqOpen);

  // Filter options
  const categories = ['Training', 'Symposia', 'Continuing Professional Education (CPE)'];
  const types = ['Video Course', 'Document Based', 'Both'];

  // Fetch competencies on mount
  useEffect(() => {
    const loadCompetencies = async () => {
      const fetchedCompetencies = await fetchCompetencies();
      setCompetencies(fetchedCompetencies);
    };
    loadCompetencies();
  }, []);

  useEffect(() => {
    const loadCourses = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchCourses(
          currentPage,
          searchQuery,
          selectedCategory,
          selectedType,
          selectedCompetencies
        );
        setCourses(data.materials);
        setPagination(data.pagination);
      } catch (err) {
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    loadCourses();
  }, [currentPage, searchQuery, selectedCategory, selectedType, selectedCompetencies]);

  const handleSearch = (query: string, setResultCount: (count: number) => void) => {
    setSearchQuery(query);
    setCurrentPage(1);
    setResultCount(pagination.totalItems);
  };

  return (
    <div className="p-2">
      <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:justify-between sm:items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold mb-2 text-black">Explore Courses</h1>
          <p className="text-black">Discover wide range of courses and start learning today</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center border-b-2 border-gray-200 py-4 justify-between gap-4 mb-6">
      <div className="flex items-center  gap-4 ">
        <FilterDropdown
          title="Category"
          options={categories}
          selected={selectedCategory}
          onSelect={setSelectedCategory}
          onClear={() => setSelectedCategory(null)}
        />
        <FilterDropdown
          title="Type"
          options={types}
          selected={selectedType}
          onSelect={setSelectedType}
          onClear={() => setSelectedType(null)}
        />
        <FilterDropdown
          title="Competencies"
          options={competencies.map(comp => comp.competencyName)}
          selected={selectedCompetencies}
          onSelect={setSelectedCompetencies}
          onClear={() => setSelectedCompetencies([])}
          multiple
        />
        </div>
         <div className="w-full sm:w-[350px] md:w-[400px]">
          <SearchBar
            onSearch={handleSearch}
            placeholder="Search courses..."
            title=""
            subtitle=""
            showResultCount={false}
            className=" shadow-sm"
          />
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="text-red-500 bg-red-50 p-4 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="flex justify-center items-center h-96">
          <Loader />
        </div>
      ) : (
        <>
          {/* Course Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {courses.length === 0 ? (
              <NoDataState 
                message={
                  searchQuery || selectedCategory || selectedType || selectedCompetencies.length > 0
                    ? "No Matching Courses Found"
                    : "No Courses Available"
                }
                subMessage={
                  searchQuery || selectedCategory || selectedType || selectedCompetencies.length > 0
                    ? "Try adjusting your search criteria or filters to find what you're looking for."
                    : "There are no courses available at the moment. Please check back later for new content."
                }
              />
            ) : (
              courses.map((course) => (
                <CourseCard key={course.id} course={course} />
              ))
            )}
          </div>

          {/* Pagination */}
          {courses.length > 0 && pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-8">
              <button
                onClick={() => setCurrentPage(prev => prev - 1)}
                disabled={!pagination.hasPrevPage}
                className={`flex items-center gap-2 cursor-pointer px-4 py-2 rounded-lg transition-all duration-200 ${
                  pagination.hasPrevPage
                    ? 'bg-white text-[#0077CC] border border-[#0077CC] hover:bg-[#0077CC] hover:text-white shadow-sm'
                    : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                }`}
              >
                <span>Previous</span>
              </button>
              
              <div className="flex items-center gap-2">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNum) => (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`w-10 cursor-pointer h-10 rounded-lg transition-all duration-200 ${
                      currentPage === pageNum
                        ? 'bg-[#0077CC] text-white shadow-md'
                        : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                    }`}
                  >
                    {pageNum}
                  </button>
                ))}
              </div>

              <button
                onClick={() => setCurrentPage(prev => prev + 1)}
                disabled={!pagination.hasNextPage}
                className={`flex items-center gap-2 cursor-pointer px-4 py-2 rounded-lg transition-all duration-200 ${
                  pagination.hasNextPage
                    ? 'bg-white text-[#0077CC] border border-[#0077CC] hover:bg-[#0077CC] hover:text-white shadow-sm'
                    : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                }`}
              >
                <span>Next</span>
              </button>
            </div>
          )}
        </>
      )}

      <Modal
        isOpen={isFaqOpen}
        onClose={toggleModal}
        title="How Can We Help You?"
        size="large"
      >
        <div className="p-4">
          {/* FAQ content will go here */}
        </div>
      </Modal>
    </div>
  );
};

export default HomePage;
