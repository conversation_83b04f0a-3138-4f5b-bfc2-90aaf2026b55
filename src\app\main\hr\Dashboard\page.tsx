'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import HRSidebar from '@/components/layout/HRsidebar';
import { SidebarProvider } from '@/components/layout/HRsidebar';
import HRnav from '@/components/layout/HRnav';
import HRDashboard from '../Modules/HRDashboard';
import ParticipationModule from '../Modules/participation';
import ApprovalsModule from '../Modules/approvals/page';
import AnalyticsModule from '../Modules/analytics';
import Loader from '@/components/ui/Loader';

const HRPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeModule, setActiveModule] = useState<'dashboard' | 'participation' | 'approvals' | 'analytics'>('dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (status === 'authenticated') {
      if (session.user.role !== 'HR_ADMIN') {
        router.replace('/access-denied');
      }
    } else if (status === 'unauthenticated') {
      router.replace('/');
    }
  }, [session, status, router]);

  useEffect(() => {
    const module = searchParams?.get('module') || null;
    setIsLoading(true);
    if (module === 'participation') {
      setActiveModule('participation');
    } 
    else if (module === 'approvals') {
      setActiveModule('approvals');
    }
    else if (module === 'analytics') {
      setActiveModule('analytics');
    }
    else {
      setActiveModule('dashboard');
    }
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [searchParams]);

  if (status === 'loading') {
    return <Loader />;
  }
  
  if (status !== 'authenticated' || (session && session.user.role !== 'HR_ADMIN')) {
    return null;
  }

  return (
    <>
      {isLoading && <Loader />}
      <SidebarProvider>
        <div className="flex min-h-screen bg-gray-50">
          <HRSidebar onCollapse={(collapsed) => setIsSidebarCollapsed(collapsed)} />
          <div className="flex-1 min-w-0">
            <HRnav />
            <main 
              className={`relative min-w-0 p-4 sm:p-5 transition-all duration-300 mt-16 ${
                isSidebarCollapsed ? 'ml-[80px]' : 'ml-[270px]'
              }`}
            >
              <div className="w-full min-w-0 max-w-[1920px] mx-auto">
                {activeModule === 'dashboard' && <HRDashboard />}
                {activeModule === 'participation' && <ParticipationModule />}
                {activeModule === 'approvals' && <ApprovalsModule />}
                {activeModule === 'analytics' && <AnalyticsModule />}
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    </>
  );
};

export default HRPage; 