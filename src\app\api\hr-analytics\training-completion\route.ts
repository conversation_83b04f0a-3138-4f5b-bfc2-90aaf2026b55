import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  unitId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  divisionId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month')
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'HR_ADMIN') {
      return NextResponse.json({ error: 'Forbidden: Insufficient permissions' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    
    // Validate query parameters using Zod
    const validationResult = querySchema.safeParse(Object.fromEntries(searchParams.entries()));
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { unitId, divisionId, timeframe } = validationResult.data;

    // Calculate date range based on timeframe
    const today = new Date();
    let startDate = new Date();
    
    switch (timeframe) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(today.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(today.getMonth() - 1); // default to month
    }

    // Build the user where clause based on filters
    const userWhereClause: any = {};
    
    if (unitId) {
      userWhereClause.unitId = unitId;
    } else if (divisionId) {
      userWhereClause.unit = {
        divisionId
      };
    }

    // Get total users matching the filter
    const totalUsers = await prisma.user.count({
      where: userWhereClause
    });

    if (totalUsers === 0) {
      return NextResponse.json({
        completionRate: 0,
        totalEnrollments: 0,
        completedTrainings: 0,
        inProgressTrainings: 0,
        notStartedTrainings: 0,
        trend: 0
      });
    }

    // Get all users matching the filter
    const users = await prisma.user.findMany({
      where: userWhereClause,
      select: {
        id: true
      }
    });

    const userIds = users.map(user => user.id);

    // Get training progress stats
    const trainingStats = await prisma.userTrainingProgress.groupBy({
      by: ['status'],
      _count: true,
      where: {
        userId: {
          in: userIds
        },
        updatedAt: {
          gte: startDate
        }
      }
    });

    // Calculate counts by status
    let completedCount = 0;
    let inProgressCount = 0;
    let notStartedCount = 0;

    trainingStats.forEach(stat => {
      if (stat.status === 'COMPLETED') {
        completedCount = stat._count;
      } else if (stat.status === 'IN_PROGRESS') {
        inProgressCount = stat._count;
      } else if (stat.status === 'NOT_STARTED') {
        notStartedCount = stat._count;
      }
    });

    // Get total enrollments
    const totalEnrollments = completedCount + inProgressCount + notStartedCount;

    // Calculate completion rate
    const completionRate = totalEnrollments > 0 
      ? parseFloat(((completedCount / totalEnrollments) * 100).toFixed(1)) 
      : 0;

    // Mock trend data - in a real implementation, would compare to previous period
    const trend = 3.2; // 3.2% increase from previous period

    return NextResponse.json({
      completionRate,
      totalEnrollments,
      completedTrainings: completedCount,
      inProgressTrainings: inProgressCount,
      notStartedTrainings: notStartedCount,
      trend
    });
    
  } catch (error) {
    console.error('Error calculating training completion:', error);
    return NextResponse.json(
      { error: 'Failed to calculate training completion' },
      { status: 500 }
    );
  }
} 