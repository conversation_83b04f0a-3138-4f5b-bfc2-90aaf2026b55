'use client';

import React, { useState } from 'react';
import { BookOpen, Eye, ChevronDown, ChevronUp } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { PublicationRowProps } from '../utils/types';
import { highlightSearchMatches } from '../utils/helpers';
import StatusBadge from './StatusBadge';
import ActionButton from './ActionButton';

const PublicationRow: React.FC<PublicationRowProps> = ({ item, searchQuery, onReview }) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [showAllTags, setShowAllTags] = useState(false);

  // Combine all tags
  const allTags = [...item.officialTags, ...item.temporaryTags];
  const hasMoreTags = allTags.length > 2;

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
            <BookOpen className="w-4 h-4 text-color3" />
          </div>
          <div className="min-w-0">
            <div className="text-sm truncate font-medium text-gray-900 max-w-[230px]">
              {highlightSearchMatches(item.title, searchQuery)}
            </div>
            {allTags.length > 0 && (
              <div className="flex flex-wrap gap-1.5 mt-1.5">
                {/* Show first 2 tags or all tags based on state */}
                {(showAllTags ? allTags : allTags.slice(0, 2)).map((tag, index) => (
                  <span
                    key={index}
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.officialTags.includes(tag)
                        ? 'bg-blue-50 text-blue-600'
                        : 'bg-gray-100 text-gray-600'
                    }`}
                  >
                    {tag}
                  </span>
                ))}
                {/* Show see more/less button if there are more than 2 tags */}
                {hasMoreTags && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowAllTags(!showAllTags);
                    }}
                    className="inline-flex items-center gap-0.5 px-2 py-0.5 text-xs font-medium text-gray-500 hover:text-gray-700"
                  >
                    {showAllTags ? (
                      <>
                        See less
                        <ChevronUp className="w-3 h-3" />
                      </>
                    ) : (
                      <>
                        +{allTags.length - 2} more
                        <ChevronDown className="w-3 h-3" />
                      </>
                    )}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {item.author ? (
          <React.Fragment>
            <div className="text-sm text-gray-900">{item.author.name}</div>
            <div className="text-sm text-gray-500">{item.author.department}</div>
          </React.Fragment>
        ) : (
          <div className="text-sm text-gray-500">No author specified</div>
        )}
      </td>
      <td className="px-6 py-4 align-top">
        <div className="text-sm text-gray-900 break-words max-w-xs">
          {item.category}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <StatusBadge status={item.status} />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">
          {new Date(item.submittedDate).toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: '2-digit', 
            day: '2-digit' 
          })}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right">
        <div className="flex items-center justify-end gap-2">
          <button
            onClick={() => onReview(item)}
            className="flex items-center cursor-pointer gap-1 px-3 py-1 text-sm text-color3 hover:bg-color3/10 rounded-md transition-colors"
          >
            <Eye className="w-4 h-4" />
            <span>Review</span>
          </button>
          <ActionButton 
            materialId={item.id} 
            status={item.status} 
            isCreator={item.author?.id === (session?.user?.id ? parseInt(session.user.id) : null)} 
            onStatusChange={() => router.refresh()}
          />
        </div>
      </td>
    </tr>
  );
};

export default PublicationRow;