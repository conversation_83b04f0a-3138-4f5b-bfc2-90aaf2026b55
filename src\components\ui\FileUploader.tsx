"use client"

import { useState, useRef } from "react"
import { Upload, AlertCircle, Info } from "lucide-react"
import { validateFiles, generateAcceptString, FileValidationError } from "@/utils/fileValidation"
import { toast } from "sonner"
import FileErrorDialog from "./FileErrorDialog"

interface FileUploaderProps {
  id?: string;
  onFilesSelected: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
}

const FileUploader: React.FC<FileUploaderProps> = ({ 
  id, 
  onFilesSelected, 
  accept = generateAcceptString(), 
  multiple = false 
}) => {
  const [isDragging, setIsDragging] = useState(false)
  const [error, setError] = useState<FileValidationError | null>(null)
  const [showErrorDialog, setShowErrorDialog] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFiles = (files: File[]) => {
    setError(null)
    setShowErrorDialog(false)
    
    // Validate files
    const validationError = validateFiles(files)
    if (validationError) {
      setError(validationError)
      // Automatically show the error dialog
      setShowErrorDialog(true)
      return
    }

    onFilesSelected(files)
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files.length > 0) {
      const filesArray = Array.from(e.dataTransfer.files)
      handleFiles(filesArray)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files)
      handleFiles(filesArray)

      // Reset the input value so the same file can be uploaded again if needed
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const closeErrorDialog = () => {
    setShowErrorDialog(false)
  }

  const handleShowErrorDialog = () => {
    setShowErrorDialog(true)
  }

  return (
    <div className="flex flex-col">
      <input
        ref={fileInputRef}
        id={id}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileInputChange}
        className="hidden"
      />

      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={triggerFileInput}
        className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors ${
          isDragging ? "border-primary bg-primary/5" : "border-gray-300 hover:border-primary/50"
        } ${error ? "border-red-100" : ""}`}
      >
        <div className="flex flex-col items-center">
          <Upload className={`w-8 h-8 ${error ? "text-red-400" : "text-gray-400"} mb-2`} />
          <p className="text-sm font-medium">Drag and drop files here, or click to browse</p>
          <p className="text-xs text-gray-500 mt-1">
            {multiple ? "You can upload multiple files" : "You can upload one file"}
          </p>
          {accept !== "*" && (
            <p className="text-xs text-gray-500 mt-1">
              Accepted formats: {accept.replace(/\./g, "").replace(/,/g, ", ")}
            </p>
          )}
          <p className="text-xs text-gray-500 mt-1">
            Maximum file size: 300MB
          </p>
        </div>
      </div>
      
      {/* Error notification pill outside the upload box */}
      {error && !showErrorDialog && (
        <div 
          className="flex items-center justify-center mt-2 px-3 py-1.5 bg-red-50 border border-red-100 rounded-full text-xs text-red-600 cursor-pointer hover:bg-red-100 transition-colors self-center"
          onClick={handleShowErrorDialog}
        >
          <Info className="w-3 h-3 mr-1" />
          <span>Click for file upload requirements</span>
        </div>
      )}

      {/* Error Dialog */}
      {showErrorDialog && error && (
        <FileErrorDialog error={error} onClose={closeErrorDialog} />
      )}
    </div>
  )
}

export default FileUploader 