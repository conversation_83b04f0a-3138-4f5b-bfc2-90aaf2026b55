'use client';

import { Progress } from '@/components/ui/progress';
import { MaterialOverviewProps } from '../types/trainingTypes';
import { FiClock, FiTarget, FiFile, FiArrowRight } from 'react-icons/fi';
import { Button } from '@/components/ui/Button';

export const MaterialOverview = ({
  currentStep,
  trainingData,
  currentMaterial,
  onNextModule,
  allStepsCompleted,
  onFinishCourse
}: MaterialOverviewProps) => {
  // Find current module based on current step
  const currentModuleIndex = currentStep
    ? trainingData.modules.findIndex(module =>
        module.steps.some(step => step.id === currentStep.id)
      )
    : -1;

  const currentModule = currentModuleIndex !== -1
    ? trainingData.modules[currentModuleIndex]
    : null;

  const hasNextModule = currentModuleIndex !== -1 &&
    currentModuleIndex < trainingData.modules.length - 1;

  const isModuleCompleted = currentModule?.completed || false;

  return (
    <div className="h-full p-4 md:p-6 flex flex-col overflow-y-auto">
      {/* Progress Section */}
      <div className="mb-6 md:mb-8">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Course Progress</h3>
        <Progress value={trainingData.progress} className="h-2 bg-gray-100" />
        <div className="flex justify-between text-sm mt-2">
          <span className="text-gray-700">{trainingData.completedSteps} of {trainingData.totalSteps} steps</span>
          <span className="font-medium text-blue-600">{Math.round(trainingData.progress)}%</span>
        </div>
      </div>

      {/* Current Material Info */}
      {currentMaterial && (
        <div className="mb-4 md:mb-5">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Current Material</h3>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <FiFile className="w-5 h-5 text-gray-400 flex-shrink-0" />
              <span className="text-sm text-gray-900 font-medium truncate">{currentMaterial.name}</span>
            </div>
            <div className="text-xs text-gray-500 space-y-2">
              <p className="flex items-center gap-2">
                <span className="font-medium">Type:</span>
                <span className="uppercase">{currentMaterial.type}</span>
              </p>
              <p className="flex items-center gap-2">
                <span className="font-medium">Status:</span>
                <span className={currentMaterial.viewed ? "text-green-600" : "text-yellow-600"}>
                  {currentMaterial.viewed ? 'Completed' : 'In Progress'}
                </span>
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Next Module Button */}
      {currentModule && currentModule.completed && hasNextModule && typeof onNextModule === 'function' && !allStepsCompleted && (
        <div className="my-6 md:my-8">
          <Button
            onClick={() => onNextModule(trainingData.modules[currentModuleIndex + 1].id)}
            className="w-full bg-color3 hover:bg-color3/80 text-white flex items-center justify-center gap-2 py-2.5"
            variant="default"
          >
            Continue to Next Module
            <FiArrowRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Finish Course Button */}
      {allStepsCompleted && typeof onFinishCourse === 'function' && (
        <div className="my-6 md:my-8">
          <Button
            onClick={onFinishCourse}
            className="w-full flex items-center justify-center gap-2 py-2.5 bg-color3 hover:bg-green-600 text-white"
            variant="default"
          >
            Finish Training Material
            <FiArrowRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Current Step Overview */}
      {currentStep && (
        <div className="mb-6 md:mb-8">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Step Information</h3>
          <div className="space-y-4">
            <div className="flex items-center text-sm text-gray-700">
              <FiClock className="h-4 w-4 mr-2 text-blue-600 flex-shrink-0" />
              <span>Estimated time: {currentStep.estimatedTime}</span>
            </div>

            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-900">Learning Objectives</h4>
              <ul className="space-y-2">
                {currentStep.learningObjectives.map((objective, index) => (
                  <li key={index} className="flex items-start">
                    <FiTarget className="h-4 w-4 mr-2 text-blue-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{objective}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Course Overview */}
      <div className="mb-6 md:mb-8">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Course Information</h3>
        <div className="space-y-3">
          <p className="text-sm text-gray-700 leading-relaxed">{trainingData.description}</p>
          <div className="flex items-center text-sm text-gray-700">
            <FiClock className="h-4 w-4 mr-2 text-blue-600 flex-shrink-0" />
            <span>Total time: {trainingData.estimatedTotalTime}</span>
          </div>
          {trainingData.instructor && (
            <p className="text-sm text-gray-700">
              Instructor: {trainingData.instructor}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};