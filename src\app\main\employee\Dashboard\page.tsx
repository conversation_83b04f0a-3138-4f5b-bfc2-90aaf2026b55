'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Sidebar from '@/components/layout/sidebar';
import HomePage from '../Modules/homePage';
import KnowledgeBase from '../Modules/knowledgeBase';
import CourseDetails from '../Modules/courseDetails';
import CreateCourse from '../Modules/createCourse';
import FAQ from '@/components/ui/FAQ';
import Modal from '@/components/ui/modal';
import FAQContents from '@/components/ui/FAQContents';
import Loader from '@/components/ui/Loader';

export default function Page() {
  const searchParams = useSearchParams();
  const [activeModule, setActiveModule] = useState<'home' | 'knowledge' | 'courseDetails' | 'createCourse'>('home');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isFaqOpen, setIsFaqOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const toggleModal = () => {
    setIsFaqOpen(!isFaqOpen);
  };

  useEffect(() => {
    setIsLoading(true);
    const module = searchParams?.get('module') || null;
    if (module === 'knowledge') {
      setActiveModule('knowledge');
    } 
    else if (module === 'courseDetails') {
      setActiveModule('courseDetails');
    }
    else if (module === 'createCourse') {
      setActiveModule('createCourse');
    }
    else {
      setActiveModule('home');
    }
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [searchParams]);

  return (
    <>
      {isLoading && <Loader />}
      <div className="flex min-h-screen bg-gray-50">
        <Sidebar onCollapse={(collapsed) => setIsSidebarCollapsed(collapsed)} />
        <div 
          className={`flex-1 relative p-8 transition-all duration-300 ${
            isSidebarCollapsed ? 'ml-[80px]' : 'ml-[300px]'
          }`}
        >
          {activeModule === 'home' && <HomePage />}
          {activeModule === 'knowledge' && <KnowledgeBase />}
          {activeModule === 'courseDetails' && <CourseDetails />}
          {activeModule === 'createCourse' && <CreateCourse />}
        </div>
        {/* FAQ Floating Widget */}\
        <FAQ toggleModal={toggleModal} />

        {/* FAQ Modal */}
        <Modal isOpen={isFaqOpen} onClose={toggleModal} size='large' title="How Can We Help You?">
          <FAQContents />
        </Modal>
      </div>
    </>
  );
}