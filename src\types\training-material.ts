// src/types/training-material.ts
export interface TrainingMaterial {
    id: number;
    title: string;
    officialTags: string[];
    temporaryTags: string[];
    status: 'PUBLISHED' | 'PENDING_APPROVAL' | 'DRAFT' | 'ARCHIVED';
    submittedDate: Date;
    category: string;
    author: {
      id: number;
      name: string;
      department: string;
    } | null;
  }
  
  export interface PaginationData {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }