'use client';

import React from 'react';
import { TrendingUp, TrendingDown, LucideIcon } from 'lucide-react';

export interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: number;
  subtitle?: string;
  description?: string;
  progress?: number;
  color?: string;
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  subtitle,
  description,
  progress,
  color = 'text-color3',
  className = ''
}) => {
  const isPositiveTrend = trend !== undefined && trend > 0;

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-100 p-6 min-h-[140px] relative overflow-hidden ${className}`}>
      <div className='flex justify-between items-start mb-2'>
        <div className='space-y-2'>
          <h3 className='text-sm font-medium text-gray-700'>{title}</h3>
          <p className={`text-3xl font-semibold ${color}`}>{value}</p>
          {subtitle && <p className='text-xs text-gray-500'>{subtitle}</p>}
          {description && <p className='text-xs text-gray-400'>{description}</p>}
          {trend !== undefined && (
            <div className={`flex items-center gap-1 text-xs ${isPositiveTrend ? 'text-green-600' : 'text-red-600'}`}>
              {isPositiveTrend ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
              <span>{Math.abs(trend)}% from last month</span>
            </div>
          )}
        </div>
        <div className={`p-2 rounded-lg ${color} bg-opacity-10`}>
          <Icon size={20} className={color} />
        </div>
      </div>
      {progress !== undefined && (
        <div className='w-full mt-4'>
          <div className='h-2 w-full bg-gray-100 rounded-full overflow-hidden'>
            <div 
              className={`h-full ${color} transition-all duration-500 ease-out`} 
              style={{ width: `${progress}%` }} 
            />
          </div>
        </div>
      )}
    </div>
  );
}; 