import React from 'react';
import RoleI<PERSON>, { Role } from './RoleItem';

interface RolesListProps {
  roles: Role[];
  onEditRole: (role: Role) => void;
}

const RolesList: React.FC<RolesListProps> = ({ roles, onEditRole }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead>
          <tr>
            <th scope="col" className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Role Name
            </th>
            <th scope="col" className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Users
            </th>
            <th scope="col" className="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Permission Level
            </th>
            <th scope="col" className="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {roles.map((role) => (
            <RoleItem 
              key={role.id} 
              role={role} 
              onEdit={onEditRole}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default RolesList; 