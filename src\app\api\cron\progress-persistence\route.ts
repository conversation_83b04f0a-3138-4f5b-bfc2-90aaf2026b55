import { NextRequest, NextResponse } from 'next/server';
import redis from '@/lib/redis';
import { prisma } from '@/lib/prisma';
import { 
  materialProgressSchema, 
  stepProgressSchema, 
  moduleProgressSchema, 
  courseProgressSchema,
  MaterialProgress 
} from '@/lib/schemas/progress';
import {
  acquireLock,
  releaseLock
} from '@/lib/services/progressCache';
import { ProgressStatus } from '@prisma/client';

// This endpoint is intended to be called by a cron job
// e.g., every 15 minutes via a service like Vercel Cron Jobs
export async function GET(req: NextRequest) {
  // Check for the secret in authorization header or query param
  const authHeader = req.headers.get('authorization');
  const querySecret = req.nextUrl.searchParams.get('secret');
  const cronSecret = process.env.CRON_SECRET;
  
  const isAuthorized = 
    (cronSecret && authHeader && authHeader.startsWith('Bearer ') && authHeader.split(' ')[1] === cronSecret) ||
    (cronSecret && querySecret === cronSecret);
  
  if (cronSecret && !isAuthorized) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Find all material progress keys in Redis
    let cursor = '0';
    const keys: string[] = [];
    
    do {
      const [nextCursor, scanKeys] = await redis.scan(
        cursor,
        'MATCH',
        'progress:material:*',
        'COUNT',
        '100'
      );
      cursor = nextCursor;
      keys.push(...scanKeys);
    } while (cursor !== '0');
    
  
    
    // Process all keys in batches to avoid overwhelming the database
    const batchSize = 50;
    const batches = Math.ceil(keys.length / batchSize);
    let successCount = 0;
    let failureCount = 0;
    
    for (let i = 0; i < batches; i++) {
      const batchKeys = keys.slice(i * batchSize, (i + 1) * batchSize);
      
      // Process keys in parallel within each batch
      const results = await Promise.all(
        batchKeys.map(async (key) => {
          try {
            const lockKey = `${key}`;
            const lockAcquired = await acquireLock(lockKey, 30);
            
            if (!lockAcquired) {
          
              return false;
            }
            
            try {
              const cached = await redis.get(key);
              if (!cached) return false;
              
              // Parse and validate the cached data
              const progress = materialProgressSchema.parse(JSON.parse(cached));
              
              // Check if the progress is older than 30 minutes
              const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000;
              
              if (progress.lastUpdated && progress.lastUpdated > thirtyMinutesAgo) {
                // Skip recent progress (less than 30 minutes old)
                return false;
              }
              
              // Ensure values are correct types for the database
              const progressValue = Math.round(Number(progress.progress));
              const lastPosition = progress.lastPosition ? Math.round(Number(progress.lastPosition)) : null;
              const status = progress.progress >= 100 
                ? ProgressStatus.COMPLETED 
                : progress.progress > 0 
                ? ProgressStatus.IN_PROGRESS 
                : ProgressStatus.NOT_STARTED;
              
              // Persist to database according to the schema
              await prisma.userFileProgress.upsert({
                where: { 
                  userId_resourceId: { 
                    userId: parseInt(progress.userId, 10), 
                    resourceId: parseInt(progress.materialId, 10)
                  } 
                },
                update: {
                  progress: progressValue,
                  lastPage: lastPosition,
                  lastTimestamp: Math.round(Date.now() / 1000), // Unix timestamp
                  status: status,
                  completedAt: status === ProgressStatus.COMPLETED ? new Date() : null,
                  lastAccessedAt: new Date(),
                  updated_at: new Date()
                },
                create: {
                  userId: parseInt(progress.userId, 10),
                  resourceId: parseInt(progress.materialId, 10),
                  progress: progressValue,
                  lastPage: lastPosition,
                  lastTimestamp: Math.round(Date.now() / 1000), // Unix timestamp
                  status: status,
                  completedAt: status === ProgressStatus.COMPLETED ? new Date() : null,
                  lastAccessedAt: new Date(),
                  created_at: new Date(),
                  updated_at: new Date()
                }
              });
              
              // Only delete from Redis after successful DB write
              await redis.del(key);
              return true;
            } catch (error) {
              console.error(`Error persisting progress for ${key}:`, error);
=
              return false;
            } finally {
              // Release the lock regardless of success/failure
              await releaseLock(lockKey);
            }
          } catch (error) {
            console.error(`Error processing key ${key}:`, error);
            return false;
          }
        })
      );
      
      // Count successful and failed operations
      successCount += results.filter(Boolean).length;
      failureCount += results.filter(result => result === false).length;
    }
    
    return NextResponse.json({
      success: true,
      total: keys.length,
      persisted: successCount,
      skipped: failureCount
    });
  } catch (error) {
    console.error('Error in progress persistence cron job:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 