import { z } from 'zod';

export const materialProgressSchema = z.object({
  userId: z.string().nonempty(),
  courseId: z.string().nonempty(),
  moduleId: z.string().nonempty(),
  stepId: z.string().nonempty(),
  materialId: z.string().nonempty(),
  materialName: z.string().nonempty(),
  materialType: z.enum(['pdf', 'youtube', 'video', 'document', 'jpg', 'jpeg', 'png', 'image']),
  progress: z.number().min(0).max(100),
  lastPosition: z.number().int().optional(),
  isViewed: z.boolean().default(false),
  lastUpdated: z.number().int().default(() => Date.now())
});

export type MaterialProgress = z.infer<typeof materialProgressSchema>;

export const stepProgressSchema = z.object({
  userId: z.string().nonempty(),
  courseId: z.string().nonempty(),
  moduleId: z.string().nonempty(),
  stepId: z.string().nonempty(),
  isCompleted: z.boolean().default(false),
  lastUpdated: z.number().int().default(() => Date.now())
});

export type StepProgress = z.infer<typeof stepProgressSchema>;

export const moduleProgressSchema = z.object({
  userId: z.string().nonempty(),
  courseId: z.string().nonempty(),
  moduleId: z.string().nonempty(),
  isCompleted: z.boolean().default(false),
  lastUpdated: z.number().int().default(() => Date.now())
});

export type ModuleProgress = z.infer<typeof moduleProgressSchema>;

export const courseProgressSchema = z.object({
  userId: z.string().nonempty(),
  courseId: z.string().nonempty(),
  totalSteps: z.number().int().min(0),
  completedSteps: z.number().int().min(0),
  progress: z.number().min(0).max(100),
  isCompleted: z.boolean().default(false),
  lastUpdated: z.number().int().default(() => Date.now())
});

export type CourseProgress = z.infer<typeof courseProgressSchema>;