# Dependencies
node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage

# Next.js
/.next/
/out/
/build

# Production
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

#Files
/storage
/PRD
.mydatabase_backup.sql
dump.rdb
ecosystem.config.js
mydatabase_backup.sql
.github/workflows/progress-persistence.yml
Progress/redis_user_progress_plan.md
