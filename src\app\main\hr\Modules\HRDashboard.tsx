'use client';

import React, { useState, useEffect } from 'react';
import 'chart.js/auto';
import { Bar } from 'react-chartjs-2';
import { ClipboardMinus, User, CircleCheck, FileText, ChartColumn, TrendingUp, TrendingDown, Clock, Eye, ChevronRight, ArrowDown, ArrowUp, Users, Award, ClipboardCheck, GraduationCap, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import TrainingAnalytics from './components/TrainingAnalytics';
import ComplianceMetrics from './components/ComplianceMetrics';
import { StatCard } from '@/components/shared/StatCard';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
);

interface PendingTrainingMaterialApproval {
  id: number;
  author: {
    name: string;
    department: string;
  } | null;
  title: string; // Resource (Title of Training Material)
  submittedDate: string; // Date
  status: string; // Status
  // Action will be a review button, similar to existing
}

// Convert color class to hex (simplified version)
const convertBgColorToHex = (colorClass: string) => '#3B82F6'; // Default to blue

const HRDashboard: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentModule = searchParams?.get('module');
  const [selectedDivision, setSelectedDivision] = useState<number | null>(null);
  const [divisions, setDivisions] = useState<{id: number, name: string}[]>([]);
  const [isLoadingDivisions, setIsLoadingDivisions] = useState<boolean>(true);
  const [departmentData, setDepartmentData] = useState<any>({
    labels: [],
    datasets: [{
      label: 'Participation Rate (%)',
      data: [],
      backgroundColor: [],
      borderRadius: 6,
      maxBarThickness: 35,
      borderColor: 'transparent'
    }]
  });
  const [pendingTrainingMaterials, setPendingTrainingMaterials] = useState<PendingTrainingMaterialApproval[]>([]);
  const [loadingTrainingMaterials, setLoadingTrainingMaterials] = useState(true);
  const [pendingApprovalsCount, setPendingApprovalsCount] = useState<number | null>(null);

  // Metrics state
  const [totalEmployees, setTotalEmployees] = useState<number | null>(null);
  const [employeeTrend, setEmployeeTrend] = useState<number>(0);
  const [completionRate, setCompletionRate] = useState<number | null>(null);
  const [completionTrend, setCompletionTrend] = useState<number>(0);
  const [skillGapIndex, setSkillGapIndex] = useState<number | null>(null);
  const [skillGapTrend, setSkillGapTrend] = useState<number>(0);
  const [isLoadingMetrics, setIsLoadingMetrics] = useState<boolean>(true);

  // Fetch divisions
  useEffect(() => {
    const fetchDivisions = async () => {
      setIsLoadingDivisions(true);
      try {
        const response = await fetch('/api/divisions');
        if (!response.ok) throw new Error('Failed to fetch divisions');
        const data = await response.json();
        
        if (data.divisions && data.divisions.length > 0) {
          setDivisions(data.divisions.map((div: any) => ({
            id: div.id,
            name: div.divisionName
          })));
          
          if (selectedDivision === null && data.divisions.length > 0) {
            setSelectedDivision(data.divisions[0].id);
          }
        }
      } catch (error) {
        console.error('Error fetching divisions:', error);
        setDivisions([]);
      } finally {
        setIsLoadingDivisions(false);
      }
    };

    fetchDivisions();
  }, []);

  // Fetch department participation data
  useEffect(() => {
    if (selectedDivision === null) return;
    
    const fetchDepartmentParticipation = async () => {
      try {
        const response = await fetch(`/api/hr-analytics/department-participation?divisionId=${selectedDivision}`);
        if (!response.ok) throw new Error('Failed to fetch department participation');
        
        const data = await response.json();
        if (data.chartData) {
          const customChartData = {
            ...data.chartData,
            datasets: data.chartData.datasets.map((dataset: any) => ({
              ...dataset,
              backgroundColor: Array(data.chartData.labels.length).fill(convertBgColorToHex('bg-blue-500')),
              borderRadius: 6,
              maxBarThickness: 35,
              borderColor: 'transparent'
            }))
          };
          setDepartmentData(customChartData);
        } else {
          setDepartmentData({
            labels: [],
            datasets: [{
              label: 'Participation Rate (%)',
              data: [],
              backgroundColor: [],
              borderRadius: 6,
              maxBarThickness: 35,
              borderColor: 'transparent'
            }]
          });
        }
      } catch (error) {
        console.error("Error fetching department participation:", error);
        setDepartmentData({
          labels: [],
          datasets: [{
            label: 'Participation Rate (%)',
            data: [],
            backgroundColor: [],
            borderRadius: 6,
            maxBarThickness: 35,
            borderColor: 'transparent'
          }]
        });
      }
    };

    fetchDepartmentParticipation();
  }, [selectedDivision]);

  // Fetch dashboard metrics
  useEffect(() => {
    const fetchDashboardMetrics = async () => {
      setIsLoadingMetrics(true);
      try {
        const [employeeRes, completionRes, skillGapRes] = await Promise.all([
          fetch('/api/hr-analytics/employee-count'),
          fetch('/api/hr-analytics/training-completion'),
          fetch('/api/hr-analytics/skill-gap')
        ]);

        if (!employeeRes.ok || !completionRes.ok || !skillGapRes.ok) {
          throw new Error('Failed to fetch one or more metrics');
        }

        const [employeeData, completionData, skillGapData] = await Promise.all([
          employeeRes.json(),
          completionRes.json(),
          skillGapRes.json()
        ]);

        setTotalEmployees(employeeData.totalCount);
        setEmployeeTrend(employeeData.trend);
        setCompletionRate(completionData.completionRate);
        setCompletionTrend(completionData.trend);
        setSkillGapIndex(skillGapData.skillGapIndex);
        setSkillGapTrend(skillGapData.trend);
      } catch (error) {
        console.error("Error fetching dashboard metrics:", error);
        // Set null values instead of mock data
        setTotalEmployees(null);
        setEmployeeTrend(0);
        setCompletionRate(null);
        setCompletionTrend(0);
        setSkillGapIndex(null);
        setSkillGapTrend(0);
      } finally {
        setIsLoadingMetrics(false);
      }
    };

    fetchDashboardMetrics();
  }, []);

  // Fetch pending training materials
  useEffect(() => {
    const fetchPendingMaterials = async () => {
      try {
        setLoadingTrainingMaterials(true);
        const response = await fetch('/api/training-material/list?status=PENDING_APPROVAL&limit=10');
        if (!response.ok) throw new Error('Failed to fetch pending materials');
        
        const data = await response.json();
        const formattedMaterials = data.materials.map((material: any) => {
          let author = null;
          if (Array.isArray(material.trainingParticipants) && material.trainingParticipants[0]?.user) {
            author = {
              name: `${material.trainingParticipants[0].user.firstName || ''} ${material.trainingParticipants[0].user.lastName || ''}`.trim(),
              department: material.trainingParticipants[0].user.unit?.unitName || 'N/A'
            };
          } else if (material.author) {
            author = {
              name: material.author.name || 'N/A',
              department: material.author.department || 'N/A'
            };
          }
          return {
            id: material.id,
            author,
            title: material.title,
            submittedDate: material.submittedDate || material.createdAt,
            status: material.status
          };
        });
        
        setPendingTrainingMaterials(formattedMaterials);
        // Set the pending approvals count from the pagination total
        setPendingApprovalsCount(data.pagination?.totalItems || 0);
      } catch (error) {
        console.error('Error fetching pending materials:', error);
        setPendingTrainingMaterials([]);
        setPendingApprovalsCount(0); // Set to 0 on error instead of null
      } finally {
        setLoadingTrainingMaterials(false);
      }
    };

    fetchPendingMaterials();
  }, []);

  // Stats configuration
  const stats = [
    {
      title: 'Total Employees',
      value: isLoadingMetrics ? '...' : totalEmployees ?? 'N/A',
      trend: employeeTrend,
      icon: Users,
      color: 'text-color3'
    },
    {
      title: 'Training Completion',
      value: isLoadingMetrics ? '...' : completionRate !== null ? `${completionRate}%` : 'N/A',
      trend: completionTrend,
      icon: GraduationCap,
      progress: completionRate || undefined,
      color: 'text-color3'
    },
    {
      title: 'Pending Approvals',
      value: pendingApprovalsCount ?? 'N/A',
      trend: 0,
      icon: FileText,
      color: 'text-color3'
    },
    {
      title: 'Skill Gap Index',
      value: isLoadingMetrics ? '...' : skillGapIndex ?? 'N/A',
      trend: skillGapTrend,
      icon: TrendingUp,
      progress: skillGapIndex || undefined,
      color: 'text-color3'
    }
  ];

  const chartOptions = {
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        padding: 12,
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        },
        callbacks: {
          label: function(context: any) {
            return `Participation: ${context.parsed.x}%`;
          }
        }
      }
    },
    scales: {
      x: {
        type: 'linear' as const,
        beginAtZero: true,
        max: 100,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          callback: function(value: string | number) {
            return `${value}%`;
          },
          font: {
            size: 12
          }
        }
      },
      y: {
        type: 'category' as const,
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12,
            weight: 'bold' as const
          },
          padding: 8,
          autoSkip: false
        }
      }
    },
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        left: 20,
        right: 20,
        top: 15,
        bottom: 15
      }
    },
    barThickness: 'flex',
    maxBarThickness: 35
  };

  const PendingTrainingMaterialApprovalsSection = ({ showAll = false }) => (
    <div className='bg-white p-6 border-[0.5px] border-gray-300 shadow-md rounded-md'>
      <div className='flex items-center justify-between mb-4'>
        <div>
          <h1 className='font-semibold text-base mb-1'>Pending Training Material Approvals</h1>
          <p className='text-xs text-gray-500'>Training materials awaiting approval</p>
        </div>
        {!showAll && (
          <Link
            href="/main/hr/Dashboard?module=approvals" // This should lead to the Publication Approvals page
            className='text-color3 hover:text-color3/80 text-xs flex items-center gap-1'
          >
            <span>View All</span>
            <ChevronRight size={14} />
          </Link>
        )}
      </div>
      <div className='overflow-y-auto max-h-[400px] -mx-6'>
        <table className='min-w-full border-separate border-spacing-0'>
          <thead className='bg-gray-50 sticky top-0 z-10'>
            <tr>
              <th className='sticky top-0 bg-gray-50 px-6 py-3 text-center text-xs font-semibold text-gray-600'>Employee</th>
              <th className='sticky top-0 bg-gray-50 px-6 py-3 text-center text-xs font-semibold text-gray-600'>Department</th>
              <th className='sticky top-0 bg-gray-50 px-6 py-3 text-center text-xs font-semibold text-gray-600'>Resource</th>
              <th className='sticky top-0 bg-gray-50 px-6 py-3 text-center text-xs font-semibold text-gray-600'>Date</th>
              <th className='sticky top-0 bg-gray-50 px-6 py-3 text-center text-xs font-semibold text-gray-600'>Status</th>
              <th className='sticky top-0 bg-gray-50 px-6 py-3 text-right text-xs font-semibold text-gray-600 pr-6'>Action</th>
            </tr>
          </thead>
          <tbody className='divide-y divide-gray-200 bg-white'>
            {loadingTrainingMaterials ? (
              <tr>
                <td colSpan={6} className='text-center py-4'>Loading...</td>
              </tr>
            ) : pendingTrainingMaterials.length > 0 ? (
              (showAll ? pendingTrainingMaterials : pendingTrainingMaterials.slice(0, 3)).map((material) => (
                <tr key={material.id} className='hover:bg-gray-50'>
                  <td className='whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 text-center'>{material.author?.name || 'N/A'}</td>
                  <td className='whitespace-nowrap px-6 py-4 text-sm text-gray-600 text-center'>{material.author?.department || 'N/A'}</td>
                  <td className='whitespace-nowrap px-6 py-4 text-sm text-gray-600 text-center'>{material.title}</td>
                  <td className='whitespace-nowrap px-6 py-4 text-sm text-gray-600 text-center'>{new Date(material.submittedDate).toLocaleDateString()}</td>
                  <td className='whitespace-nowrap px-6 py-4 text-sm text-center'>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      material.status === 'PENDING_APPROVAL' ? 'bg-amber-100 text-amber-800' : 
                      material.status === 'PUBLISHED' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800' // Default or other statuses
                    }`}>
                      {material.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className='whitespace-nowrap px-6 py-4 text-sm text-right'>
                    <button
                      onClick={() => router.push(`/main/hr/review/${material.id}`)} // Assuming review page takes material ID
                      className="inline-flex items-center px-3 py-1 border cursor-pointer border-transparent text-sm leading-4 font-medium rounded-md text-white bg-color3 hover:bg-color3/90 focus:outline-none focus:ring-1 focus:ring-offset-2 focus:ring-color3"
                    >
                      Review
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className='text-center py-4 text-gray-500'>No pending training material approvals.</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render different content based on the current module
  const renderContent = () => {
    switch (currentModule) {
      case 'participation':
        return (
          <>
            <div className='flex flex-col gap-4 sm:gap-6'>
              <div className='bg-white p-4 sm:p-6 border-[0.5px] border-gray-300 shadow-md rounded-md w-full'> 
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                  <div>
                    <h1 className='font-semibold text-base mb-1'>Department Participation</h1>
                    <p className='text-xs text-gray-500'>Training participation rates across departments</p>
                  </div>
                  <select
                    value={selectedDivision || ''}
                    onChange={(e) => setSelectedDivision(e.target.value ? Number(e.target.value) : null)}
                    className="w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isLoadingDivisions}
                  >
                    {isLoadingDivisions ? (
                      <option value="">Loading divisions...</option>
                    ) : divisions.length > 0 ? (
                      divisions.map((division) => (
                        <option key={division.id} value={division.id}>
                          {division.name}
                        </option>
                      ))
                    ) : (
                      <option value="">No divisions available</option>
                    )}
                  </select>
                </div>
                <div className='h-[300px] sm:h-[400px] w-full'>
                  {departmentData && departmentData.datasets && departmentData.datasets[0] && departmentData.datasets[0].data ? (
                    <Bar data={departmentData} options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      legend: {
                        display: false
                      }
                    }
                    }} />
                  ) : (
                    <div className="h-full flex flex-col items-center justify-center text-gray-400">
                      <p>No participation data available for this division</p>
                      <p className="text-sm mt-2">This could be due to no users or training activity in these units</p>
                    </div>
                  )}
                </div>
              </div>

              <PendingTrainingMaterialApprovalsSection showAll={true} />
            </div>
          </>
        );

      case 'approvals':
        return (
          <div className='bg-white p-4 sm:p-6 border-[0.5px] border-gray-300 shadow-md rounded-md w-full'> 
            <div className='flex items-center justify-between mb-4'>
              <div>
                <h1 className='font-semibold text-base'>Resource Approvals</h1>
                <p className='text-xs text-gray-500'>Manage and review resource approval requests</p>
              </div>
            </div>
            <div className='overflow-x-auto -mx-4 sm:-mx-6'>
              <div className='inline-block min-w-full align-middle'>
                <div className='overflow-hidden'>
                  <table className='min-w-full divide-y divide-gray-200'>
                    <thead className='bg-gray-50'>
                      <tr>
                        <th scope="col" className='px-3 sm:px-6 py-3 text-left text-xs font-semibold text-gray-600'>Employee</th>
                        <th scope="col" className='px-3 sm:px-6 py-3 text-left text-xs font-semibold text-gray-600'>Department</th>
                        <th scope="col" className='px-3 sm:px-6 py-3 text-left text-xs font-semibold text-gray-600'>Resource</th>
                        <th scope="col" className='px-3 sm:px-6 py-3 text-left text-xs font-semibold text-gray-600'>Date</th>
                        <th scope="col" className='px-3 sm:px-6 py-3 text-left text-xs font-semibold text-gray-600'>Status</th>
                        <th scope="col" className='relative px-3 sm:px-6 py-3'>
                          <span className="sr-only">Action</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className='divide-y divide-gray-200'>
                      {/* pendingApprovals.map((request) => (
                        <tr key={request.id} className='hover:bg-gray-50'>
                          <td className='whitespace-nowrap px-3 sm:px-6 py-4 text-sm font-medium text-gray-900'>{request.name}</td>
                          <td className='whitespace-nowrap px-3 sm:px-6 py-4 text-sm text-gray-600'>{request.department}</td>
                          <td className='whitespace-nowrap px-3 sm:px-6 py-4 text-sm text-gray-600'>{request.resource}</td>
                          <td className='whitespace-nowrap px-3 sm:px-6 py-4 text-sm text-gray-600'>{request.requestDate}</td>
                          <td className='whitespace-nowrap px-3 sm:px-6 py-4 text-sm'>
                            <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800'>
                              {request.status}
                            </span>
                          </td>
                          <td className='whitespace-nowrap px-3 sm:px-6 py-4 text-right text-sm font-medium'>
                            <button 
                              onClick={() => router.push(`/main/hr/review/${request.id}`)}
                              className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-color3 hover:bg-color3/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3"
                            >
                              Review
                            </button>
                          </td>
                        </tr>
                      )) */}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        );

      case 'analytics':
        return (
          <>
            <div className="grid grid-cols-1 gap-4 sm:gap-6">
              <ComplianceMetrics />
            </div>
          </>
        );

      default:
        return (
          <>
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
              {stats.map((stat, index) => (
                <StatCard 
                  key={index} 
                  title={stat.title} 
                  value={stat.value} 
                  trend={stat.trend} 
                  icon={stat.icon}
                  progress={stat.progress}
                  color={stat.color}
                />
              ))}
            </div>

            <div className='mt-4 sm:mt-6'>
              <PendingTrainingMaterialApprovalsSection />
            </div>

            <div className='flex flex-col w-full gap-4 sm:gap-6 mt-4 sm:mt-6'>
              <div className='bg-white p-4 sm:p-6 border-[0.5px] border-gray-300 shadow-md rounded-md w-full'> 
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                  <div>
                    <h1 className='font-semibold text-base mb-1'>Department Participation</h1>
                    <p className='text-xs text-gray-500'>Training participation rates across departments</p>
                  </div>
                  <select
                    value={selectedDivision || ''}
                    onChange={(e) => setSelectedDivision(e.target.value ? Number(e.target.value) : null)}
                    className="w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isLoadingDivisions}
                  >
                    {isLoadingDivisions ? (
                      <option value="">Loading divisions...</option>
                    ) : divisions.length > 0 ? (
                      divisions.map((division) => (
                        <option key={division.id} value={division.id}>
                          {division.name}
                        </option>
                      ))
                    ) : (
                      <option value="">No divisions available</option>
                    )}
                  </select>
                </div>
                <div className='h-[300px] sm:h-[400px] w-full'>
                  {departmentData && departmentData.datasets && departmentData.datasets[0] && departmentData.datasets[0].data ? (
                    <Bar data={departmentData} options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      legend: {
                        display: false
                      }
                    }
                    }} />
                  ) : (
                    <div className="h-full flex flex-col items-center justify-center text-gray-400">
                      <p>No participation data available for this division</p>
                      <p className="text-sm mt-2">This could be due to no users or training activity in these units</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

          
          </>
        );
    }
  };

  return (
    <div className='w-full overflow-x-hidden flex flex-col gap-4'>
      <div className='flex flex-col md:flex-row justify-between items-start md:items-center mb-4 w-full'>
        <div className='w-full md:w-auto'>
          <h1 className='text-xl md:text-2xl font-semibold text-gray-800 truncate'>
            {currentModule ? (
              currentModule.charAt(0).toUpperCase() + currentModule.slice(1)
            ) : 'Dashboard Overview'}
          </h1>
          <p className='text-xs md:text-sm text-gray-500 mt-1 line-clamp-2'>
            {currentModule === 'participation' && 'Monitor department participation and training progress'}
            {currentModule === 'approvals' && 'Review and manage resource approval requests'}
            {currentModule === 'analytics' && 'Analyze performance metrics and compliance data'}
            {!currentModule && 'Track key metrics and performance indicators'}
          </p>
        </div>
      </div>
      
      <div className='w-full overflow-x-hidden'>
        {renderContent()}
      </div>
    </div>
  );
};
export default HRDashboard;
