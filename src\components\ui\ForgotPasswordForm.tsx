'use client';

import React, { useState } from 'react';
import { Mail, ArrowLeft } from 'lucide-react';
import { Button } from './Button'; // Assuming Button component exists
import Loader from '@/components/ui/Loader';

interface ForgotPasswordFormProps {
  onSwitchToLogin: () => void;
}

const ForgotPasswordForm = ({ onSwitchToLogin }: ForgotPasswordFormProps) => {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'An error occurred.');
      }

      // Show success message
      setMessage({ type: 'success', text: data.message });
      setEmail(''); // Clear input on success

    } catch (error) {
      console.error('Forgot Password Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset link. Please try again.';
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6 px-4 py-2"> {/* Adjusted padding for modal context */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          Enter your email address and we&apos;ll send you a link to reset your password.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="forgot-email" className="sr-only">Email address</label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-gray-400" />
            </div>
            <input
              id="forgot-email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Email address"
              disabled={isLoading}
            />
          </div>
        </div>

        {message && (
          <div className={`p-3 rounded-md text-sm ${message.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
            {message.text}
          </div>
        )}

        <div>
          <Button
            type="submit"
            variant="default"
            size="lg"
            disabled={isLoading}
            className="w-full flex justify-center bg-color3 text-white hover:bg-color3/95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600 cursor-pointer"
          >
            {isLoading && <Loader className="mr-2" style={{ width: 20, height: 20 }} />}
            {isLoading ? 'Sending...' : 'Send Reset Link'}
          </Button>
        </div>
      </form>

      <div className="text-center">
        <button
          type="button"
          onClick={onSwitchToLogin}
          className="text-sm text-color3 cursor-pointer hover:text-blue-500 flex items-center justify-center mx-auto"
        >
          <ArrowLeft size={16} className="mr-1" /> Back to Login
        </button>
      </div>
    </div>
  );
};

export default ForgotPasswordForm; 