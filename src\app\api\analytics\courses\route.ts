import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { MaterialStatus, ProgressStatus } from '@prisma/client';
import { z } from 'zod';

// Request Query Schema
const RequestQuerySchema = z.object({
  category: z.string().optional().default('All Categories')
});

// Course Schema
const CourseSchema = z.object({
  name: z.string(),
  started: z.number().int().nonnegative(),
  completed: z.number().int().nonnegative(),
  effectiveness: z.number().min(0).max(100),
  score: z.number().min(0).max(100),
  category: z.string()
});

// Category Schema
const CategorySchema = z.object({
  id: z.number(),
  name: z.string()
});

// Response Schema
const ResponseSchema = z.object({
  courses: z.array(CourseSchema),
  categories: z.array(CategorySchema)
});

// Error Response Schema
const ErrorResponseSchema = z.object({
  error: z.string()
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        ErrorResponseSchema.parse({ error: 'Unauthorized' }), 
        { status: 401 }
      );
    }

    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = RequestQuerySchema.parse({
      category: searchParams.get('category')
    });

    // Get all categories
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        categoryName: true
      }
    });

    // Fetch courses with their progress data and category info
    const courses = await prisma.trainingMaterial.findMany({
      where: {
        status: MaterialStatus.PUBLISHED,
        ...(validatedQuery.category !== 'All Categories' && {
          category: {
            categoryName: validatedQuery.category
          }
        })
      },
      include: {
        category: true,
        trainingParticipants: true,
        userTrainingProgress: true
      }
    });

    // Process the data
    const processedCourses = courses.map(course => {
      const started = course.trainingParticipants.length;
      const completed = course.userTrainingProgress.filter(
        progress => progress.status === ProgressStatus.COMPLETED
      ).length;

      // Calculate average progress percentage
      const progressPercentages = course.userTrainingProgress.map(p => p.progressPercentage);
      const avgProgress = progressPercentages.length > 0
        ? Math.round(progressPercentages.reduce((a, b) => a + b, 0) / progressPercentages.length)
        : 0;

      return CourseSchema.parse({
        name: course.title,
        started,
        completed,
        effectiveness: avgProgress,
        score: Math.round((completed / (started || 1)) * 100),
        category: course.category.categoryName
      });
    });

    // Format categories for response
    const formattedCategories = categories.map(category => 
      CategorySchema.parse({
        id: category.id,
        name: category.categoryName
      })
    );

    // Validate and return the response
    const response = ResponseSchema.parse({
      courses: processedCourses,
      categories: formattedCategories
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching course analytics:', error);
    
    // If it's a Zod error, return the validation issues
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        ErrorResponseSchema.parse({ 
          error: `Validation error: ${error.errors.map(e => e.message).join(', ')}` 
        }), 
        { status: 400 }
      );
    }

    return NextResponse.json(
      ErrorResponseSchema.parse({ error: 'Failed to fetch course analytics' }), 
      { status: 500 }
    );
  }
} 