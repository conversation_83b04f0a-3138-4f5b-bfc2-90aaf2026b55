import React, { useEffect, useState } from 'react';
import { UnitDropdownProps } from '../types';
import styles from '../../styles/customScrollbar.module.css';

interface Division {
  id: number;
  divisionName: string;
  units: {
    id: number;
    unitName: string;
  }[];
}

const divisionColors: { [key: number]: string } = {
  4: 'bg-blue-500',   // Office of the Regional Director
  5: 'bg-green-500',  // Technical Operations
  6: 'bg-purple-500', // Financial and Administrative Services
};

const getDivisionColor = (divisionId: number): string => {
  return divisionColors[divisionId] || 'bg-gray-500';
};

const getDivisionDescription = (divisionId: number): string => {
  switch (divisionId) {
    case 4:
      return 'Regional Planning and Provincial Offices';
    case 5:
      return 'Technical Services and Research Development';
    case 6:
      return 'Administrative Support and Management';
    default:
      return '';
  }
};

export const UnitDropdown: React.FC<UnitDropdownProps> = ({ 
  isOpen, 
  selectedUnit, 
  onUnitChange, 
  onClose 
}) => {
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDivisions = async () => {
      try {
        const response = await fetch('/api/divisions');
        if (!response.ok) throw new Error('Failed to fetch divisions');
        const data = await response.json();
        setDivisions(data.divisions);
      } catch (error) {
        console.error('Error fetching divisions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDivisions();
  }, []);

  if (!isOpen) return null;

  return (
    <div className="absolute right-0 mt-1 w-[320px] bg-white border border-gray-200 rounded-md shadow-lg z-10">
      <div className={`py-1 max-h-[400px] overflow-y-auto ${styles.customScrollbar}`}>
        <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
          <button
            onClick={() => {
              onUnitChange('All Units');
              onClose();
            }}
            className={`block w-full text-left py-1 px-2 text-sm rounded cursor-pointer ${
              selectedUnit === 'All Units'
                ? 'bg-color3 text-white'
                : 'hover:bg-gray-100 text-gray-700'
            }`}
          >
            All Units
          </button>
        </div>

        {isLoading ? (
          <div className="p-4 text-center text-gray-500">Loading...</div>
        ) : (
          divisions.map((division) => (
            <div key={division.id} className="border-b border-gray-200 last:border-b-0">
              <div className="px-3 py-2 bg-gray-100">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getDivisionColor(division.id)}`}></div>
                  <span className="text-sm font-semibold text-gray-700">
                    {division.divisionName}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1 pl-4">
                  {getDivisionDescription(division.id)}
                </p>
              </div>

              <div className="py-1 bg-white">
                {division.units.map((unit) => (
                  <button
                    key={unit.id}
                    onClick={() => {
                      onUnitChange(unit.unitName);
                      onClose();
                    }}
                    className={`block w-full text-left px-6 py-1.5 text-sm truncate group hover:bg-gray-50 cursor-pointer ${
                      selectedUnit === unit.unitName
                        ? 'bg-gray-50 text-color3 font-medium'
                        : 'text-gray-600'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <span className={`w-1 h-1 rounded-full ${
                        selectedUnit === unit.unitName ? 'bg-color3' : 'bg-gray-400 group-hover:bg-gray-500'
                      }`}></span>
                      {unit.unitName}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}; 