import React from 'react';

interface RoleHeaderProps {
  onAddRole: () => void;
}

const RoleHeader: React.FC<RoleHeaderProps> = ({ onAddRole }) => {
  return (
    <div className="flex justify-between items-center mb-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800">Access Control</h1>
        <p className="text-gray-500 text-sm">
          Manage roles, permissions, and access levels for the ShareIT platform.
        </p>
      </div>
      <button
        onClick={onAddRole}
        className="flex items-center gap-2 px-4 py-2 bg-color3 text-white rounded-lg hover:bg-color3/90 transition-colors"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        Add Role
      </button>
    </div>
  );
};

export default RoleHeader; 