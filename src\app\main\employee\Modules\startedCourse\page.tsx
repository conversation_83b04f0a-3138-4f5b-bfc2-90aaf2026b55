'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Navbar } from '../components/Navbar';
import { MaterialsList } from '../components/MaterialsList';
import { MaterialPreview } from '../components/MaterialPreview';
import { MaterialOverview } from '../components/MaterialOverview';
import { TrainingData, TrainingModule, TrainingStep, Material, MaterialType } from '../types/trainingTypes';
import { FiChevronLeft, FiChevronRight, FiChevronDown, FiFile } from 'react-icons/fi';
import { useSession } from 'next-auth/react';
import {
  trackMaterialProgress,
  trackStepCompletion,
  trackCourseProgress,
  calculatePdfProgress,
  persistProgress,
  MaterialProgressData
} from '@/utils/progress';
import {
  startTrainingProgress,
  updateTrainingProgress,
  completeTrainingProgress
} from '@/utils/trainingProgress';
import Loader from '@/components/ui/Loader';

// Mock data for fallback if API call fails
const mockTrainingData: TrainingData = {
  id: '1',
  title: 'Test Course',
  description: 'Testing material preview functionality.',
  modules: [
    {
      id: '1',
      title: 'PDF Test Module',
      description: 'Testing PDF viewer functionality',
      moduleOrder: 1,
      isExpanded: true,
      completed: false,
      steps: [
        {
          id: '1-1',
          title: 'PDF Viewer Test',
          description: 'Testing the PDF viewer component',
          materials: [
            {
              name: 'Sample PDF Document',
              type: 'pdf',
              file: '/materials/1747117607446_eee304e2239902561aef18238d0d9716.pdf',
              active: true,
              viewed: false
            },
            {
              name: 'Documentation',
              type: 'pdf',
              file: '/materials/1747117607446_eee304e2239902561aef18238d0d9716 copy.pdf',
              active: false,
              viewed: false
            }
          ],
          learningObjectives: [
            'Test PDF viewer functionality',
            'Verify page navigation',
            'Check completion tracking'
          ],
          isCompleted: false,
          estimatedTime: '10 minutes'
        }
      ]
    },
    {
      id: '2',
      title: 'YouTube Video Module',
      description: 'Learning through YouTube videos',
      moduleOrder: 2,
      isExpanded: false,
      completed: false,
      steps: [
        {
          id: '2-1',
          title: 'YouTube Video Lesson',
          description: 'Watch this tutorial on the topic',
          materials: [
            {
              name: 'Introduction to React',
              type: 'youtube',
              file: 'https://www.youtube.com/watch?v=SqcY0GlETPk',
              active: false,
              viewed: false
            },
            {
              name: 'React Hooks Tutorial',
              type: 'youtube',
              file: 'https://www.youtube.com/watch?v=TNhaISOUy6Q',
              active: false,
              viewed: false
            }
          ],
          learningObjectives: [
            'Understand the basics of React',
            'Learn about component-based architecture',
            'Explore React Hooks'
          ],
          isCompleted: false,
          estimatedTime: '20 minutes'
        }
      ]
    }
  ],
  totalSteps: 2,
  completedSteps: 0,
  estimatedTotalTime: '15 minutes',
  progress: 0,
  instructor: 'Test Instructor'
};

export default function StartedCourse() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseId = searchParams.get('id');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStepId, setSelectedStepId] = useState<string | null>(null);
  const [openModule, setOpenModule] = useState(0);
  const [trainingData, setTrainingData] = useState<TrainingData | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [numPages, setNumPages] = useState<number | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [materialProgress, setMaterialProgress] = useState<Record<string, number>>({});
  const { data: session } = useSession();
  const [preloadedMaterials, setPreloadedMaterials] = useState<Record<string, boolean>>({});

  // Fetch course data when component mounts
  useEffect(() => {
    async function fetchCourseData() {
      if (!courseId) {
        setError('Course ID is missing');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetch(`/api/training-material/${courseId}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch course details: ${response.statusText}`);
        }

        const data = await response.json();
        
        // Start tracking user progress in userTrainingProgress table
        if (session?.user?.id) {
          console.log(`Starting to track progress for course: ${courseId}`);
          await startTrainingProgress(courseId);
        }

        // Transform API data to match TrainingData format
        // This is a simplified example - adjust according to your actual API response
        const transformedData: TrainingData = {
          id: data.id.toString(),
          title: data.title || 'Untitled Course',
          description: data.description || '',
          modules: data.modules.map((module: any) => ({
            id: module.id.toString(),
            title: module.title || 'Untitled Module',
            description: module.description || '',
            moduleOrder: module.moduleOrder || 0,
            isExpanded: true,
            completed: false,
            steps: [{
              id: `${module.id}-1`,
              title: module.title || 'Untitled Step',
              description: module.description || '',
              materials: module.resources.map((resource: any) => {
                let type: string;
                if (resource.resourceType === 'file') {
                  // Check if it's a video file (mp4, mov)
                  if (resource.fileType?.toLowerCase().includes('mp4') ||
                    resource.fileType?.toLowerCase().includes('mov') ||
                    resource.fileName?.toLowerCase().endsWith('.mp4') ||
                    resource.fileName?.toLowerCase().endsWith('.mov')) {
                    type = 'video';
                  } else {
                    type = resource.fileType || 'document';
                  }
                } else if (resource.resourceType === 'link' && resource.resourceUrl?.includes('youtube.com')) {
                  type = 'youtube';
                } else {
                  type = resource.videoType || 'video';
                }

                let fileUrl = '';
                if (resource.resourceType === 'file' && resource.fileKey) {
                  fileUrl = `/api/files/view?key=${resource.fileKey}`;
                } else if (resource.resourceUrl) {
                  fileUrl = resource.resourceUrl;
                } else if (resource.filePath) {
                  fileUrl = resource.filePath.startsWith('/')
                    ? resource.filePath
                    : `/${resource.filePath}`;
                }

                return {
                  name: resource.fileName || resource.resourceUrl || 'Untitled Resource',
                  type: type as MaterialType,
                  file: fileUrl,
                  active: false,
                  viewed: false
                };
              }),
              learningObjectives: data.objectives?.map((obj: any) => obj.text) || [],
              isCompleted: false,
              estimatedTime: '10 minutes'
            }]
          })),
          totalSteps: data.modules.length,
          completedSteps: 0,
          estimatedTotalTime: `${data.modules.length * 10} minutes`,
          progress: 0,
          instructor: data.trainingParticipants?.find((p: any) => p.isCreator)?.user?.firstName + ' ' +
            data.trainingParticipants?.find((p: any) => p.isCreator)?.user?.lastName || 'Unknown'
        };

        // Set the first material as active
        if (transformedData.modules.length > 0 &&
          transformedData.modules[0].steps.length > 0 &&
          transformedData.modules[0].steps[0].materials.length > 0) {
          transformedData.modules[0].steps[0].materials[0].active = true;
        }

        setTrainingData(transformedData);

        // Set the first step as selected
        if (transformedData.modules.length > 0 && transformedData.modules[0].steps.length > 0) {
          setSelectedStepId(transformedData.modules[0].steps[0].id);
        }

        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setTrainingData(mockTrainingData); // Use mock data as fallback
        setSelectedStepId(mockTrainingData.modules[0].steps[0].id);
        setLoading(false);
      }
    }

    fetchCourseData();
  }, [courseId]);

  // Find current step and material
  const currentStep = selectedStepId && trainingData
    ? trainingData.modules
      .flatMap(module => module.steps)
      .find(step => step.id === selectedStepId) || null
    : null;

  const currentMaterial = currentStep?.materials.find(m => m.active) || null;

  // Handle module toggle
  const handleModuleToggle = useCallback((moduleId: string) => {
    if (!trainingData) return;
    const moduleIndex = trainingData.modules.findIndex(m => m.id === moduleId);
    setOpenModule(moduleIndex === openModule ? -1 : moduleIndex);
  }, [openModule, trainingData]);

  // Handle material selection
  const handleMaterialClick = useCallback((moduleIndex: number, materialIndex: number) => {
    if (!trainingData) return;

    setTrainingData(prev => {
      if (!prev) return prev;

      const updatedModules = [...prev.modules];

      // Deactivate all materials
      updatedModules.forEach(module => {
        module.steps.forEach(step => {
          step.materials.forEach(material => {
            material.active = false;
          });
        });
      });

      // Activate selected material
      const selectedStep = updatedModules[moduleIndex].steps[0]; // Assuming one step per module for now
      selectedStep.materials[materialIndex].active = true;

      return {
        ...prev,
        modules: updatedModules
      };
    });

    setOpenModule(moduleIndex);
    setCurrentPage(1);
    setNumPages(null);
  }, []);

  // Handle step completion
  const handleStepComplete = useCallback((stepId: string) => {
    if (!trainingData) return;

    setTrainingData(prev => {
      if (!prev) return prev;

      // Find the step that should be marked as completed
      let stepToComplete: TrainingStep | null = null;
      let moduleContainingStep: TrainingModule | null = null;

      for (const module of prev.modules) {
        const step = module.steps.find(s => s.id === stepId);
        if (step) {
          stepToComplete = step;
          moduleContainingStep = module;
          break;
        }
      }

      // If step not found or all materials are not viewed, don't mark as completed
      if (!stepToComplete) {
        return prev;
      }

      // Double-check that all materials in the step are viewed
      const allMaterialsViewed = stepToComplete.materials.every(material => material.viewed);

      if (!allMaterialsViewed) {
        return prev;
      }

      const updatedModules = prev.modules.map(module => {
        // Check if this module contains the completed step
        const containsCompletedStep = module.steps.some(step => step.id === stepId);

        // Update the steps
        const updatedSteps = module.steps.map(step =>
          step.id === stepId ? { ...step, isCompleted: true } : step
        );

        // If this module contains the completed step, check if all steps are now completed
        const allStepsCompleted = containsCompletedStep &&
          updatedSteps.every(step => step.isCompleted);

        return {
          ...module,
          steps: updatedSteps,
          completed: allStepsCompleted || module.completed
        };
      });

      const completedSteps = updatedModules
        .flatMap(module => module.steps)
        .filter(step => step.isCompleted)
        .length;

      // Also update completedSteps and progress
      return {
        ...prev,
        modules: updatedModules,
        completedSteps,
        progress: (completedSteps / prev.totalSteps) * 100
      };
    });
  }, [trainingData]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);

    // Store progress for current material
    if (currentMaterial && currentStep && session?.user?.id) {
      const materialId = currentMaterial.file;
      const progress = calculatePdfProgress(page, numPages || 0);

      // Store progress in local state
      setMaterialProgress(prev => ({
        ...prev,
        [materialId]: Math.max(prev[materialId] || 0, progress)
      }));

      // Cache progress in Redis
      trackMaterialProgress({
        userId: session.user.id,
        courseId: courseId || 'unknown',
        moduleId: currentStep.id.split('-')[0],
        stepId: currentStep.id,
        materialId,
        materialName: currentMaterial.name,
        materialType: currentMaterial.type,
        progress,
        lastPosition: page,
        isViewed: progress >= 90, // Consider viewed at 90% progress
      });

      // Mark as viewed if progress is high enough
      // We'll check viewed status instead of auto-calling handleMarkMaterialAsRead
      // to avoid circular dependency
      if (progress >= 90 && !currentMaterial.viewed) {
        setTrainingData(prev => {
          if (!prev) return prev;

          return {
            ...prev,
            modules: prev.modules.map(module => ({
              ...module,
              steps: module.steps.map(step => {
                if (step.id === currentStep.id) {
                  return {
                    ...step,
                    materials: step.materials.map(mat => {
                      if (mat.file === currentMaterial.file) {
                        return { ...mat, viewed: true };
                      }
                      return mat;
                    })
                  };
                }
                return step;
              })
            }))
          };
        });
      }
    }
  }, [currentMaterial, currentStep, numPages, session?.user?.id, courseId]);

  // Calculate progress for material
  const calculateProgressForMaterial = (page: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((page / total) * 100);
  };

  // Function to preload a material
  const preloadMaterial = useCallback((material: Material) => {
    // Check if already preloaded
    if (preloadedMaterials[material.file]) return;
    
    // Mark as preloading
    setPreloadedMaterials(prev => ({
      ...prev,
      [material.file]: true
    }));
    
    // Preload based on type
    if (material.type === 'pdf' || material.type === 'document') {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = material.file;
      link.as = 'fetch';
      document.head.appendChild(link);
      console.log(`Preloading ${material.type}: ${material.name}`);
    } else if (material.type === 'jpg' || material.type === 'jpeg' || material.type === 'png') {
      const img = new Image();
      img.src = material.file;
      console.log(`Preloading image: ${material.name}`);
    } else if (material.type === 'video') {
      const video = document.createElement('video');
      video.preload = 'metadata';
      video.src = material.file;
      console.log(`Preloading video metadata: ${material.name}`);
    }
    // YouTube videos are loaded by the player itself
  }, [preloadedMaterials]);

  // Preload next material when current material changes
  useEffect(() => {
    if (!currentMaterial || !currentStep || !trainingData) return;

    // Find the next material to preload
    const currentModuleIndex = trainingData.modules.findIndex(m => 
      m.steps.some(s => s.id === currentStep.id));
    
    if (currentModuleIndex === -1) return;
    
    const currentModule = trainingData.modules[currentModuleIndex];
    const currentStepIndex = currentModule.steps.findIndex(s => s.id === currentStep.id);
    
    if (currentStepIndex === -1) return;
    
    const stepToCheck = currentModule.steps[currentStepIndex];
    const currentMaterialIndex = stepToCheck.materials.findIndex(m => m.active);
    
    if (currentMaterialIndex === -1) return;
    
    // Check if there's a next material in the same step
    if (currentMaterialIndex < stepToCheck.materials.length - 1) {
      const nextMaterial = stepToCheck.materials[currentMaterialIndex + 1];
      preloadMaterial(nextMaterial);
    } 
    // Otherwise check if there's a next step or module
    else if (currentStepIndex < currentModule.steps.length - 1) {
      const nextStep = currentModule.steps[currentStepIndex + 1];
      if (nextStep.materials.length > 0) {
        preloadMaterial(nextStep.materials[0]);
      }
    } else if (currentModuleIndex < trainingData.modules.length - 1) {
      const nextModule = trainingData.modules[currentModuleIndex + 1];
      if (nextModule.steps.length > 0 && nextModule.steps[0].materials.length > 0) {
        preloadMaterial(nextModule.steps[0].materials[0]);
      }
    }
  }, [currentMaterial, currentStep, trainingData, preloadMaterial]);

  // Navigation: Next Material
  const handleNextMaterial = useCallback(() => {
    if (!currentStep || !trainingData) return;

    // If current material is a video, mark it as viewed before moving on
    if (currentMaterial && 
       (currentMaterial.type === 'video' || currentMaterial.type === 'youtube') && 
       !currentMaterial.viewed && 
       session?.user?.id) {
      
      // Track progress for the skipped video
      const progressData = {
        userId: session.user.id,
        courseId: courseId || 'unknown',
        moduleId: currentStep.id.split('-')[0],
        stepId: currentStep.id,
        materialId: currentMaterial.file,
        materialName: currentMaterial.name,
        materialType: currentMaterial.type,
        progress: 100, // Mark as fully complete when skipped
        isViewed: true,
      };
      
      console.log(`Auto-marking skipped video as completed: ${currentMaterial.name}`);
      trackMaterialProgress(progressData);
    }

    setTrainingData(prev => {
      if (!prev) return prev;

      const stepIdToUpdate = currentStep.id;

      let actualModuleIndex = -1;
      let actualStepIndexInModule = -1;
      let activeMaterialIndexInStep = -1;
      let targetStepMaterialsLength = 0;

      for (let i = 0; i < prev.modules.length; i++) {
        const module = prev.modules[i];
        const stepIdx = module.steps.findIndex(s => s.id === stepIdToUpdate);
        if (stepIdx !== -1) {
          actualModuleIndex = i;
          actualStepIndexInModule = stepIdx;
          const tempTargetStep = module.steps[stepIdx];
          activeMaterialIndexInStep = tempTargetStep.materials.findIndex(m => m.active);
          targetStepMaterialsLength = tempTargetStep.materials.length;
          break;
        }
      }

      if (actualModuleIndex === -1 || actualStepIndexInModule === -1) {
        return prev;
      }

      const currentActiveMaterialName = activeMaterialIndexInStep >= 0 ? prev.modules[actualModuleIndex].steps[actualStepIndexInModule].materials[activeMaterialIndexInStep].name : 'None';

      if (activeMaterialIndexInStep >= 0) {
        const nextMaterialToActivateIndex = activeMaterialIndexInStep + 1;

        if (nextMaterialToActivateIndex < targetStepMaterialsLength) {
          const nextMaterialName = prev.modules[actualModuleIndex].steps[actualStepIndexInModule].materials[nextMaterialToActivateIndex].name;
          const nextMaterialType = prev.modules[actualModuleIndex].steps[actualStepIndexInModule].materials[nextMaterialToActivateIndex].type;
        } else {
        }

        const newModules = prev.modules.map((module, mIdx) => {
          if (mIdx !== actualModuleIndex) return module;

          return {
            ...module,
            steps: module.steps.map((step, sIdx) => {
              if (sIdx !== actualStepIndexInModule) return step;

              return {
                ...step,
                materials: step.materials.map((material, matIdx) => {
                  if (matIdx === activeMaterialIndexInStep) {
                    return { ...material, viewed: true, active: false };
                  }
                  if (matIdx === nextMaterialToActivateIndex && nextMaterialToActivateIndex < step.materials.length) {
                    return { ...material, active: true };
                  }
                  return material;
                })
              };
            })
          };
        });
        return { ...prev, modules: newModules };
      } else {
        return prev;
      }
    });
  }, [currentStep, trainingData, currentMaterial, session?.user?.id, courseId]);

  // Navigation: Previous Material
  const handlePreviousMaterial = useCallback(() => {
    if (!currentStep || !trainingData) return;

    setTrainingData(prev => {
      if (!prev) return prev;

      const stepIdToUpdate = currentStep.id;
      let actualModuleIndex = -1;
      let actualStepIndexInModule = -1;
      let activeMaterialIndexInStep = -1;

      // Find the correct module and step within the `prev` state
      for (let i = 0; i < prev.modules.length; i++) {
        const module = prev.modules[i];
        const stepIdx = module.steps.findIndex(s => s.id === stepIdToUpdate);
        if (stepIdx !== -1) {
          actualModuleIndex = i;
          actualStepIndexInModule = stepIdx;
          activeMaterialIndexInStep = module.steps[stepIdx].materials.findIndex(m => m.active);
          break;
        }
      }

      if (actualModuleIndex === -1 || actualStepIndexInModule === -1) {
        return prev;
      }

      const currentActiveMaterialName = activeMaterialIndexInStep >= 0 ? prev.modules[actualModuleIndex].steps[actualStepIndexInModule].materials[activeMaterialIndexInStep].name : 'None';

      if (activeMaterialIndexInStep > 0) { // Only proceed if not the first material
        const previousMaterialToActivateIndex = activeMaterialIndexInStep - 1;
        const prevMaterialName = prev.modules[actualModuleIndex].steps[actualStepIndexInModule].materials[previousMaterialToActivateIndex].name;
        const prevMaterialType = prev.modules[actualModuleIndex].steps[actualStepIndexInModule].materials[previousMaterialToActivateIndex].type;

        // Perform immutable update
        const newModules = prev.modules.map((module, mIdx) => {
          if (mIdx !== actualModuleIndex) return module;

          return {
            ...module,
            steps: module.steps.map((step, sIdx) => {
              if (sIdx !== actualStepIndexInModule) return step;

              return {
                ...step,
                materials: step.materials.map((material, matIdx) => {
                  if (matIdx === activeMaterialIndexInStep) {
                    // Deactivate current
                    return { ...material, active: false };
                  }
                  if (matIdx === previousMaterialToActivateIndex) {
                    // Activate previous
                    return { ...material, active: true };
                  }
                  return material;
                })
              };
            })
          };
        });
        return { ...prev, modules: newModules };
      } else {
        return prev; // No change if already on first or no active material
      }
    });
  }, [currentStep, trainingData]);

  // Handle navigating to the next module
  const handleNextModule = useCallback((moduleId: string) => {
    if (!trainingData) return;

    const moduleIndex = trainingData.modules.findIndex(m => m.id === moduleId);
    if (moduleIndex >= 0) {
      setOpenModule(moduleIndex);

      // Find the first step and material in the next module
      const nextModule = trainingData.modules[moduleIndex];
      if (nextModule && nextModule.steps.length > 0) {
        const firstStep = nextModule.steps[0];
        setSelectedStepId(firstStep.id);

        // Reset materials and set first material as active
        setTrainingData(prev => {
          if (!prev) return prev;

          const updatedModules = [...prev.modules];

          // Deactivate all materials
          updatedModules.forEach(module => {
            module.steps.forEach(step => {
              step.materials.forEach(material => {
                material.active = false;
              });
            });
          });

          // Activate first material in the selected module
          if (updatedModules[moduleIndex].steps[0].materials.length > 0) {
            updatedModules[moduleIndex].steps[0].materials[0].active = true;
          }

          return {
            ...prev,
            modules: updatedModules
          };
        });

        // Reset current page
        setCurrentPage(1);
        setNumPages(null);
      }
    }
  }, [trainingData]);

  // Log all loaded training materials with their type, title, and file URL
  useEffect(() => {
    if (trainingData && trainingData.modules.length > 0) {
      const allMaterials = trainingData.modules.flatMap(module => module.steps.flatMap(step => step.materials));
      const materialList = allMaterials.map((mat, idx) => `${idx + 1}: [${mat.type.toUpperCase()}] Title: "${mat.name}" URL: ${mat.file}`).join('\n');
    }
  }, [trainingData]);

  // Log which material is currently being viewed
  useEffect(() => {
    if (currentMaterial && trainingData) {
      const allMaterials = trainingData.modules.flatMap(module => module.steps.flatMap(step => step.materials));
      const idx = allMaterials.findIndex(mat => mat === currentMaterial);
      if (idx !== -1) {
        console.log(`Currently viewing material ${idx + 1} of ${allMaterials.length}: ${currentMaterial.name} [${currentMaterial.type}]`);
      }
    }
  }, [currentMaterial, trainingData]);

  // Add this use effect to periodically persist progress
  useEffect(() => {
    if (!session?.user?.id || !courseId) return;

    // Persist progress every 30 seconds
    const persistInterval = setInterval(() => {
      persistProgress({
        type: 'material',
        courseId
      })
        .then(() => {
          // Also update the overall course progress in userTrainingProgress table
          if (trainingData) {
            const currentProgressPercentage = Math.round(trainingData.progress);
            console.log(`Updating overall course progress: ${currentProgressPercentage}%`);
            return updateTrainingProgress(courseId, currentProgressPercentage);
          }
        })
        .catch(() => {});
    }, 30000);

    return () => clearInterval(persistInterval);
  }, [session?.user?.id, courseId, trainingData]);

  const handleMarkMaterialAsRead = useCallback(() => {
    if (!currentMaterial || !currentStep || !trainingData || !session?.user?.id) {
      return;
    }

    console.log(`handleMarkMaterialAsRead called for ${currentMaterial.name} [${currentMaterial.type}]`);

    // Check if this is an image file
    const isImageFile = ['jpg', 'jpeg', 'png'].includes(currentMaterial.type);

    // Track progress in Redis if not already at 100%
    const progressData = {
      userId: session.user.id,
      courseId: courseId || 'unknown',
      moduleId: currentStep.id.split('-')[0],
      stepId: currentStep.id,
      materialId: currentMaterial.file,
      materialName: currentMaterial.name,
      materialType: currentMaterial.type,
      progress: 100, // Mark as fully complete
      isViewed: true,
    };

    console.log(`Caching progress data for ${currentMaterial.name}:`, progressData);

    // Track the material progress
    trackMaterialProgress(progressData);

    // Update the UI state
    setTrainingData(prev => {
      if (!prev) return prev;

      const stepIdToUpdate = currentStep.id;
      let moduleIndexToUpdate = -1;
      let stepIndexInModuleToUpdate = -1;
      let materialIndexToUpdateInStep = -1;

      for (let i = 0; i < prev.modules.length; i++) {
        const sIdx = prev.modules[i].steps.findIndex(s => s.id === stepIdToUpdate);
        if (sIdx !== -1) {
          moduleIndexToUpdate = i;
          stepIndexInModuleToUpdate = sIdx;
          const matIdx = prev.modules[i].steps[sIdx].materials.findIndex(m =>
            m.name === currentMaterial.name && m.file === currentMaterial.file // Match by name and file
          );
          if (matIdx !== -1) {
            materialIndexToUpdateInStep = matIdx;
          }
          break;
        }
      }

      if (moduleIndexToUpdate === -1 || stepIndexInModuleToUpdate === -1 || materialIndexToUpdateInStep === -1) {
        return prev;
      }

      return {
        ...prev,
        modules: prev.modules.map((module, mIdx) => {
          if (mIdx !== moduleIndexToUpdate) return module;
          return {
            ...module,
            steps: module.steps.map((step, sIdx) => {
              if (sIdx !== stepIndexInModuleToUpdate) return step;
              return {
                ...step,
                materials: step.materials.map((material, matIdx) => {
                  if (matIdx === materialIndexToUpdateInStep) {
                    return { ...material, viewed: true };
                  }
                  return material;
                })
              };
            })
          };
        })
      };
    });

    // For image files, we need to ensure the data is properly persisted
    // Add a longer delay for images to ensure Redis has time to store the data
    const persistDelay = isImageFile ? 2000 : 500;

    console.log(`Scheduling persistence with ${persistDelay}ms delay for ${currentMaterial.name}`);

    // Schedule persistence with appropriate delay
    setTimeout(() => {
      console.log(`Executing delayed persistence for ${currentMaterial.name}`);

      persistProgress({
        type: 'material',
        courseId: courseId || undefined,
        isImageFile: isImageFile
      })
        .then(result => {
          console.log(`Persistence result for ${currentMaterial.name}:`, result);

          if (!result || (result.persisted === 0 && isImageFile)) {
            // For image files, if first persistence attempt failed, try again with a longer delay
            console.log(`First persistence attempt for ${currentMaterial.name} didn't persist any data, retrying...`);

            setTimeout(() => {
              console.log(`Executing second persistence attempt for ${currentMaterial.name}`);

              persistProgress({
                type: 'material',
                courseId: courseId || undefined
              })
                .then(secondResult => {
                  console.log(`Second persistence result for ${currentMaterial.name}:`, secondResult);
                })
                .catch(err => {
                  console.error(`Error in second persistence attempt for ${currentMaterial.name}:`, err);
                });
            }, 3000);
          }
        })
        .catch(err => {
          console.error(`Error persisting progress for ${currentMaterial.name}:`, err);
        });
    }, persistDelay);
  }, [currentMaterial, currentStep, trainingData, session?.user?.id, courseId]);

  // Handle special cases for current material, especially for the last item in the array
  useEffect(() => {
    if (!currentMaterial || !currentStep || !session?.user?.id) return;

    // Check if this is the last material in the step
    const idx = currentStep.materials.findIndex(m => m.active);
    const isLastMaterial = idx === currentStep.materials.length - 1;

    // Special handling for image files (jpg, jpeg, png)
    const isImageFile = ['jpg', 'jpeg', 'png'].includes(currentMaterial.type);

    // For the last item in the array or image files, ensure they get properly tracked
    if ((isLastMaterial || isImageFile) && !currentMaterial.viewed) {
      console.log(`Special handling for ${isLastMaterial ? 'last material' : 'image file'}: ${currentMaterial.name}`);

      // For image files, we'll mark them as viewed after a short delay
      // This ensures the image has time to load and trigger the onLoad event
      if (isImageFile) {
        console.log(`Setting up delayed tracking for image: ${currentMaterial.name}`);

        // First, explicitly track the progress in Redis
        const progressData = {
          userId: session.user.id,
          courseId: courseId || 'unknown',
          moduleId: currentStep.id.split('-')[0],
          stepId: currentStep.id,
          materialId: currentMaterial.file,
          materialName: currentMaterial.name,
          materialType: currentMaterial.type,
          progress: 100, // Mark as fully complete
          isViewed: true,
        };

        console.log(`Explicitly caching progress for image file: ${currentMaterial.name}`, progressData);
        trackMaterialProgress(progressData);

        // Then set up a fallback timer to mark as read if the image load event doesn't trigger
        const timer = setTimeout(() => {
          // Double-check it's still not viewed before marking
          if (currentMaterial && !currentMaterial.viewed) {
            console.log(`Fallback: Delayed tracking for image: ${currentMaterial.name}`);
            handleMarkMaterialAsRead();

            // Force an additional persistence after a delay
            setTimeout(() => {
              console.log(`Forcing additional persistence for image: ${currentMaterial.name}`);
              persistProgress({
                type: 'material',
                courseId: courseId || undefined,
                isImageFile: true
              }).catch(err => console.error('Error in forced persistence:', err));
            }, 3000);
          }
        }, 2500); // 2.5 second delay as a fallback

        return () => clearTimeout(timer);
      }
    }
  }, [currentMaterial, currentStep, session?.user?.id, handleMarkMaterialAsRead, courseId]);

  const allStepsCompleted = trainingData ? trainingData.completedSteps === trainingData.totalSteps : false;

  const handleFinishCourse = useCallback(() => {
    // Mark all remaining materials as completed before finishing
    if (session?.user?.id && courseId && trainingData) {
      // First mark all materials as viewed in the UI
      setTrainingData(prev => {
        if (!prev) return prev;
        
        // Create an updated version with all materials marked as viewed
        return {
          ...prev,
          modules: prev.modules.map(module => ({
            ...module,
            completed: true,
            steps: module.steps.map(step => ({
              ...step,
              isCompleted: true,
              materials: step.materials.map(material => ({
                ...material,
                viewed: true
              }))
            }))
          })),
          completedSteps: prev.totalSteps,
          progress: 100
        };
      });
      
      // Then track progress for all unviewed materials
      const allMaterials = trainingData.modules.flatMap(module => 
        module.steps.flatMap(step => 
          step.materials.filter(material => !material.viewed).map(material => ({
            material,
            stepId: step.id,
            moduleId: module.id
          }))
        )
      );
      
      // Create a promise array for all materials that need tracking
      const trackingPromises = allMaterials.map(({ material, stepId, moduleId }) => {
        const progressData = {
          userId: session.user.id,
          courseId: courseId,
          moduleId: moduleId,
          stepId: stepId,
          materialId: material.file,
          materialName: material.name,
          materialType: material.type,
          progress: 100,
          isViewed: true
        };
        
        console.log(`Marking remaining material as completed: ${material.name}`);
        return trackMaterialProgress(progressData);
      });
      
      // Wait for all tracking to complete
      Promise.all(trackingPromises)
        .then(() => {
          console.log(`All ${trackingPromises.length} remaining materials marked as completed`);
          
          // First persist all progress for this course to the database
          return persistProgress({
            type: 'all',
            courseId
          });
        })
        .then(() => {
          // Mark the overall course as completed in userTrainingProgress table
          console.log('Marking course as completed in userTrainingProgress table');
          return completeTrainingProgress(courseId);
        })
        .then(() => {
          // Navigate back or to a completion page
          router.back();
        })
        .catch(err => console.error('Error persisting course progress:', err));
    } else {
      router.back();
    }
  }, [router, session?.user?.id, courseId, trainingData]);

  // Add useEffect to save progress when component unmounts
  useEffect(() => {
    // Save function for clean-up
    return () => {
      // When leaving the page, persist any remaining progress
      if (session?.user?.id && courseId) {
        persistProgress({
          type: 'material',
          courseId
        }).catch(err => console.error('Error persisting progress on unmount:', err));
      }
    };
  }, [session?.user?.id, courseId]);

  return (
    <div className="min-h-screen bg-white">
      {loading ? (
        <div className="flex justify-center items-center h-screen">
          <Loader />
        </div>
      ) : error ? (
        <div className="flex flex-col justify-center items-center h-screen">
          <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg max-w-md">
            <h2 className="text-lg font-medium mb-2">Error loading course</h2>
            <p className="mb-4">{error}</p>
            <button
              onClick={() => router.back()}
              className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md transition-colors"
            >
              Go back to course details
            </button>
          </div>
        </div>
      ) : trainingData ? (
        <>
          <Navbar
            courseTitle={trainingData.title}
            onBack={() => router.back()}
          />

          <div className="flex h-[calc(100vh-4rem)]">
            {/* Left Sidebar */}
            <div className="w-[280px] border-r border-gray-200 bg-white">
              <MaterialsList
                modules={trainingData.modules}
                selectedStep={selectedStepId}
                onStepSelect={setSelectedStepId}
                onModuleToggle={handleModuleToggle}
                onMaterialClick={handleMaterialClick}
                openModule={openModule}
              />
            </div>

            {/* Main Content */}
            <div className="flex-1 bg-gray-50">
              <MaterialPreview
                selectedStep={currentStep}
                currentMaterial={currentMaterial}
                onComplete={handleStepComplete}
                onNextMaterial={handleNextMaterial}
                onPreviousMaterial={handlePreviousMaterial}
                onMarkAsRead={handleMarkMaterialAsRead}
                currentPage={currentPage}
                numPages={numPages || undefined}
                onPageChange={handlePageChange}
                savedProgress={currentMaterial ?
                  materialProgress[`${currentMaterial.name}-${currentMaterial.type}`] || 0 : 0}
              />
            </div>

            {/* Right Sidebar */}
            <div className="w-[280px] border-l border-gray-200 bg-white">
              <MaterialOverview
                currentStep={currentStep}
                trainingData={trainingData}
                currentMaterial={currentMaterial}
                onNextModule={handleNextModule}
                allStepsCompleted={allStepsCompleted}
                onFinishCourse={handleFinishCourse}
              />
            </div>
          </div>
        </>
      ) : null}
    </div>
  );
}
