'use client';

import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Extend NavbarProps to include pagination
export interface NavbarProps {
  courseTitle: string;
  onBack: () => void;
  onPrevPage?: () => void;
  onNextPage?: () => void;
  currentPage?: number;
  totalPages?: number;
  showPagination?: boolean;
}

export const Navbar = ({ courseTitle, onBack, onPrevPage, onNextPage, currentPage, totalPages, showPagination }: NavbarProps) => {
  return (
    <nav className="w-full h-16 border-b border-gray-200 bg-gray-50 mx-auto shadow-sm">
      <div className="h-full max-w-full mx-auto px-6 flex items-center justify-between relative">
        {/* Back button on the left */}
        <div className="absolute left-6 flex items-center gap-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onBack}
                  className="hover:bg-gray-100 p-2"
                >
                  <ArrowLeft className="h-6 w-6 text-gray-900" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>Return to dashboard</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <span className="text-sm text-gray-700 font-medium">Back</span>
        </div>
        
        {/* Centered title */}
        <div className="flex-1 flex text-center items-center justify-center">
          <h1 className="text-xl font-semibold text-gray-900">
            {courseTitle}
          </h1>
        </div>

        {/* Pagination controls on the right */}
        {showPagination && (
          <div className="absolute right-6 flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onPrevPage}
              disabled={!onPrevPage || !currentPage || currentPage <= 1}
              className="hover:bg-gray-100 p-2"
              aria-label="Previous Page"
            >
              <FiChevronLeft className="h-6 w-6 text-gray-900" />
            </Button>
            <span className="text-sm text-gray-700 font-medium">
              {currentPage} / {totalPages || '--'}
            </span>
            <Button
              variant="ghost"
              size="icon"
              onClick={onNextPage}
              disabled={!onNextPage || !currentPage || !totalPages || currentPage >= totalPages}
              className="hover:bg-gray-100 p-2"
              aria-label="Next Page"
            >
              <FiChevronRight className="h-6 w-6 text-gray-900" />
            </Button>
          </div>
        )}
      </div>
    </nav>
  );
}; 