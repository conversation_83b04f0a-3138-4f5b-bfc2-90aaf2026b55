/*
  Warnings:

  - The primary key for the `material_competencies` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `createdBy` on the `temporary_competencies` table. All the data in the column will be lost.
  - You are about to drop the column `reviewedBy` on the `temporary_competencies` table. All the data in the column will be lost.
  - You are about to drop the `moduleFiles` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `objectives` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `training_material` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `userFileProgress` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[training_material_id,competency_id,temporary_competency_id]` on the table `material_competencies` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "materialApprovals" DROP CONSTRAINT "materialApprovals_trainingMaterialId_fkey";

-- DropForeignKey
ALTER TABLE "materialStatusLogs" DROP CONSTRAINT "materialStatusLogs_trainingMaterialId_fkey";

-- DropForeignKey
ALTER TABLE "material_competencies" DROP CONSTRAINT "material_competencies_training_material_id_fkey";

-- DropForeignKey
ALTER TABLE "moduleFiles" DROP CONSTRAINT "moduleFiles_moduleId_fkey";

-- DropForeignKey
ALTER TABLE "modules" DROP CONSTRAINT "modules_trainingMaterialId_fkey";

-- DropForeignKey
ALTER TABLE "objectives" DROP CONSTRAINT "objectives_trainingMaterialId_fkey";

-- DropForeignKey
ALTER TABLE "temporary_competencies" DROP CONSTRAINT "temporary_competencies_createdBy_fkey";

-- DropForeignKey
ALTER TABLE "temporary_competencies" DROP CONSTRAINT "temporary_competencies_reviewedBy_fkey";

-- DropForeignKey
ALTER TABLE "training_material" DROP CONSTRAINT "training_material_categoryId_fkey";

-- DropForeignKey
ALTER TABLE "training_participants" DROP CONSTRAINT "training_participants_trainingMaterialId_fkey";

-- DropForeignKey
ALTER TABLE "userFileProgress" DROP CONSTRAINT "userFileProgress_moduleFileId_fkey";

-- DropForeignKey
ALTER TABLE "userFileProgress" DROP CONSTRAINT "userFileProgress_userId_fkey";

-- DropForeignKey
ALTER TABLE "userTrainingProgress" DROP CONSTRAINT "userTrainingProgress_trainingMaterialId_fkey";

-- AlterTable
ALTER TABLE "material_competencies" DROP CONSTRAINT "material_competencies_pkey",
ADD COLUMN     "id" SERIAL NOT NULL,
ADD COLUMN     "temporary_competency_id" INTEGER,
ALTER COLUMN "competency_id" DROP NOT NULL,
ADD CONSTRAINT "material_competencies_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "temporary_competencies" DROP COLUMN "createdBy",
DROP COLUMN "reviewedBy",
ADD COLUMN     "createdByUserId" INTEGER,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "reviewedByUserId" INTEGER,
ALTER COLUMN "status" SET DEFAULT 'PENDING';

-- DropTable
DROP TABLE "moduleFiles";

-- DropTable
DROP TABLE "objectives";

-- DropTable
DROP TABLE "training_material";

-- DropTable
DROP TABLE "userFileProgress";

-- CreateTable
CREATE TABLE "TrainingMaterial" (
    "id" SERIAL NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "categoryId" INTEGER NOT NULL,
    "status" "MaterialStatus" NOT NULL DEFAULT 'DRAFT',
    "publishedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TrainingMaterial_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Objective" (
    "id" SERIAL NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "text" TEXT NOT NULL,
    "displayOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Objective_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrainingResource" (
    "id" SERIAL NOT NULL,
    "fileKey" VARCHAR(512) NOT NULL,
    "fileType" VARCHAR(50) NOT NULL,
    "fileName" VARCHAR(255),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TrainingResource_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserFileProgress" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "progress" INTEGER NOT NULL DEFAULT 0,
    "lastPage" INTEGER,
    "lastTimestamp" INTEGER,
    "status" "ProgressStatus" NOT NULL DEFAULT 'NOT_STARTED',
    "completedAt" TIMESTAMP(3),
    "lastAccessedAt" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "resourceId" INTEGER NOT NULL,

    CONSTRAINT "UserFileProgress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_ModuleResources" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_ModuleResources_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "Objective_trainingMaterialId_idx" ON "Objective"("trainingMaterialId");

-- CreateIndex
CREATE UNIQUE INDEX "Objective_trainingMaterialId_displayOrder_key" ON "Objective"("trainingMaterialId", "displayOrder");

-- CreateIndex
CREATE UNIQUE INDEX "TrainingResource_fileKey_key" ON "TrainingResource"("fileKey");

-- CreateIndex
CREATE UNIQUE INDEX "UserFileProgress_userId_resourceId_key" ON "UserFileProgress"("userId", "resourceId");

-- CreateIndex
CREATE INDEX "_ModuleResources_B_index" ON "_ModuleResources"("B");

-- CreateIndex
CREATE UNIQUE INDEX "material_competencies_training_material_id_competency_id_te_key" ON "material_competencies"("training_material_id", "competency_id", "temporary_competency_id");

-- AddForeignKey
ALTER TABLE "temporary_competencies" ADD CONSTRAINT "temporary_competencies_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "temporary_competencies" ADD CONSTRAINT "temporary_competencies_reviewedByUserId_fkey" FOREIGN KEY ("reviewedByUserId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrainingMaterial" ADD CONSTRAINT "TrainingMaterial_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Objective" ADD CONSTRAINT "Objective_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "material_competencies" ADD CONSTRAINT "material_competencies_training_material_id_fkey" FOREIGN KEY ("training_material_id") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "material_competencies" ADD CONSTRAINT "material_competencies_temporary_competency_id_fkey" FOREIGN KEY ("temporary_competency_id") REFERENCES "temporary_competencies"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "training_participants" ADD CONSTRAINT "training_participants_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "modules" ADD CONSTRAINT "modules_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userTrainingProgress" ADD CONSTRAINT "userTrainingProgress_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserFileProgress" ADD CONSTRAINT "UserFileProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserFileProgress" ADD CONSTRAINT "UserFileProgress_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "TrainingResource"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "materialApprovals" ADD CONSTRAINT "materialApprovals_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "materialStatusLogs" ADD CONSTRAINT "materialStatusLogs_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "TrainingMaterial"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ModuleResources" ADD CONSTRAINT "_ModuleResources_A_fkey" FOREIGN KEY ("A") REFERENCES "modules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ModuleResources" ADD CONSTRAINT "_ModuleResources_B_fkey" FOREIGN KEY ("B") REFERENCES "TrainingResource"("id") ON DELETE CASCADE ON UPDATE CASCADE;
