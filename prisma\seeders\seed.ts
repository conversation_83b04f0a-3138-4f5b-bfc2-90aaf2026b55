import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcrypt'

const prisma = new PrismaClient()

async function main() {
  // Clear existing data
  await prisma.userFileProgress.deleteMany()
  await prisma.userModuleProgress.deleteMany()
  await prisma.userTrainingProgress.deleteMany()
  await prisma.trainingParticipant.deleteMany()
  await prisma.materialStatusLog.deleteMany()
  await prisma.materialApproval.deleteMany()
  await prisma.materialCompetency.deleteMany()
  await prisma.trainingResource.deleteMany()
  await prisma.module.deleteMany()
  await prisma.trainingMaterial.deleteMany()
  await prisma.category.deleteMany()
  await prisma.userPermission.deleteMany()
  await prisma.rolePermission.deleteMany()
  await prisma.permission.deleteMany()
  await prisma.userSearch.deleteMany()
  await prisma.userSession.deleteMany()
  await prisma.user.deleteMany()
  await prisma.unit.deleteMany()
  await prisma.division.deleteMany()
  await prisma.role.deleteMany()
  await prisma.category.deleteMany()
  await prisma.competency.deleteMany()

  //Create Competencies
  await Promise.all([
    prisma.competency.create({
      data: {
        competencyName: 'Data Science',
      },
    }),
    prisma.competency.create({
      data: {
        competencyName: 'Data Analytics',
      },
    }),
    prisma.competency.create({
      data: {
        competencyName: 'Red Hat',
      },
    }),
    prisma.competency.create({
      data: {
        competencyName: 'Graphic Design',
      },
    }),
    prisma.competency.create({
      data: {
        competencyName: 'Cyber Security',
      },
    }),
  ])
  // Create Divisions
  const divisions = await Promise.all([
    prisma.division.create({
      data: {
        divisionName: 'Office of the Regional Director',
      },
    }),
    prisma.division.create({
      data: {
        divisionName: 'Financial and Administrative Services',
      },
    }),
    prisma.division.create({
      data: {
        divisionName: 'Technical Operations',
      },
    }),
  ])

  // Create Units
  const units = await Promise.all([
    // ORD units
    prisma.unit.create({
      data: {
        unitName: 'PLANNING',
        divisionId: divisions[0].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PSTO ALBAY',
        divisionId: divisions[0].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PSTO SORSOGON',
        divisionId: divisions[0].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PSTO CATANDUANES',
        divisionId: divisions[0].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PSTO MASBATE',
        divisionId: divisions[0].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PSTO CAMARINES SUR',
        divisionId: divisions[0].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PSTO CAMARINES NORTE',
        divisionId: divisions[0].id,
      },
    }),
    

    // FAS units
    prisma.unit.create({
      data: {
        unitName: 'HUMAN RESOURCES MANAGEMENT',
        divisionId: divisions[1].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'PROCUREMENT UNIT',
        divisionId: divisions[1].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'ACCOUNTING UNIT',
        divisionId: divisions[1].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'SUPPLY AND PROPERTY UNIT',
        divisionId: divisions[1].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'BUDGET UNIT',
        divisionId: divisions[1].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'GENERAL SUPPORT SERVICES',
        divisionId: divisions[1].id,
      },
    }),
    //TO units
    
    prisma.unit.create({
      data: {
        unitName: 'RSTL',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'MIISU',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'S&T PROMO',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'RPMO',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'CEST/GRIND',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'MONITORING AND EVALUATION',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'GAD/QMS/DRRM/COMPLIANCES',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({  
      data: {
        unitName: 'SMART & SUSTAINABLE COMMUNITIES',
        divisionId: divisions[2].id,
      },
    }),
    prisma.unit.create({
      data: {
        unitName: 'S&T SCHOLARSHIP UNIT',
        divisionId: divisions[2].id,
      },
    }),
  ])  

  // Create default roles
  const roles = await Promise.all([
    prisma.role.create({
      data: {
        roleName: 'EMPLOYEE',
        description: 'Regular Staff Member',
      },
    }),
    prisma.role.create({
        data: {
            roleName: 'HR_ADMIN',
            description: 'Human Resource Administrator',
        },
    }),
    prisma.role.create({
        data: {
            roleName: 'TRAINING_UNIT',
            description: 'Training Unit Administrator',
        },
    }),
    prisma.role.create({
        data: {
          roleName: 'IT_ADMIN',
          description: 'System Administrator',
        },
      }),
  ])
  
  //Create Categories
  await Promise.all([
    prisma.category.create({
      data: {
        categoryName: 'Training',
      },
    }),
    prisma.category.create({
      data: {
        categoryName: 'Symposia',
      },
    }),
    prisma.category.create({
      data: {
        categoryName: 'Continuing Professional Education (CPE)',
      },
    }),
  ])

  // Create Users
  const hashedPassword = await bcrypt.hash('password', 10)
  
  await Promise.all([
    prisma.user.create({
      data: {
        unitId: units[2].id, 
        roleId: roles[0].id,
        firstName: 'John',
        lastName: 'Amin',
        password: hashedPassword,
        email: '<EMAIL>',
        employeeType: 'PERMANENT',
      },
    }),
    prisma.user.create({
      data: {
        unitId: units[3].id,
        roleId: roles[1].id, 
        firstName: 'Guirald',
        lastName: 'Escober',
        password: hashedPassword,
        email: '<EMAIL>',
        employeeType: 'PERMANENT',
      },
    }),
    prisma.user.create({
      data: {
        unitId: units[3].id, 
        roleId: roles[1].id,
        firstName: 'Kenneth',
        lastName: 'Espela',
        password: hashedPassword,
        email: '<EMAIL>',
        employeeType: 'PERMANENT',
      },
    }),
    prisma.user.create({
      data: {
        unitId: units[3].id,
        roleId: roles[1].id,
        firstName: 'Jamil',
        lastName: 'Cervano',
        password: hashedPassword,
        email: '<EMAIL>',
        employeeType: 'PERMANENT',
      },
    }),
  ])
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
