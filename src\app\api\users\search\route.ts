import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const searchQuerySchema = z.object({
  searchTerm: z.string().min(2, 'Search term must be at least 2 characters long').optional().default('')
});

export async function GET(request: NextRequest) {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get the current user's ID
    const currentUserId = parseInt(session.user.id.toString(), 10);

    const searchParams = request.nextUrl.searchParams;
    const queryParams = {
      searchTerm: searchParams.get('searchTerm')
    };

    const validationResult = searchQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { searchTerm } = validationResult.data;

    if (searchTerm.length < 2) {
      return NextResponse.json({ users: [] });
    }

    const users = await prisma.user.findMany({
      where: {
        OR: [
          { firstName: { contains: searchTerm, mode: 'insensitive' } },
          { lastName: { contains: searchTerm, mode: 'insensitive' } },
          { email: { contains: searchTerm, mode: 'insensitive' } },
        ],
        // Exclude the current user
        id: { not: currentUserId }
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        unit: {
          select: {
            unitName: true
          }
        },
      },
      take: 10, 
    });

    const formattedUsers = users.map(user => ({
      id: user.id.toString(),
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      unit: user.unit.unitName
    }));

    return NextResponse.json({ users: formattedUsers });
  } catch (error) {
    console.error('Error searching users:', error);
    return NextResponse.json(
      { error: 'Failed to search users' },
      { status: 500 }
    );
  }
} 