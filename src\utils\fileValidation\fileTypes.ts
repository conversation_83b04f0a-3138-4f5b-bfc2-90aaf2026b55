// File type definitions and constants
export type AllowedMimeType = 
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.ms-powerpoint'
  | 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  | 'text/plain'
  | 'image/jpeg'
  | 'image/png'
  | 'image/gif'
  | 'image/svg+xml'
  | 'video/mp4'
  | 'video/webm'
  | 'video/quicktime'
  | 'audio/mpeg'
  | 'audio/wav'
  | 'audio/ogg';

export const ALLOWED_MIME_TYPES: Record<AllowedMimeType, string[]> = {
  // Documents
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-powerpoint': ['.ppt'],
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
  'text/plain': ['.txt'],
  
  // Images
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/svg+xml': ['.svg'],
  
  // Videos
  'video/mp4': ['.mp4'],
  'video/webm': ['.webm'],
  'video/quicktime': ['.mov'],
  
  // Audio
  'audio/mpeg': ['.mp3'],
  'audio/wav': ['.wav'],
  'audio/ogg': ['.ogg'],
} as const;

// Size limits in bytes
export const MAX_FILE_SIZE = {
  module: 300 * 1024 * 1024, // 300MB
  certificate: 10 * 1024 * 1024, // 10MB
  assessment: 10 * 1024 * 1024 // 10MB
} as const;

// Maximum number of files per upload
export const MAX_FILES_PER_UPLOAD = 5;

/**
 * Generates an accept string for file input elements based on allowed MIME types
 * @param types Optional array of MIME types to include (defaults to all allowed types)
 * @returns A string suitable for the accept attribute of an input element
 */
export const generateAcceptString = (types?: AllowedMimeType[]): string => {
  if (!types || types.length === 0) {
    // Collect all extensions from all MIME types
    const allExtensions = Object.values(ALLOWED_MIME_TYPES).flat();
    return allExtensions.join(',');
  }
  
  // Collect extensions only for the specified MIME types
  const extensions = types.flatMap(type => ALLOWED_MIME_TYPES[type] || []);
  return extensions.join(',');
}; 