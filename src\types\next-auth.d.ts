import { DefaultSession, DefaultUser } from "next-auth";
import { DefaultJWT } from "next-auth/jwt";

// Extend the default User type recognized by authorize etc.
// This must match the object structure returned by authorize
interface I<PERSON><PERSON> extends DefaultUser {
  role: string;
  remember?: boolean; // Add remember flag from authorize
  // id is already string in De<PERSON><PERSON><PERSON><PERSON> after our authorize fix
}

declare module "next-auth" {
  // Extend session to include role and ensure user id is string
  interface Session {
    user: {
      id: string; // Ensure ID is string in session user
      role: string;
    } & DefaultSession["user"]; // Keep existing fields like name, email, image
  }

  // Removed redundant interface User extends IUser {}
  // interface User extends IUser {}
}

declare module "next-auth/jwt" {
  // Extend JWT token to include role and id
  interface JWT extends DefaultJWT {
    role: string;
    id: string;
    remember?: boolean; // Add remember flag
    exp?: number;       // Ensure exp is defined as a number
  }
} 