import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { MaterialStatus, Prisma } from '@prisma/client';

const STATUS_PRIORITY: { [key in MaterialStatus]: number } = {
  PENDING_APPROVAL: 0,
  PUBLISHED: 1,
  DRAFT: 2,
  ARCHIVED: 3
};

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '5');
    const searchQuery = searchParams.get('search') || '';
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'newest';
    const category = searchParams.get('category');
    const type = searchParams.get('type');
    const competencies = searchParams.get('competencies')?.split(',') || [];

    const where: Prisma.TrainingMaterialWhereInput = {
      ...(searchQuery ? {
        OR: [
          { title: { contains: searchQuery, mode: 'insensitive' } },
          { description: { contains: searchQuery, mode: 'insensitive' } }
        ]
      } : {}),
      ...(status && status !== 'all' ? { status: status as MaterialStatus } : {}),
      ...(category ? {
        category: {
          categoryName: category
        }
      } : {}),
      ...(type && type !== 'Both' ? {
        modules: {
          some: {
            ModuleResources: {
              some: {
                TrainingResource: {
                  resourceType: type === 'Video Course' ? 'link' : 'file'
                }
              }
            }
          }
        }
      } : {}),
      ...(competencies.length > 0 ? {
        materialCompetencies: {
          some: {
            OR: [
              {
                competency: {
                  competencyName: {
                    in: competencies
                  }
                }
              },
              {
                temporaryCompetency: {
                  competencyName: {
                    in: competencies
                  }
                }
              }
            ]
          }
        }
      } : {})
    };

    const totalItems = await prisma.trainingMaterial.count({ where });
    const totalPages = Math.ceil(totalItems / limit);
    const skip = (page - 1) * limit;
    const materials = await prisma.trainingMaterial.findMany({
      where,
      include: {
        category: true,
        trainingParticipants: {
          where: { isCreator: true },
          take: 1,
          include: {
            user: {
              include: {
                unit: true
              }
            }
          }
        },
        materialCompetencies: {
          include: {
            competency: true,
            temporaryCompetency: true
          }
        },
        modules: {
          include: {
            ModuleResources: {
              include: {
                TrainingResource: true
              }
            }
          }
        }
      },
      orderBy: [
        {
          status: 'asc'
        },
        sortBy === 'newest'
          ? { createdAt: 'desc' }
          : { createdAt: 'asc' }
      ],
      skip,
      take: limit
    });

    const formattedMaterials = materials.map(material => {
      const creator = material.trainingParticipants[0];
      const officialTags = (material.materialCompetencies || [])
        .filter((mc: any) => mc.competency)
        .map((mc: any) => mc.competency.competencyName);
      const temporaryTags = (material.materialCompetencies || [])
        .filter((mc: any) => mc.temporaryCompetency)
        .map((mc: any) => mc.temporaryCompetency.competencyName);
      // Determine material type based on ModuleResources
      let hasVideo = false;
      let hasDocument = false;
      for (const module of material.modules) {
        for (const mr of module.ModuleResources) {
          if (mr.TrainingResource.resourceType === 'link') hasVideo = true;
          if (mr.TrainingResource.resourceType === 'file') hasDocument = true;
        }
      }
      const materialType = hasVideo && hasDocument ? 'BOTH' :
        hasVideo ? 'VIDEO' :
        hasDocument ? 'DOCUMENT' : null;
      return {
        id: material.id,
        title: material.title,
        description: material.description,
        officialTags,
        temporaryTags,
        status: material.status,
        submittedDate: material.createdAt,
        category: material.category.categoryName,
        materialType,
        author: creator ? {
          id: creator.user.id,
          name: `${creator.user.firstName} ${creator.user.lastName}`,
          department: creator.user.unit.unitName
        } : null
      };
    });

    return NextResponse.json({
      materials: formattedMaterials,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch training materials' },
      { status: 500 }
    );
  }
}