import React from 'react';

interface TagChipProps {
  text: string;
  type: 'official' | 'temporary';
  className?: string;
}

const TagChip: React.FC<TagChipProps> = ({ text, type, className = '' }) => {
  const baseStyle = 'inline-block px-2.5 py-0.5 rounded-full text-xs font-medium';
  
  // Define styles based on type
  const typeStyle = type === 'official' 
    ? 'bg-blue-100 text-blue-800 border border-blue-200' // Style for official tags
    : 'bg-gray-100 text-gray-800 border border-gray-200'; // Style for temporary tags

  return (
    <span className={`${baseStyle} ${typeStyle} ${className}`}>
      {text}
    </span>
  );
};

export default TagChip;
