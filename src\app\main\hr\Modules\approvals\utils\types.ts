'use client';

import { ElementType } from 'react';
import { MaterialStatus } from '@prisma/client';

export type Status = MaterialStatus;

export interface StatCardProps {
  title: string;
  value: string | number;
  icon: ElementType;
  trend?: number;
  subtitle?: string;
  progress?: number;
}

export interface Participant {
  name: string;
  userId: number;
  hasAssessment: boolean;
  hasCertification: boolean;
  assessmentFormKey?: string | null;
  certificateKey?: string | null;
}

export interface PendingApproval {
  id: number;
  title: string;
  officialTags: string[];
  temporaryTags: string[];
  status: Status;
  submittedDate: string;
  category: string;
  author: {
    id: number;
    name: string;
    department: string;
  } | null;
  participants?: Participant[];
}

export interface StatusBadgeProps {
  status: Status;
}

export interface TimeFilterProps {
  timeFilter: string;
  isFilterOpen: boolean;
  filterRef: React.RefObject<HTMLDivElement | null>;
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleFilterChange: (filter: string) => void;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  indexOfFirstItem: number;
  indexOfLastItem: number;
  handlePageChange: (page: number) => void;
}

export interface PublicationRowProps {
  item: PendingApproval;
  searchQuery: string;
  onReview: (item: PendingApproval) => void;
  isCertification?: boolean;
}

export interface CertificationForm {
  id: string;
  title: string;
  description: string;
  status: Status;
  submittedDate: string;
  dueDate: string;
  department: string;
  type: 'certification' | 'form';
  author: string;
  lastActivity: string;
}

export interface CertificationFormRowProps {
  item: CertificationForm;
  searchQuery: string;
  onReview: (item: CertificationForm) => void;
}