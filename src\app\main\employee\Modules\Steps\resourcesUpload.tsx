"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import FileUploader from "@/components/ui/FileUploader"
import FileList from "@/components/ui/FileList"
import { Button } from "@/components/ui/Button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Trash2, ChevronDown, LinkIcon, AlertCircle, Play, Youtube, X } from "lucide-react"
import { toast } from "sonner"
// Import shared types
import { FormData, Module, FileData } from '../types';
import { validateFiles, MAX_FILES_PER_UPLOAD } from '@/utils/fileValidation';

const MAX_FILE_SIZE_BYTES = 300 * 1024 * 1024; 
// Max resources per module
const MAX_RESOURCES_PER_MODULE = MAX_FILES_PER_UPLOAD;

// Regex for basic URL validation
const URL_REGEX = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/i;

// Re-add ModulesUploadStepProps interface using imported FormData
interface ModulesUploadStepProps {
  formData: FormData; // Uses imported FormData
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
}

// Function to validate a single URL
const validateUrl = (url: string): boolean => {
  if (!url) return false; 
  return URL_REGEX.test(url);
};

// YouTube regex - more specific than general URL regex
const YOUTUBE_REGEX = /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=)|youtu\.be\/)([\w-]{11})(?:\S+)?$/;

// Function to extract YouTube video ID
const extractYouTubeVideoId = (url: string): string | null => {
  const match = url.match(YOUTUBE_REGEX);
  return match && match[1] ? match[1] : null;
};

// YouTube preview component
const YouTubePreview = ({ videoId, onClose }: { videoId: string, onClose: () => void }) => {
  return (
    <div className="relative mt-2 rounded-md overflow-hidden">
      <div className="aspect-video bg-black rounded-md">
        <iframe 
          src={`https://www.youtube.com/embed/${videoId}`}
          className="w-full h-full"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      </div>
      <button 
        onClick={onClose}
        className="absolute top-2 right-2 bg-black/50 text-white p-1 rounded-full hover:bg-black/80 transition-colors"
        aria-label="Close preview"
      >
        <X size={16} />
      </button>
    </div>
  );
};

// Resource link component
const ResourceLink = ({ 
  resource, 
  moduleIndex, 
  resourceIndex, 
  updateResourceInfo, 
  removeResource,
  linkErrors
}: { 
  resource: FileData, 
  moduleIndex: number, 
  resourceIndex: number,
  updateResourceInfo: (moduleIndex: number, resourceIndex: number, field: 'link' | 'description', value: string) => void,
  removeResource: (moduleIndex: number, resourceIndex: number) => void,
  linkErrors: Record<string, string | null>
}) => {
  const [showPreview, setShowPreview] = useState(false);
  
  // Detect YouTube video when link changes
  useEffect(() => {
    if (resource.link) {
      const videoId = extractYouTubeVideoId(resource.link);
      if (videoId) {
        // It's a YouTube video
        if (resource.videoType !== 'youtube' || resource.videoId !== videoId) {
          // Only update if changed (to avoid unnecessary renders)
          resource.videoType = 'youtube';
          resource.videoId = videoId;
        }
      } else {
        // Not a YouTube video - clear video type and ID
        resource.videoType = undefined;
        resource.videoId = undefined;
      }
    }
  }, [resource]);
  
  const handleLinkChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    updateResourceInfo(moduleIndex, resourceIndex, "link", value);
    
    // Immediate validation when the link is entered
    if (value.trim() !== '') {
      const videoId = extractYouTubeVideoId(value);
      if (!videoId && value.match(/youtube\.com|youtu\.be/)) {
        // It looks like a YouTube URL but doesn't match the format
        updateResourceInfo(moduleIndex, resourceIndex, "link", value);
      }
    }
  };
  
  return (
    <div className="flex-1 min-w-0">
      <div className="flex items-center gap-2">
        <Youtube className="w-4 h-4 text-red-500 flex-shrink-0" />
        <Input 
          type="url"
          value={resource.link || ''}
          onChange={handleLinkChange}
          onBlur={() => {
            // Validate on blur to ensure validation happens immediately after text entry
            if (resource.link && resource.link.trim() !== '') {
              const videoId = extractYouTubeVideoId(resource.link);
              if (!videoId) {
                updateResourceInfo(moduleIndex, resourceIndex, "link", resource.link);
              }
            }
          }}
          placeholder="Enter YouTube URL (Required)"
          className={`w-full flex-1 min-w-0 ${linkErrors[resource.id] ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
          required
        />
        
        {/* Preview button for YouTube videos */}
        {resource.videoType === 'youtube' && resource.videoId && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => setShowPreview(!showPreview)}
            className="text-gray-500 hover:text-blue-500 cursor-pointer flex-shrink-0"
            title={showPreview ? "Hide preview" : "Show preview"}
          >
            <Play className="w-4 h-4" />
          </Button>
        )}
        
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={() => removeResource(moduleIndex, resourceIndex)}
          className="text-red-500 cursor-pointer flex-shrink-0"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
      
      {/* Display error message */}
      {linkErrors[resource.id] && (
        <div className="flex items-center mt-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3 mr-1" /> 
          {linkErrors[resource.id]}
        </div>
      )}
      
      {/* Success message for valid YouTube links */}
      {resource.videoType === 'youtube' && resource.videoId && !linkErrors[resource.id] && (
        <div className="flex items-center mt-1 text-xs text-green-600">
          <Youtube className="w-3 h-3 mr-1" />
          YouTube video detected
        </div>
      )}
      
      {/* YouTube preview */}
      {showPreview && resource.videoId && (
        <YouTubePreview 
          videoId={resource.videoId} 
          onClose={() => setShowPreview(false)} 
        />
      )}
    </div>
  );
};

export default function ResourcesUpload({ formData, updateFormData, nextStep, prevStep }: ModulesUploadStepProps) {
  const [modules, setModules] = useState<Module[]>(
    formData.modules || [
      { id: 1, title: "", files: [], isOpen: true }
    ]
  )
  // Add state to track validation errors for links
  const [linkErrors, setLinkErrors] = useState<Record<string, string | null>>({}); // { resource.id: errorMessage }

  const addModule = () => {
    const closedModules = modules.map(module => ({ ...module, isOpen: false }));
    setModules([
      ...closedModules,
      {
        id: Date.now(), 
        title: "",
        files: [],
        isOpen: true
      }
    ])
  }

  const removeModule = (moduleIndex: number) => {
    const newModules = modules.filter((_, i) => i !== moduleIndex)
    setModules(newModules)
    updateFormData({ modules: newModules })
  }

  const updateModuleTitle = (moduleIndex: number, title: string) => {
    const newModules = [...modules]
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      title
    }
    setModules(newModules)
    updateFormData({ modules: newModules })
  }

  const toggleModule = (moduleIndex: number) => {
    const newModules = [...modules]
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      isOpen: !newModules[moduleIndex].isOpen
    }
    setModules(newModules)
  }

  const handleModuleFileUpload = (moduleIndex: number, selectedFiles: File[]) => {
    // Validate files first
    const validationError = validateFiles(selectedFiles);
    if (validationError) {
      toast.error(validationError.message);
      return;
    }

    // Check if adding these files would exceed the maximum resources per module
    const currentModule = modules[moduleIndex];
    if (currentModule.files.length + selectedFiles.length > MAX_RESOURCES_PER_MODULE) {
      toast.error(`Maximum ${MAX_RESOURCES_PER_MODULE} resources allowed per module`);
      return;
    }

    // Add the files to the module
    const newFiles = selectedFiles.map(file => ({
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type: 'file',
      description: '',
      file: file
    }));

    const updatedModules = [...modules];
    updatedModules[moduleIndex] = {
      ...updatedModules[moduleIndex],
      files: [...updatedModules[moduleIndex].files, ...newFiles]
    };

    setModules(updatedModules);
    updateFormData({ modules: updatedModules });
  }

  const handleAddLink = (moduleIndex: number) => {
    const currentModule = modules[moduleIndex];
    if (currentModule.files.length >= MAX_RESOURCES_PER_MODULE) {
      alert(`You can only add up to ${MAX_RESOURCES_PER_MODULE} resources per module.`);
      return;
    }

    const newModules = [...modules];
    const newResourceId = crypto.randomUUID(); // Get the ID of the new resource
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      files: [
        ...newModules[moduleIndex].files,
        { 
          id: newResourceId, // Use the generated ID
          type: 'link', 
          link: '', 
          description: 'Enter a description for this YouTube video',
          videoType: undefined
        }
      ]
    };
    setModules(newModules);
    updateFormData({ modules: newModules });
    // Initialize error state for the new link
    setLinkErrors(prev => ({ ...prev, [newResourceId]: null })); 
    
    // Show a helpful tooltip about the link format
    toast.info("Only YouTube links are accepted. Standard YouTube video URLs will show a preview.", {
      duration: 4000,
    });
  }

  const updateResourceInfo = (moduleIndex: number, fileIndex: number, field: 'link' | 'description', value: string) => {
    const newModules = [...modules];
    const resource = newModules[moduleIndex].files[fileIndex];
    const resourceId = resource.id;

    if (field === 'link' && resource.type !== 'link') {
        return; 
    }

    newModules[moduleIndex].files[fileIndex] = {
      ...resource,
      [field]: value
    };

    // Check for YouTube video
    if (field === 'link' && value) {
      const videoId = extractYouTubeVideoId(value);
      if (videoId) {
        // It's a YouTube video
        newModules[moduleIndex].files[fileIndex].videoType = 'youtube';
        newModules[moduleIndex].files[fileIndex].videoId = videoId;
        
        // Clear any error since it's a valid YouTube URL
        setLinkErrors(prev => ({ ...prev, [resourceId]: null }));
      } else {
        // Not a valid YouTube URL
        newModules[moduleIndex].files[fileIndex].videoType = undefined;
        newModules[moduleIndex].files[fileIndex].videoId = undefined;
        
        // Show error immediately for invalid URLs if there's text entered
        if (value.trim() !== '') {
          setLinkErrors(prev => ({ 
            ...prev, 
            [resourceId]: value.match(/youtube\.com|youtu\.be/) 
              ? "This YouTube URL format is invalid. Please use a standard YouTube URL." 
              : "Please enter a valid YouTube URL (e.g., https://youtube.com/watch?v=XXXXXXXXXXX)" 
          }));
        } else {
          // Clear errors for empty field
          setLinkErrors(prev => ({ ...prev, [resourceId]: null }));
        }
      }
    }

    setModules(newModules);
    updateFormData({ modules: newModules });

    // Validate link when it changes
    if (field === 'link' && resource.type === 'link') {
      if (value.trim() !== '') {
        const videoId = extractYouTubeVideoId(value);
        if (!videoId) {
          // Show specific error for YouTube URLs that are malformed
          setLinkErrors(prev => ({ 
            ...prev, 
            [resourceId]: value.match(/youtube\.com|youtu\.be/) 
              ? "This YouTube URL format is invalid. Please use a standard YouTube URL." 
              : "Please enter a valid YouTube URL (e.g., https://youtube.com/watch?v=XXXXXXXXXXX)" 
          }));
        } else {
          setLinkErrors(prev => ({ ...prev, [resourceId]: null }));
        }
      } else {
        setLinkErrors(prev => ({ ...prev, [resourceId]: null }));
      }
    }
  }

  const removeResource = (moduleIndex: number, fileIndex: number) => {
    const newModules = [...modules]
    const resourceId = newModules[moduleIndex].files[fileIndex]?.id;
    newModules[moduleIndex].files.splice(fileIndex, 1)
    setModules(newModules)
    updateFormData({ modules: newModules })
    // Clear any error associated with the removed link
    if (resourceId && linkErrors[resourceId]) { // Check if an error actually exists for this ID
        setLinkErrors(prev => {
            const nextErrors = { ...prev };
            delete nextErrors[resourceId]; // Explicitly delete the key
            return nextErrors;
        });
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Final validation check before proceeding
    let allLinksValid = true;
    const currentErrors = { ...linkErrors }; // Copy current errors
    modules.forEach(module => {
        module.files.forEach(resource => {
            if (resource.type === 'link') {
                const videoId = extractYouTubeVideoId(resource.link || '');
                if (!resource.link || !videoId) {
                    allLinksValid = false;
                    // Ensure error is set if link is invalid/empty
                    if (!currentErrors[resource.id]) {
                        currentErrors[resource.id] = !resource.link ? 
                          "A YouTube URL is required." : 
                          "This is not a valid YouTube URL.";
                    }
                } else {
                    // Ensure error is cleared if valid
                    if (currentErrors[resource.id]) {
                       currentErrors[resource.id] = null; 
                    }
                }
            }
        });
    });
    setLinkErrors(currentErrors); // Update errors based on final check

    if (allLinksValid) {
      nextStep()
    }
  }

  
  const canProceed = 
    modules.length > 0 && 
    modules.every(module => module.title.trim() !== '') && 
    modules.every(module => module.files.length >= 1 && module.files.length <= MAX_RESOURCES_PER_MODULE) &&
    modules.every(module => module.files.every(resource => 
        resource.type === 'file' || 
        (resource.type === 'link' && resource.link && extractYouTubeVideoId(resource.link)) // Check for valid YouTube URLs
    )) && !Object.values(linkErrors).some(error => error !== null);

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Training Modules Upload</h2>
          <p className="mt-1 text-sm text-gray-500">Upload your training module files with descriptions.</p>
        </div>

        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-center mb-4">
              <Label>Training Modules</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addModule}
                className="flex items-center cursor-pointer gap-2"
              >
                <Plus className="w-4 h-4" /> Add Module
              </Button>
            </div>

            <div className="space-y-2">
              {modules.map((module, moduleIndex) => (
                <div key={module.id} className="border rounded-lg overflow-hidden">
                  <div className="flex items-center p-4 bg-gray-50">
                    <button
                      type="button"
                      onClick={() => toggleModule(moduleIndex)}
                      className="flex-1 flex cursor-pointer items-center text-left"
                    >
                      <ChevronDown
                        className={`w-5 h-5 cursor-pointer mr-2 transform transition-transform duration-200 ${
                          module.isOpen ? "rotate-180" : ""
                        }`}
                      />
                      <div className="flex-1 flex items-center gap-3">
                        <span className="font-medium whitespace-nowrap">Module {moduleIndex + 1}</span>
                        <Input
                          id={`module-title-${module.id}`}
                          value={module.title}
                          onChange={(e) => updateModuleTitle(moduleIndex, e.target.value)}
                          placeholder="Enter module title (Required)"
                          className="w-full"
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        removeModule(moduleIndex)
                      }}
                      className="text-red-500 cursor-pointer ml-2"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>

                  <div
                    className={`transition-all duration-300 ${module.isOpen ? "max-h-[9999px] opacity-100" : "max-h-0 opacity-0"} overflow-hidden`}
                  >
                    <div className="p-4 space-y-4">
                      <div className="flex items-center justify-evenly gap-4">
                        <div>
                          <FileUploader
                            id={`module-files-${module.id}`}
                            onFilesSelected={(files) => handleModuleFileUpload(moduleIndex, files)}
                            accept=".pdf,.docx,.pptx,.mp4,.mov,.avi,.png,.jpg,.mp3"
                            multiple={true}
                          />
                        </div>
                        
                        <span className="text-gray-500 font-medium">OR</span>

                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleAddLink(moduleIndex)}
                          className="flex items-center gap-2"
                          disabled={module.files.length >= MAX_RESOURCES_PER_MODULE}
                        >
                          <Youtube className="w-4 h-4" /> Add YouTube Link
                        </Button>
                      </div>

                      {module.files.length > 0 && (
                        <div className="space-y-4 border-t pt-4">
                          <Label>Module Resources ({module.files.length}/{MAX_RESOURCES_PER_MODULE})</Label>
                          {module.files.map((resource, resourceIndex) => (
                            <div key={resource.id} className="p-3 bg-gray-50 rounded-md space-y-3 border">
                              <div className="flex justify-between items-start">
                                {resource.type === 'file' && resource.file && (
                                  <div className="flex-1">
                                    <FileList
                                      files={[resource.file]}
                                      onRemove={() => removeResource(moduleIndex, resourceIndex)}
                                    />
                                    <span className="text-xs text-gray-500 ml-1">(Max size: {MAX_FILE_SIZE_BYTES / 1024 / 1024}MB)</span>
                                  </div>
                                )}
                                {resource.type === 'link' && (
                                  <ResourceLink
                                    resource={resource}
                                    moduleIndex={moduleIndex}
                                    resourceIndex={resourceIndex}
                                    updateResourceInfo={updateResourceInfo}
                                    removeResource={removeResource}
                                    linkErrors={linkErrors}
                                  />
                                )}
                              </div>
                              
                              <div>
                                <Label htmlFor={`resource-description-${resource.id}`}>Description</Label>
                                <Textarea
                                  id={`resource-description-${resource.id}`}
                                  value={resource.description}
                                  onChange={(e) => updateResourceInfo(moduleIndex, resourceIndex, "description", e.target.value)}
                                  placeholder="Enter description (optional)"
                                  className="mt-1"
                                  rows={2}
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      {module.files.length === 0 && (
                        <div className="flex flex-col items-center space-y-2 text-center mt-4">
                           <p className="text-sm text-gray-500">No resources added yet. Upload a file or add a link using the buttons above.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-between pt-4 border-t">
          <Button className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md" variant="outline" onClick={prevStep}>
            Previous
          </Button>
          <Button 
            className="bg-color3 cursor-pointer text-white px-4 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
            variant="default"
            type="submit"
            disabled={!canProceed}
            title={!canProceed ? (
                Object.values(linkErrors).some(error => error !== null) ? "Please fix the invalid URL(s) marked in red." : "Please ensure all modules have a title and 1-5 resources (files or valid links)."
            ) : "Proceed to next step"} 
          >
            Next Step
          </Button>
        </div>
      </div>
    </form>
  )
}
