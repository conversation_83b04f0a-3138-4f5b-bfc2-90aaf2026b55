'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Megaphone, LogOut, Search, X, Menu, Users, Shield, Database, HardDrive, FileText } from 'lucide-react';
import { useSidebar } from '@/components/layout/ITsidebar';
import { signOut } from 'next-auth/react';

interface Notification {
  id: number;
  message: string;
  time: string;
  isNew: boolean;
}

// Search result types
type ResultType = 'user' | 'system' | 'database' | 'storage' | 'log';

interface SearchResult {
  id: string;
  title: string;
  subtitle: string;
  type: ResultType;
  url: string;
}

// Mock data for search results
const mockSearchData: SearchResult[] = [
  // Users
  { id: '1', title: '<PERSON>', subtitle: 'System Administrator • Active', type: 'user', url: '/main/it/Dashboard?module=usermanagement' },
  { id: '2', title: '<PERSON>', subtitle: 'Database Admin • Active', type: 'user', url: '/main/it/Dashboard?module=usermanagement' },
  { id: '3', title: '<PERSON>', subtitle: 'Security Analyst • Inactive', type: 'user', url: '/main/it/Dashboard?module=usermanagement' },
  
  // Systems
  { id: 's1', title: 'Main Server', subtitle: 'Production • 98% Uptime', type: 'system', url: '/main/it/Dashboard?module=accesscontrol' },
  { id: 's2', title: 'Backup Server', subtitle: 'Backup • 99% Uptime', type: 'system', url: '/main/it/Dashboard?module=accesscontrol' },
  { id: 's3', title: 'Development Server', subtitle: 'Development • 95% Uptime', type: 'system', url: '/main/it/Dashboard?module=accesscontrol' },
  
  // Databases
  { id: 'd1', title: 'Production DB', subtitle: 'MySQL • 500GB', type: 'database', url: '/main/it/Dashboard?module=database' },
  { id: 'd2', title: 'Analytics DB', subtitle: 'PostgreSQL • 200GB', type: 'database', url: '/main/it/Dashboard?module=database' },
  { id: 'd3', title: 'Backup DB', subtitle: 'MongoDB • 300GB', type: 'database', url: '/main/it/Dashboard?module=database' },
  
  // Storage
  { id: 'st1', title: 'File Server', subtitle: 'NAS • 2TB Used', type: 'storage', url: '/main/it/Dashboard?module=storage' },
  { id: 'st2', title: 'Backup Storage', subtitle: 'S3 • 1.5TB Used', type: 'storage', url: '/main/it/Dashboard?module=storage' },
  
  // Logs
  { id: 'l1', title: 'Security Logs', subtitle: 'Last 24h • 1,234 entries', type: 'log', url: '/main/it/Dashboard?module=logs' },
  { id: 'l2', title: 'System Logs', subtitle: 'Last 24h • 5,678 entries', type: 'log', url: '/main/it/Dashboard?module=logs' },
];

const getIconForResultType = (type: ResultType) => {
  switch (type) {
    case 'user':
      return <Users className="w-4 h-4" />;
    case 'system':
      return <Shield className="w-4 h-4" />;
    case 'database':
      return <Database className="w-4 h-4" />;
    case 'storage':
      return <HardDrive className="w-4 h-4" />;
    case 'log':
      return <FileText className="w-4 h-4" />;
    default:
      return <FileText className="w-4 h-4" />;
  }
};

// Function to highlight search matches
const highlightSearchMatches = (text: string, searchQuery: string): React.ReactNode => {
  if (!searchQuery.trim()) return text;
  
  const parts = text.split(new RegExp(`(${searchQuery.trim()})`, 'gi'));
  return (
    <>
      {parts.map((part, i) => 
        part.toLowerCase() === searchQuery.toLowerCase() ? 
          <span key={i} className="text-blue-600 font-semibold">{part}</span> : part
      )}
    </>
  );
};

const ITnav: React.FC = () => {
  const [showNotifications, setShowNotifications] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showSearchResults, setShowSearchResults] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const { isCollapsed, toggleSidebar } = useSidebar();
  const notificationsRef = useRef<HTMLDivElement | null>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);

  // Sample notifications - in a real app, these would come from your backend
  const notifications: Notification[] = [
    {
      id: 1,
      message: "New security alert: Multiple failed login attempts detected",
      time: "5 minutes ago",
      isNew: true,
    },
    {
      id: 2,
      message: "Database backup completed successfully",
      time: "1 hour ago",
      isNew: true,
    },
    {
      id: 3,
      message: "System maintenance scheduled for tonight",
      time: "2 hours ago",
      isNew: false,
    },
    {
      id: 4,
      message: "Storage capacity warning: 85% used",
      time: "3 hours ago",
      isNew: false,
    }
  ];

  // Search logic
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    // Filter mock data based on search query
    const filteredResults = mockSearchData.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setSearchResults(filteredResults);
    setShowSearchResults(true);
  }, [searchQuery]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleResultClick = (result: SearchResult) => {
    setShowSearchResults(false);
    setSearchQuery('');
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
      setShowNotifications(false);
    }
    if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
      setShowSearchResults(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`fixed top-0 right-0 ${isCollapsed ? 'w-[calc(100%-64px)]' : 'w-[calc(100%-250px)]'} transition-all duration-300 ease-in-out z-50`}>
      <div className='flex justify-between h-16 px-6 shadow-md bg-white items-center'>
        <div className='flex items-center gap-4'>
          <button
            onClick={toggleSidebar}
            className='p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors'
            aria-label="Toggle sidebar"
          >
            <Menu size={20} className='text-gray-600' />
          </button>
          <div className='relative flex items-center' ref={searchRef}>
            <div className='absolute left-3 flex items-center pointer-events-none'>
              <Search className='h-5 w-5 text-gray-400' />
            </div>
            <input 
              type="text"
              placeholder="Search for users, systems, databases..."
              className='w-[350px] h-10 pl-10 pr-4 rounded-lg bg-gray-50 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-100 text-sm'
              value={searchQuery}
              onChange={handleSearch}
            />

            {/* Search Results Dropdown */}
            {showSearchResults && searchResults.length > 0 && (
              <div className='absolute z-50 top-full left-0 mt-1 w-[350px] bg-white rounded-lg shadow-lg border border-gray-200 max-h-[400px] overflow-y-auto'>
                <div className='p-3 border-b border-gray-100'>
                  <p className='text-xs text-gray-500'>
                    {searchResults.length} results for "{searchQuery}"
                  </p>
                </div>
                <div className='divide-y divide-gray-100'>
                  {searchResults.map((result) => (
                    <div 
                      key={result.id} 
                      className='p-3 hover:bg-gray-50 cursor-pointer'
                      onClick={() => handleResultClick(result)}
                    >
                      <div className='flex items-start gap-3'>
                        <div className='mt-0.5 p-1.5 bg-blue-50 rounded-md text-color3'>
                          {getIconForResultType(result.type)}
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900'>
                            {highlightSearchMatches(result.title, searchQuery)}
                          </p>
                          <p className='text-xs text-gray-500 mt-0.5'>
                            {highlightSearchMatches(result.subtitle, searchQuery)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* No Results Message */}
            {showSearchResults && searchQuery.trim() !== '' && searchResults.length === 0 && (
              <div className='absolute z-50 top-full left-0 mt-1 w-[350px] bg-white rounded-lg shadow-lg border border-gray-200'>
                <div className='p-6 flex flex-col items-center justify-center'>
                  <Search className='text-gray-300 mb-2' size={24} />
                  <p className='text-gray-500 text-sm'>No results found for "{searchQuery}"</p>
                  <p className='text-gray-400 text-xs mt-1'>Try different keywords or check spelling</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className='flex items-center gap-3'>
          <h1 className='text-sm font-semibold'>IT Admin</h1>
          <div className='relative flex items-center' ref={notificationsRef}>
            <button 
              className='relative p-2 cursor-pointer hover:bg-gray-50 rounded-full transition-colors'
              onClick={() => setShowNotifications(!showNotifications)}
              aria-label="Toggle notifications"
            >
              <Megaphone className='text-color3' size={20} />
              {notifications.some(n => n.isNew) && (
                <span className='absolute top-1 right-1 bg-red-500 rounded-full w-2 h-2'></span>
              )}
            </button>

            {showNotifications && (
              <div className='absolute z-[9999] right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-100 py-2'>
                <div className='flex items-center justify-between px-4 py-2 border-b border-gray-100'>
                  <h3 className='font-semibold text-sm'>Notifications</h3>
                  <button 
                    onClick={() => setShowNotifications(false)}
                    className='p-1 hover:bg-gray-50 cursor-pointer rounded-full text-gray-400 hover:text-gray-600'
                    aria-label="Close notifications"
                  >
                    <X size={16} />
                  </button>
                </div>
                <div className='max-h-[300px] overflow-y-auto'>
                  {notifications.map((notif) => (
                    <div 
                      key={notif.id}
                      className='px-4 py-3 hover:bg-gray-50 border-b border-gray-50 last:border-0 cursor-pointer'
                    >
                      <div className='flex items-start gap-2'>
                        {notif.isNew && (
                          <span className='w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0'></span>
                        )}
                        <div>
                          <p className='text-sm text-gray-700'>{notif.message}</p>
                          <span className='text-xs text-gray-400 block mt-1'>{notif.time}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <button 
            className='p-2 cursor-pointer hover:bg-gray-50 rounded-full transition-colors'
            aria-label="Logout"
            onClick={() => signOut({ callbackUrl: '/' })}
          >
            <LogOut className='text-red-500' size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ITnav;
