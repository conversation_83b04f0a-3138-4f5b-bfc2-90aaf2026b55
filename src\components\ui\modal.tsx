import { FiX } from 'react-icons/fi';
import { useEffect, useState, ReactNode } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
}

const Modal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = 'small' 
}: ModalProps) => {
  const [shouldRender, setShouldRender] = useState<boolean>(false);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      requestAnimationFrame(() => {
        setIsAnimating(true);
      });
    } else {
      setIsAnimating(false);
      const timer = setTimeout(() => {
        setShouldRender(false);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!shouldRender) return null;

  const sizeClasses: Record<'small' | 'medium' | 'large' | 'xlarge', string> = {
    small: 'w-[90%] sm:w-[70%] md:w-[50%] lg:w-[40%] xl:w-[30%]',
    medium: 'w-[95%] sm:w-[85%] md:w-[75%] lg:w-[60%] xl:w-[50%]',
    large: 'w-[98%] sm:w-[90%] md:w-[80%] lg:w-[70%] xl:w-[60%]',
    xlarge: 'w-[98%] sm:w-[95%] md:w-[85%] lg:w-[75%] xl:w-[65%] max-w-5xl',
  };

  return (
    <div
      className={`fixed inset-0 bg-black/30 backdrop-blur-sm transition-opacity ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onClick={onClose}
      style={{ zIndex: 9998 }}
    >
      <div
        className={`fixed left-1/2 top-1/2 p-5 transform -translate-x-1/2 -translate-y-1/2 bg-white/95 backdrop-blur-md rounded-lg shadow-xl transition-all ${
          isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'
        } ${sizeClasses[size]}`}
        style={{ zIndex: 9999 }}
        onClick={(e) => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="absolute cursor-pointer top-2 right-2 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-600 transition-colors"
          type="button"
          aria-label="Close modal"
        >
          <FiX className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
        <h2 className="text-xl text-center sm:text-2xl font-semibold mb-4 sm:mb-6 text-gray-900">{title}</h2>
        <div className="p-4">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
