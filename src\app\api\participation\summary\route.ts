import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month')
});

export async function GET(request: NextRequest) {
  try {
    console.log('Participation summary API called');
    
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      console.log('Auth failed: No session');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get and validate query parameters
    const searchParams = request.nextUrl.searchParams;
    
    const validationResult = querySchema.safeParse(Object.fromEntries(searchParams.entries()));
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { timeframe } = validationResult.data;
    console.log('Using timeframe:', timeframe);

    // Calculate date ranges for current and previous periods
    const today = new Date();
    let startDate = new Date();
    let previousPeriodStart = new Date();
    let previousPeriodEnd = new Date(startDate);

    switch (timeframe) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        previousPeriodStart.setDate(today.getDate() - 14);
        previousPeriodEnd.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        previousPeriodStart.setMonth(today.getMonth() - 2);
        previousPeriodEnd.setMonth(today.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(today.getMonth() - 3);
        previousPeriodStart.setMonth(today.getMonth() - 6);
        previousPeriodEnd.setMonth(today.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        previousPeriodStart.setFullYear(today.getFullYear() - 2);
        previousPeriodEnd.setFullYear(today.getFullYear() - 1);
        break;
    }

    // 1. Total Participants
    const totalParticipants = await prisma.user.count({
      where: {
        userTrainingProgress: {
          some: {}
        }
      }
    });

    const previousPeriodParticipants = await prisma.user.count({
      where: {
        userTrainingProgress: {
          some: {
            updatedAt: {
              gte: previousPeriodStart,
              lt: previousPeriodEnd
            }
          }
        }
      }
    });

    const currentPeriodParticipants = await prisma.user.count({
      where: {
        userTrainingProgress: {
          some: {
            updatedAt: {
              gte: startDate
            }
          }
        }
      }
    });

    // Calculate participant trend percentage with safeguards
    let participantTrend = 0;
    if (previousPeriodParticipants > 0) {
      participantTrend = parseFloat((((currentPeriodParticipants - previousPeriodParticipants) / previousPeriodParticipants) * 100).toFixed(1));
    } else if (currentPeriodParticipants > 0) {
      participantTrend = 100; // 100% increase if previously 0
    }

    // 2. Average Completion Rate
    const trainingProgresses = await prisma.userTrainingProgress.findMany({
      where: {
        OR: [
          { status: 'COMPLETED' },
          { status: 'IN_PROGRESS' }
        ]
      },
      select: {
        progressPercentage: true,
        status: true,
        updatedAt: true
      }
    });

    const currentCompletionStats = trainingProgresses.filter(progress => 
      progress.updatedAt >= startDate
    );

    const previousCompletionStats = trainingProgresses.filter(progress => 
      progress.updatedAt >= previousPeriodStart && progress.updatedAt < previousPeriodEnd
    );

    // Calculate average completion rates
    const avgCompletionRate = currentCompletionStats.length > 0
      ? parseFloat((currentCompletionStats.reduce((sum, item) => sum + item.progressPercentage, 0) / currentCompletionStats.length).toFixed(1))
      : 0;

    const previousAvgCompletionRate = previousCompletionStats.length > 0
      ? parseFloat((previousCompletionStats.reduce((sum, item) => sum + item.progressPercentage, 0) / previousCompletionStats.length).toFixed(1))
      : 0;

    // Calculate completion trend with safeguards
    let completionTrend = 0;
    if (previousAvgCompletionRate > 0) {
      completionTrend = parseFloat((((avgCompletionRate - previousAvgCompletionRate) / previousAvgCompletionRate) * 100).toFixed(1));
    } else if (avgCompletionRate > 0) {
      completionTrend = 100; // 100% increase if previously 0
    }

    // 3. Knowledge Sharing Stats (created resources)
    const totalContributions = await prisma.trainingParticipant.count({
      where: {
        isCreator: true
      }
    });

    const currentPeriodContributions = await prisma.trainingParticipant.count({
      where: {
        isCreator: true,
        createdAt: {
          gte: startDate
        }
      }
    });

    const previousPeriodContributions = await prisma.trainingParticipant.count({
      where: {
        isCreator: true,
        createdAt: {
          gte: previousPeriodStart,
          lt: previousPeriodEnd
        }
      }
    });

    // Calculate contributions trend with safeguards
    let contributionTrend = 0;
    if (previousPeriodContributions > 0) {
      contributionTrend = parseFloat((((currentPeriodContributions - previousPeriodContributions) / previousPeriodContributions) * 100).toFixed(1));
    } else if (currentPeriodContributions > 0) {
      contributionTrend = 100; // 100% increase if previously 0
    }

    // Log metrics for debugging
    console.log('Summary metrics:');
    console.log(`- Participants: ${totalParticipants} total, ${currentPeriodParticipants} active, ${participantTrend}% trend`);
    console.log(`- Completion: ${avgCompletionRate}%, ${completionTrend}% trend`);
    console.log(`- Contributions: ${totalContributions} total, ${contributionTrend}% trend`);

    return NextResponse.json({
      participants: {
        total: totalParticipants,
        active: currentPeriodParticipants,
        trend: participantTrend
      },
      completionRate: {
        rate: avgCompletionRate,
        trend: completionTrend
      },
      knowledgeSharing: {
        total: totalContributions,
        trend: contributionTrend
      }
    });
    
  } catch (error) {
    console.error('Error calculating participation summary:', error);
    return NextResponse.json(
      { error: 'Failed to calculate participation summary' },
      { status: 500 }
    );
  }
} 