import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month'),
  divisionId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  unitId: z.string().optional().transform(val => val ? parseInt(val, 10) : null)
});

export async function GET(request: NextRequest) {
  try {
    console.log('Departments participation API called');
    
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      console.log('Auth failed: No session');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get and validate query parameters
    const searchParams = request.nextUrl.searchParams;
    const rawParams = Object.fromEntries(searchParams.entries());
    console.log('Raw query parameters:', rawParams);
    
    const validationResult = querySchema.safeParse(rawParams);
    if (!validationResult.success) {
      console.log('Validation failed:', validationResult.error.flatten().fieldErrors);
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { timeframe, divisionId, unitId } = validationResult.data;
    console.log('Using parameters:', { timeframe, divisionId, unitId });

    // Calculate date ranges for current and previous periods
    const today = new Date();
    let startDate = new Date();
    let previousPeriodStart = new Date();
    let previousPeriodEnd = new Date(startDate);

    // Calculate weekly data points for the chart
    const weekPoints = 4; // Show 4 weeks of data
    const weeklyDataPoints = [];
    
    // Generate dates for weekly points
    for (let i = 0; i < weekPoints; i++) {
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - ((i + 1) * 7));
      
      const weekEnd = new Date(today);
      weekEnd.setDate(today.getDate() - (i * 7));
      
      weeklyDataPoints.push({
        label: `Week ${weekPoints - i}`,
        startDate: weekStart,
        endDate: weekEnd
      });
    }
    
    // Sort points chronologically
    weeklyDataPoints.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());

    // Calculate time periods based on timeframe parameter
    switch (timeframe) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        previousPeriodStart.setDate(today.getDate() - 14);
        previousPeriodEnd.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        previousPeriodStart.setMonth(today.getMonth() - 2);
        previousPeriodEnd.setMonth(today.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(today.getMonth() - 3);
        previousPeriodStart.setMonth(today.getMonth() - 6);
        previousPeriodEnd.setMonth(today.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        previousPeriodStart.setFullYear(today.getFullYear() - 2);
        previousPeriodEnd.setFullYear(today.getFullYear() - 1);
        break;
    }

    // Build filters for units based on parameters
    let unitsWhere: any = {};

    // First, try to get the specific unit if a unitId is provided
    if (unitId) {
      console.log('Looking for specific unit with ID:', unitId);
      unitsWhere = { id: unitId };
    } 
    // Otherwise if divisionId is provided, get all units in that division
    else if (divisionId) {
      console.log('Looking for units in division with ID:', divisionId);
      unitsWhere = { divisionId };
    }
    // Check if a unitName was provided via URL
    const unitNameParam = searchParams.get('unitName');
    if (unitNameParam) {
      console.log('Looking for specific unit by name:', unitNameParam);
      unitsWhere = { unitName: unitNameParam };
    }

    console.log('Final units where clause:', unitsWhere);
    
    // Get all units matching the filter
    const units = await prisma.unit.findMany({
      where: unitsWhere,
      select: {
        id: true,
        unitName: true,
        divisionId: true,
        division: {
          select: {
            id: true,
            divisionName: true
          }
        }
      },
      orderBy: {
        unitName: 'asc'
      }
    });

    console.log(`Found ${units.length} matching units:`, units.map(u => u.unitName));

    if (units.length === 0) {
      console.log('No units found with the provided filter');
      
      // Instead of returning 404, return empty data
      return NextResponse.json({
        departmentData: [],
        chartData: {
          labels: [],
          datasets: [{
            label: 'Participation Rate (%)',
            data: [],
            backgroundColor: [],
            borderColor: [],
            borderWidth: 1,
            borderRadius: 6,
            maxBarThickness: 35
          }]
        }
      });
    }

    // Calculate weekly participation rates for chart data
    const weeklyParticipationRates = await Promise.all(weeklyDataPoints.map(async (weekPoint) => {
      const participationRate = await calculateOverallParticipationRate(weekPoint.startDate, weekPoint.endDate);
      return {
        label: weekPoint.label,
        participationRate
      };
    }));

    // Calculate department-specific participation data
    const departmentData = await Promise.all(units.map(async (unit) => {
      // Get total employees in this unit
      const totalEmployees = await prisma.user.count({
        where: { unitId: unit.id }
      });

      // Get employees with any training activity in current period
      const activeEmployees = await prisma.user.count({
        where: {
          unitId: unit.id,
          userTrainingProgress: {
            some: {
              updatedAt: {
                gte: startDate
              }
            }
          }
        }
      });

      // Get employees with activity in previous period
      const previousPeriodActiveEmployees = await prisma.user.count({
        where: {
          unitId: unit.id,
          userTrainingProgress: {
            some: {
              updatedAt: {
                gte: previousPeriodStart,
                lt: previousPeriodEnd
              }
            }
          }
        }
      });

      // Calculate participation rates with safeguards for 0 division
      const participationRate = totalEmployees > 0
        ? Math.round((activeEmployees / totalEmployees) * 100)
        : 0;

      const previousParticipationRate = totalEmployees > 0
        ? Math.round((previousPeriodActiveEmployees / totalEmployees) * 100)
        : 0;

      // Calculate trend (change from previous period)
      const trend = participationRate - previousParticipationRate;

      console.log(`Unit ${unit.unitName}: ${activeEmployees}/${totalEmployees} employees, participation: ${participationRate}%, trend: ${trend}%`);

      return {
        unitId: unit.id,
        unitName: unit.unitName,
        divisionId: unit.divisionId,
        divisionName: unit.division.divisionName,
        totalEmployees,
        activeEmployees,
        participationRate,
        previousParticipationRate,
        trend
      };
    }));

    // Format chart data for weekly participation
    const chartData = {
      labels: weeklyParticipationRates.map(item => item.label),
      datasets: [{
        label: 'Participation Rate',
        data: weeklyParticipationRates.map(item => item.participationRate),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
        borderRadius: 4
      }]
    };

    // Return the combined data
    return NextResponse.json({
      departmentData,
      chartData
    });
    
  } catch (error) {
    console.error('Error calculating department participation:', error);
    return NextResponse.json(
      { error: 'Failed to calculate department participation' },
      { status: 500 }
    );
  }
}

// Helper function to calculate overall participation rate for a date range
async function calculateOverallParticipationRate(startDate: Date, endDate: Date): Promise<number> {
  // Get total employees
  const totalEmployees = await prisma.user.count();
  
  if (totalEmployees === 0) {
    return 0;
  }

  // Get active employees in the period
  const activeEmployees = await prisma.user.count({
    where: {
      userTrainingProgress: {
        some: {
          updatedAt: {
            gte: startDate,
            lt: endDate
          }
        }
      }
    }
  });

  // Calculate and return participation rate
  return Math.round((activeEmployees / totalEmployees) * 100);
} 