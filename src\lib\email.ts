import nodemailer from 'nodemailer';

// TODO: Configure and implement email sending logic

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html: string;
}

export const sendEmail = async (options: EmailOptions): Promise<void> => {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USERNAME, 
      pass: process.env.EMAIL_PASSWORD, 
    },
  });
  const mailOptions = {
    from: process.env.EMAIL_FROM, 
    to: options.to,
    subject: options.subject,
    text: options.text,
    html: options.html,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    
  } catch (error) {
    console.error('Error sending email: ', error);
    throw new Error('Failed to send email.');
  }
}; 