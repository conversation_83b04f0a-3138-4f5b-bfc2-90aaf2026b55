import React, { useState } from 'react';
import { FiChevronDown } from 'react-icons/fi';

interface AccordionProps {
  title: string;
  content: string;
}

const Accordion: React.FC<AccordionProps> = ({ title, content }) => {
  const [isOpen, setIsOpen] = useState(false);

  if (!title || !content) return null;

  return (
    <div className="border-b">
      <button
        className="flex w-full justify-between items-center py-4"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-sm font-medium text-gray-900">{title}</span>
        <FiChevronDown
          className={`w-5 h-5 text-gray-500 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>
      {isOpen && (
        <div className="pb-4">
          <p className="text-sm text-gray-500">{content}</p>
        </div>
      )}
    </div>
  );
};

export default Accordion; 