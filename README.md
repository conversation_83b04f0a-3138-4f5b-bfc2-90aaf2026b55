# ShareIT - Knowledge Management Portal

ShareIT is a comprehensive knowledge management portal designed to facilitate easy access, sharing, and organization of knowledge resources within an organization. The platform focuses on training material sharing, enabling employees to upload materials after specific training sessions and providing progress tracking with restore points.

## Features

- **Training Material Sharing**: Upload and access training materials after sessions
- **Progress Tracking**: Resume learning from where you left off with restore points
- **Knowledge Search & Filtering**: Advanced search and filtering for easy resource access
- **Self-Paced Learning**: Track your learning journey with progress bars
- **Role-Based Access Control**: Different access levels for Employees, HR, and IT Admins
- **Secure File Management**: Categorize and control access to documents
- **Engagement & Reports**: Track learning trends and generate insights
- **Competency Management**: Track and manage employee competencies
- **Audit Logging**: Comprehensive audit trail of system activities
- **Redis Caching**: Efficient progress tracking with Redis caching
- **PDF Viewer Integration**: Built-in PDF viewing capabilities
- **Video Support**: Support for YouTube and video content

## Tech Stack

- **Frontend**: 
  - Next.js 15.2.4
  - React 19
  - Tailwind CSS 4
  - Shadcn UI Components
  - Lucide React Icons
  - React PDF Viewer
  - React Player
  - Chart.js & Recharts
  - React Hot Toast & Sonner

- **Backend**: 
  - Next.js API Routes
  - Node.js
  - Prisma ORM 6.5.0
  - PostgreSQL/MySQL
  - Redis (ioredis)
  - PM2 for process management

- **Authentication & Security**: 
  - NextAuth.js
  - Helmet
  - Next-CORS
  - Next-Rate-Limit
  - bcrypt

- **Development Tools**:
  - TypeScript
  - ESLint
  - Zod for validation
  - tsx for TypeScript execution

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- PostgreSQL/MySQL database
- Redis server
- Prisma CLI

### Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd share-it
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Set up environment variables:
Create a `.env` file in the root directory with the following variables:
```
DATABASE_URL="your_database_url"
NEXTAUTH_SECRET="your_nextauth_secret"
NEXTAUTH_URL="http://localhost:3000"
REDIS_URL="your_redis_url"
REDIS_PORT="6379"
REDIS_USERNAME="optional_username"
REDIS_PASSWORD="optional_password"
REDIS_TLS_ENABLED="false"
CRON_SECRET="your_cron_secret"
APP_URL="http://localhost:3000"
```

4. Initialize the database:
```bash
npx prisma migrate dev
npm run seed
```

5. Start the development server:
```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run seed` - Seed the database

## Project Structure

- `/src` - Source code
  - `/app` - Next.js app directory
  - `/components` - React components
  - `/lib` - Utility functions and configurations
  - `/styles` - Global styles
  - `/utils` - Helper functions
- `/prisma` - Database schema and migrations
  - `/seeders` - Database seeders
- `/public` - Static assets
- `/scripts` - Utility scripts
  - `progress-persistence.js` - Progress tracking cron job
- `/storage` - File storage directory

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

[Add your license information here]

## Support

For support, please contact [your support contact information].
