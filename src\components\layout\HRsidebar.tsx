import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { LayoutDashboard, UsersRound, ChartLine, CircleCheckBig } from 'lucide-react';
import Logo from '../../../public/images/Logo.svg';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';

interface SidebarContextProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

const SidebarContext = createContext<SidebarContextProps | undefined>(undefined);

export const SidebarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    const saved = localStorage.getItem('sidebarCollapsed');
    if (saved !== null) {
      setIsCollapsed(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    if (isMounted) {
      localStorage.setItem('sidebarCollapsed', JSON.stringify(isCollapsed));
    }
  }, [isCollapsed, isMounted]);

  const toggleSidebar = () => {
    setIsCollapsed(prev => !prev);
  };

  return (
    <SidebarContext.Provider value={{ isCollapsed, toggleSidebar }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

interface HRSidebarProps {
  onCollapse: (collapsed: boolean) => void;
}

const HRSidebar: React.FC<HRSidebarProps> = ({ onCollapse }) => {
  const { isCollapsed } = useSidebar();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    onCollapse(isCollapsed);
  }, [isCollapsed, onCollapse]);

  const isActive = (path: string, moduleParam?: string) => {
    const currentModule = searchParams.get('module');
    
    // For dashboard (no module parameter)
    if (path === '/main/hr/Dashboard' && !moduleParam) {
      return pathname === '/main/hr/Dashboard' && !currentModule;
    }
    
    // For other menu items with module parameter
    if (moduleParam) {
      return pathname === '/main/hr/Dashboard' && currentModule === moduleParam;
    }

    return false;
  };

  return (
    <aside 
      className={`
        fixed top-0 left-0 h-screen bg-white shadow-md z-20
        ${isCollapsed ? 'w-16' : 'w-[250px]'}
        transition-all duration-300 ease-in-out
      `}
    >
      <div className='h-16 flex items-center justify-center border-b border-gray-100 overflow-hidden'>
        <Image src={Logo} 
          alt="Logo" 
          className={`
            ${isCollapsed ? 'h-8 w-8' : 'h-8'} 
            transition-all duration-300 ease-in-out
          `} 
        />
      </div>
      
      {/* Navigation Menu */}
      <nav className="p-3 space-y-1 overflow-hidden">
        <Link href="/main/hr/Dashboard" className={`flex items-center gap-3 px-3 py-2 rounded-lg text-xs group ${isActive('/main/hr/Dashboard') ? 'bg-gray-100 text-color3' : 'text-gray-700 hover:bg-gray-50'} cursor-pointer`}>
          <span className={`${isActive('/main/hr/Dashboard') ? 'text-color3' : 'text-gray-400 group-hover:text-gray-600'}`}>
            <LayoutDashboard size={20} />
          </span>
          <span className={`font-medium whitespace-nowrap transition-all duration-300 ${
            isCollapsed ? 'opacity-0 translate-x-10' : 'opacity-100 translate-x-0'
          }`}>
            Dashboard
          </span>
        </Link>

        <div className={`transition-all duration-300 ${
          isCollapsed ? 'opacity-0 h-0' : 'opacity-100 h-auto mt-3'
        }`}>
          <h1 className='text-color3 font-semibold px-3'>General</h1>
        </div>

        <Link href="/main/hr/Dashboard?module=participation" className={`flex items-center gap-3 px-3 py-2 rounded-lg text-xs group ${isActive('/main/hr/Dashboard', 'participation') ? 'bg-gray-100 text-color3' : 'text-gray-700 hover:bg-gray-50'} cursor-pointer`}>
          <span className={`${isActive('/main/hr/Dashboard', 'participation') ? 'text-color3' : 'text-gray-400 group-hover:text-gray-600'}`}>
            <UsersRound size={20} />
          </span>
          <span className={`font-medium whitespace-nowrap transition-all duration-300 ${
            isCollapsed ? 'opacity-0 translate-x-10' : 'opacity-100 translate-x-0'
          }`}>
            Participation Tracking
          </span>
        </Link>

        <Link href="/main/hr/Dashboard?module=approvals" className={`flex items-center gap-3 px-3 py-2 rounded-lg text-xs group ${isActive('/main/hr/Dashboard', 'approvals') ? 'bg-gray-100 text-color3' : 'text-gray-700 hover:bg-gray-50'} cursor-pointer`}>
          <span className={`${isActive('/main/hr/Dashboard', 'approvals') ? 'text-color3' : 'text-gray-400 group-hover:text-gray-600'}`}>
            <CircleCheckBig size={20} />
          </span>
          <span className={`font-medium whitespace-nowrap transition-all duration-300 ${
            isCollapsed ? 'opacity-0 translate-x-10' : 'opacity-100 translate-x-0'
          }`}>
            Publication Approvals
          </span>
        </Link>

        <Link href="/main/hr/Dashboard?module=analytics" className={`flex items-center gap-3 px-3 py-2 rounded-lg text-xs group ${isActive('/main/hr/Dashboard', 'analytics') ? 'bg-gray-100 text-color3' : 'text-gray-700 hover:bg-gray-50'} cursor-pointer`}>
          <span className={`${isActive('/main/hr/Dashboard', 'analytics') ? 'text-color3' : 'text-gray-400 group-hover:text-gray-600'}`}>
            <ChartLine size={20} />
          </span>
          <span className={`font-medium whitespace-nowrap transition-all duration-300 ${
            isCollapsed ? 'opacity-0 translate-x-10' : 'opacity-100 translate-x-0'
          }`}>
            Reports & Analytics
          </span>
        </Link>
      </nav>
    </aside>
  );
};

export default HRSidebar;