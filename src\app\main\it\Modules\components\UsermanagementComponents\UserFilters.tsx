import React from 'react'
import { Search } from 'lucide-react'

interface UserFiltersProps {
  onSearch: (value: string) => void;
  onRoleFilter: (role: string) => void;
  onStatusFilter: (status: string) => void;
  onEntriesChange: (entries: number) => void;
}

const UserFilters: React.FC<UserFiltersProps> = ({
  onSearch,
  onRoleFilter,
  onStatusFilter,
  onEntriesChange
}) => {
  return (
    <div className="flex flex-col md:flex-row gap-3 mb-4 items-end">
      <div className="flex-1">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search users..."
            className="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 focus:outline-none focus:border-color3 focus:ring-1 focus:ring-color3 cursor-text"
            onChange={(e) => onSearch(e.target.value)}
          />
        </div>
      </div>
      <div className="flex gap-3">
        <select
          className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 focus:outline-none focus:border-color3 focus:ring-1 focus:ring-color3 cursor-pointer"
          onChange={(e) => onRoleFilter(e.target.value)}
        >
          <option value="">All Roles</option>
          <option value="IT Admin">IT Admin</option>
          <option value="HR Admin">HR Admin</option>
          <option value="Employee">Employee</option>
        </select>
        <select
          className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 focus:outline-none focus:border-color3 focus:ring-1 focus:ring-color3 cursor-pointer"
          onChange={(e) => onStatusFilter(e.target.value)}
        >
          <option value="">All Statuses</option>
          <option value="Active">Active</option>
          <option value="Away">Away</option>
          <option value="Inactive">Inactive</option>
        </select>
        <select
          className="px-4 py-2 bg-white border border-gray-200 rounded-lg text-gray-700 focus:outline-none focus:border-color3 focus:ring-1 focus:ring-color3"
          onChange={(e) => onEntriesChange(Number(e.target.value))}
        >
          <option value="5">5</option>
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
      </div>
    </div>
  )
}

export default UserFilters