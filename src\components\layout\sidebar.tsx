'use client';

import { useState, useEffect, useRef } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>Book, FiLogOut, FiPlus, FiBell, FiChevronLeft} from 'react-icons/fi';
import { PanelLeft, PanelLeftClose, PanelRight, PanelRightClose } from 'lucide-react';
import { LuGraduationCap } from 'react-icons/lu';
import { signOut, useSession } from 'next-auth/react';


interface UserData {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  unit: string;
  role: string;
}

interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  read: boolean;
  path: string;
  relatedEntityType?: string;
  relatedEntityId?: number;
}

interface MenuItem {
  icon: React.ReactNode;
  title: string;
  path: string;
  active: boolean;
}

interface SidebarProps {
  onCollapse: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onCollapse }) => {
  const { data: session } = useSession();
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showNotifications, setShowNotifications] = useState<boolean>(false);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState<boolean>(false);
  const [notificationError, setNotificationError] = useState<string | null>(null);

  
  const fetchNotifications = async () => {
    if (!session?.user) return;
    
    try {
      setIsLoadingNotifications(true);
      setNotificationError(null);
      
      const response = await fetch('/api/notifications?limit=10');
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }
      
      const data = await response.json();
      setNotifications(data.notifications);
      setUnreadCount(data.unreadCount);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setNotificationError('Failed to load notifications');
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  
  useEffect(() => {
    if (session?.user) {
      fetchNotifications();
      
      // Poll for new notifications every 30 seconds
      const interval = setInterval(fetchNotifications, 30000);
      return () => clearInterval(interval);
    }
  }, [session]);

  // Re-fetch notifications when the notification panel is opened
  useEffect(() => {
    if (showNotifications) {
      fetchNotifications();
    }
  }, [showNotifications]);

  const handleNotificationClick = async (notification: Notification) => {
    try {
      // Mark this notification as read in the local state
      setNotifications(prevNotifications =>
        prevNotifications.map(notif =>
          notif.id === notification.id ? { ...notif, read: true } : notif
        )
      );
      
      // Decrease unread count if notification wasn't read
      if (!notification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      // Mark notification as read in the database
      await fetch('/api/notifications/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notificationIds: [notification.id]
        }),
      });
      
      // Close notification panel and navigate
      setShowNotifications(false);
      router.push(notification.path);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      // Update local state
      setNotifications(prevNotifications =>
        prevNotifications.map(notif => ({ ...notif, read: true }))
      );
      setUnreadCount(0);
      
      // Call API to mark all as read
      await fetch('/api/notifications/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // Empty body to mark all as read
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      // Revert state if the API call fails
      fetchNotifications();
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Call onCollapse when isCollapsed changes
  useEffect(() => {
    onCollapse(isCollapsed);
  }, [isCollapsed, onCollapse]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch('/api/users/me');
        if (response.ok) {
          const data = await response.json();
          setUserData(data);
        } else {
          console.error('Failed to fetch user data');
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    if (session?.user) {
      fetchUserData();
    }
  }, [session]);

  const menuItems: MenuItem[] = [
    {
      icon: <FiBook size={20} />,
      title: 'Home',
      path: '/main/employee/Dashboard',
      active: pathname === '/main/employee/Dashboard' && !searchParams.get('module')
    },
    {
      icon: <LuGraduationCap size={20} />,
      title: 'My Dashboard',
      path: '/main/employee/Dashboard?module=knowledge',
      active: pathname === '/main/employee/Dashboard' && searchParams.get('module') === 'knowledge'
    }
  ];

  const userInitial = userData ? userData.firstName[0] : '?';
  const userName = userData ? `${userData.firstName} ${userData.lastName}` : 'Loading...';

  return (
    <>
      <aside className={`fixed top-0 left-0 h-screen bg-white border-r border-gray-200 transition-all duration-300 z-[100] ${isCollapsed ? 'w-[80px]' : 'w-[280px]'}`}>
        {/* Logo Section */}
        <div className={`p-6 gap-6 flex justify-center border-b border-gray-200 ${isCollapsed ? 'p-4' : 'p-6'}`}>
          <Image 
            src="/images/Logo.svg" 
            alt="Syncko Logo" 
            width={128} 
            height={32} 
            className={`h-8 w-auto transition-all duration-300 ${isCollapsed ? 'w-8' : 'w-auto'}`} 
          />
        </div>

        {/* User Profile Section */}
        <div className={`p-4 border-b border-gray-200 ${isCollapsed ? 'flex justify-center' : ''}`}>
          {!isCollapsed ? (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-[#E8F0FE] rounded-full flex items-center justify-center">
                <span className="text-[#0077CC] font-medium">{userInitial}</span>
              </div>
              <div className="flex items-center justify-between flex-1">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-black">{userName}</span>
                  {userData && (
                    <span className="text-xs text-gray-500">{userData.unit}</span>
                  )}
                </div>
                <div className="relative" ref={notificationRef}>
                  <button
                    onClick={() => setShowNotifications(!showNotifications)}
                    className="relative p-2 hover:bg-gray-100 rounded-full transition-colors cursor-pointer"
                    aria-label="Notifications"
                  >
                    <FiBell size={20} className="hover:text-[#0077CC] text-black" />
                    {unreadCount > 0 && (
                      <span className="absolute top-1 right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                        {unreadCount}
                      </span>
                    )}
                  </button>
                  
                  {showNotifications && (
                    <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-[110] transform translate-x-[calc(100%-20px)]">
                      <div className="p-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold">Notifications</h3>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto">
                        {isLoadingNotifications && notifications.length === 0 ? (
                          <div className="flex justify-center items-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#0077CC]"></div>
                          </div>
                        ) : notificationError ? (
                          <div className="p-4 text-center text-red-500">
                            {notificationError}
                          </div>
                        ) : notifications.length === 0 ? (
                          <div className="p-4 text-center text-gray-500">
                            No notifications
                          </div>
                        ) : (
                          notifications.map((notification) => (
                            <div
                              key={notification.id}
                              onClick={() => handleNotificationClick(notification)}
                              className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
                                !notification.read ? 'bg-blue-50' : ''
                              }`}
                            >
                              <div className="flex items-start gap-3">
                                <div className="flex-1">
                                  <p className="text-sm font-medium text-black">{notification.title}</p>
                                  <p className="text-sm text-black mt-1">{notification.message}</p>
                                  <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                                </div>
                                {!notification.read && (
                                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                      <div className="p-3 text-center border-t border-gray-200">
                        <button 
                          onClick={handleMarkAllAsRead}
                          className="text-sm text-blue-600 hover:text-blue-800"
                          disabled={isLoadingNotifications || notifications.length === 0}
                        >
                          Mark all as read
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="w-8 h-8 bg-[#E8F0FE] rounded-full flex items-center justify-center">
              <span className="text-[#0077CC] font-medium">{userInitial}</span>
            </div>
          )}
        </div>

        {/* Create Course Card */}
        {isCollapsed ? (
          <div className="p-4 border-b border-gray-200">
              <button 
                onClick={() => router.push('/main/employee/Dashboard?module=createCourse')}
                className="flex items-center justify-center gap-2 w-full cursor-pointer py-2 px-4 text-sm font-medium text-white bg-[#0077CC] rounded-lg hover:bg-[#0066B3] transition-colors"
              >
                <FiPlus size={18} />
              </button>
            </div>
        ) : (
          <div className="p-4 border-b border-gray-200">
            <div className="bg-white rounded-lg p-4 border border-gray-200">
              <button 
                onClick={() => router.push('/main/employee/Dashboard?module=createCourse')}
                className="flex items-center justify-center gap-2 w-full cursor-pointer py-2 px-4 mb-4 text-sm font-medium text-white bg-[#0077CC] rounded-lg hover:bg-[#0066B3] transition-colors"
              >
                <FiPlus size={18} />
                <span>Create New Course</span>
              </button>
              <p className="text-sm text-black">
                Share your knowledge by creating a new course.
              </p>
            </div>
          </div>
        )}

        {/* Navigation Menu */}
        <nav className={`p-3 space-y-1 ${isCollapsed ? 'px-2' : ''}`}>
          {menuItems.map((item, index) => (
            <Link
              key={index}
              href={item.path}
              className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm ${
                item.active
                  ? 'bg-[#E8F0FE] text-[#0077CC]'
                  : 'text-black hover:bg-gray-50'
              } ${isCollapsed ? 'justify-center' : ''}`}
              title={isCollapsed ? item.title : ''}
            >
              <span className={item.active ? 'text-[#0077CC]' : 'text-black'}>{item.icon}</span>
              {!isCollapsed && <span className="font-medium">{item.title}</span>}
            </Link>
          ))}
          <button
            onClick={() => signOut({ callbackUrl: '/' })}
            className={`w-full cursor-pointer flex items-center gap-3 px-3 py-2 rounded-lg text-sm text-red-600 hover:bg-red-50 ${
              isCollapsed ? 'justify-center' : ''
            }`}
            title={isCollapsed ? 'Logout' : ''}
          >
            <FiLogOut size={20} />
            {!isCollapsed && <span className="font-medium">Logout</span>}
          </button>
        </nav>
      </aside>

      {/* Toggle Button */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="fixed -ml-4 left-[280px] top-1/2 z-50 bg-white p-2 rounded-full shadow-lg shadow-inherit cursor-pointer border border-gray-200 transition-all duration-300 hover:bg-blue-50"
        style={{ 
          left: isCollapsed ? '80px' : '280px',
          transform: isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)'
        }}
      >
        <FiChevronLeft size={20} className="text-black" />
      </button>
    </>
  );
};

export default Sidebar;
