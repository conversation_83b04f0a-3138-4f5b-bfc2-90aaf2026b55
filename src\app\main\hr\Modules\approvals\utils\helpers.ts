'use client';

import React from 'react';

export const highlightSearchMatches = (text: string, searchQuery: string): React.ReactElement | string => {
  if (!searchQuery.trim()) return text;
  
  const parts = text.split(new RegExp(`(${searchQuery.trim()})`, 'gi'));
  
  return React.createElement(
    React.Fragment,
    null,
    parts.map((part: string, i: number) => 
      part.toLowerCase() === searchQuery.toLowerCase()
        ? React.createElement('span', { key: i, className: 'text-blue-600 font-semibold' }, part)
        : part
    )
  );
}; 