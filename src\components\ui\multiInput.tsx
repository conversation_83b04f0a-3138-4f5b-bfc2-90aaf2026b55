import { Input } from "./input"
import { Label } from "./label"
import { Button } from "./Button"
import { FiPlus, FiX } from "react-icons/fi"
import { useState } from "react"

interface MultiInputProps {
  label?: string;
  values: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
}

const MultiInput: React.FC<MultiInputProps> = ({ 
  label, 
  values, 
  onChange, 
  placeholder = "Enter item...",
  required = false,
  error
}) => {
  const [isRotating, setIsRotating] = useState(false);

  const addItem = () => {
    setIsRotating(true);
    onChange([...values, ""]);
    // Reset rotation after animation completes
    setTimeout(() => setIsRotating(false), 500);
  }

  const removeItem = (index: number) => {
    const newValues = values.filter((_, i) => i !== index)
    onChange(newValues)
  }

  const updateItem = (index: number, value: string) => {
    const newValues = [...values]
    newValues[index] = value
    onChange(newValues)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-start items-center">
        <Label className="block">{label}</Label>
        <Button
          type="button"
          onClick={addItem}
          className="text-sm cursor-pointer text-color3 hover:text-color3/95 flex items-center gap-1"
        >
          <div className={`transform transition-transform duration-300 ${isRotating ? 'rotate-180' : ''}`}>
            <FiPlus size={16} />
          </div>
          Add Objective
        </Button>
      </div>

      <div className="space-y-2">
        {values.map((value, index) => (
          <div key={index} className="flex gap-2 items-center">
            <Input
              value={value}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateItem(index, e.target.value)}
              placeholder={placeholder}
              required={required && index === 0}
              className={`flex-1 ${error ? 'border-red-500' : ''}`}
            />
            <Button
              type="button"
              onClick={() => removeItem(index)}
              className="text-red-500 cursor-pointer hover:text-red-600 p-2"
            >
              <FiX size={16} />
            </Button>
          </div>
        ))}
      </div>

      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}

      <style jsx>{`
        @keyframes rotate360 {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(180deg);
          }
        }
        
        .rotate-360 {
          animation: rotate360 0.3s ease-in-out;
        }
      `}</style>
    </div>
  )
}

export default MultiInput 