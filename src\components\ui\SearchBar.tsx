'use client';

import React, { useState, useCallback } from 'react';
import { Search } from 'lucide-react';

interface SearchBarProps {
  onSearch?: (query: string, callback: (count: number) => void) => void;
  placeholder?: string;
  title?: string;
  subtitle?: string;
  showResultCount?: boolean;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch = () => {},
  placeholder = "Search...",
  title = "Search",
  subtitle = "Search through items",
  showResultCount = true,
  className = ""
}) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [resultCount, setResultCount] = useState<number>(0);

  const handleSearch = useCallback((query: string) => {
    setIsSearching(true);
    onSearch(query, (count: number) => {
      setResultCount(count);
      setIsSearching(false);
    });
  }, [onSearch]);

  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear previous timeout
    if (searchTimeout) clearTimeout(searchTimeout);

    // Set new timeout
    const timeoutId = setTimeout(() => {
      handleSearch(query);
    }, 300);

    setSearchTimeout(timeoutId);
  };

  const handleSearchSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchTimeout) clearTimeout(searchTimeout);
    handleSearch(searchQuery);
  };

  const handleClear = () => {
    setSearchQuery('');
    handleSearch('');
  };

  return (
    <div className={`bg-transparent rounded-lg ${className}`}>
      {title && <h2 className="text-lg font-semibold mb-2">{title}</h2>}
      {subtitle && <p className="text-gray-600 text-sm mb-4">{subtitle}</p>}

      <form onSubmit={handleSearchSubmit} className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-5 h-5" />
        <input
          type="text"
          value={searchQuery}
          onChange={handleSearchInput}
          placeholder={placeholder}
          className="w-full pl-10 pr-16 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <button
          type="submit"
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#0077CC] text-white px-4 py-1.5 rounded-md text-sm hover:bg-[#0066B3] transition-colors"
        >
          Search
        </button>
      </form>

      {showResultCount && (
        <div className="flex justify-between items-center mt-4">
          {isSearching ? (
            <span className="text-sm text-gray-500">Searching...</span>
          ) : (
            <span className="text-sm text-gray-500">
              {resultCount} results found
            </span>
          )}
          {searchQuery && (
            <button
              onClick={handleClear}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear search
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
