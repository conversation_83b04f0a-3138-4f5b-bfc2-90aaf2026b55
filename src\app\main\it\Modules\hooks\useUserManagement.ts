import { useState, useMemo } from 'react';
import { userService } from '../services/userService';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  department: string;
  status: string;
  lastLogin: string;
}

interface UseUserManagementProps {
  users: User[];
  itemsPerPage?: number;
}

interface UseUserManagementReturn {
  currentUsers: User[];
  totalPages: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  roleFilter: string;
  setRoleFilter: (role: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  filteredUsers: User[];
  exportUsers: () => Promise<void>;
  isLoading: boolean;
}

export const useUserManagement = ({
  users,
  itemsPerPage = 5
}: UseUserManagementProps): UseUserManagementReturn => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Apply filters
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const matchesSearch = searchQuery
        ? `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.department.toLowerCase().includes(searchQuery.toLowerCase())
        : true;

      const matchesRole = roleFilter ? user.role === roleFilter : true;
      const matchesStatus = statusFilter ? user.status === statusFilter : true;

      return matchesSearch && matchesRole && matchesStatus;
    });
  }, [users, searchQuery, roleFilter, statusFilter]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  
  // Get current page users
  const currentUsers = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredUsers.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredUsers, currentPage, itemsPerPage]);

  // Reset to first page when filters change
  useMemo(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [searchQuery, roleFilter, statusFilter]);

  const exportUsers = async () => {
    try {
      setIsLoading(true);
      const filters = {
        searchQuery,
        role: roleFilter,
        status: statusFilter,
      };

      const blob = await userService.exportUsers(filters, filteredUsers);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    currentUsers,
    totalPages,
    currentPage,
    setCurrentPage,
    searchQuery,
    setSearchQuery,
    roleFilter,
    setRoleFilter,
    statusFilter,
    setStatusFilter,
    filteredUsers,
    exportUsers,
    isLoading,
  };
}; 