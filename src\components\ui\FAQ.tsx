import React from 'react';
import Logo from '../../../public/images/FAQ.svg';
import Image from 'next/image';
interface FAQProps {
  toggleModal: () => void;
}

const FAQ: React.FC<FAQProps> = ({ toggleModal }) => {
  return (
    <div>
      <Image
        src={Logo} 
        className='fixed size-[70px] hover:scale-110 duration-300 cursor-pointer bottom-3 right-5'
        onClick={toggleModal}
        alt='FAQ Icon'
      />
    </div>
  );
};

export default FAQ;


