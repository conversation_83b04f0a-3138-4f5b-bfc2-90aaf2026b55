import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { MaterialStatus, Role } from '@prisma/client'; 
import { subDays } from 'date-fns';

export async function GET(request: NextRequest) {
  try {

    const twentyDaysAgo = subDays(new Date(), 20);

    const pendingMaterials = await prisma.trainingMaterial.findMany({
      where: {
        status: MaterialStatus.PENDING_APPROVAL, 
        updatedAt: { 
          lt: twentyDaysAgo,
        },

      },
      select: {
        id: true,
        title: true,
      },
    });

    if (pendingMaterials.length === 0) {
      return NextResponse.json({ message: 'No overdue pending materials found.' });
    }

    const adminUsers = await prisma.user.findMany({
      where: {
        role: {
          is: { 
            roleName: { in: ["HR_ADMIN", "ADMIN"] } 
          }
        }
      },
      select: { id: true },
    });

    if (adminUsers.length === 0) {
      console.warn('No admin users found to send overdue material notifications.');
      return NextResponse.json({ message: 'Overdue materials found, but no admin users to notify.' });
    }

    let notificationsCreatedCount = 0;
    const notificationPromises = pendingMaterials.flatMap(material => {
      const notificationMessage = `Training material \"${material.title}\" has been pending approval for over 20 days.`;
      const notificationType = 'MATERIAL_PENDING_OVERDUE';
      
      return adminUsers.map(adminUser => 
        prisma.systemNotification.create({
          data: {
            recipientUserId: adminUser.id,
            message: notificationMessage,
            type: notificationType,
            relatedEntityType: 'TRAINING_MATERIAL',
            relatedEntityId: material.id,
            isRead: false,
          },
        })
      );
    });

    const results = await Promise.allSettled(notificationPromises);
    results.forEach(result => {
      if (result.status === 'fulfilled') {
        notificationsCreatedCount++;
      } else {
        console.error('Failed to create a pending overdue notification:', result.reason);
      }
    });

    return NextResponse.json({ 
      message: `Checked for overdue materials. Found ${pendingMaterials.length}. Created ${notificationsCreatedCount} notifications.`,
      overdueMaterialsCount: pendingMaterials.length,
      notificationsSent: notificationsCreatedCount 
    });

  } catch (error) {
    console.error('[CHECK_PENDING_MATERIALS_GET] Error:', error);
    return NextResponse.json(
      { error: 'Failed to check for overdue pending materials.' },
      { status: 500 }
    );
  }
} 