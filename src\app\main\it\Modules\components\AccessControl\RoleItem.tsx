import React from 'react';

export interface Role {
  id: string;
  name: string;
  description: string;
  users: number;
  permissionLevel: 'Super Admin' | 'Admin' | 'Manager' | 'Standard';
}

interface RoleItemProps {
  role: Role;
  onEdit: (role: Role) => void;
}

const RoleItem: React.FC<RoleItemProps> = ({ role, onEdit }) => {
  const getBadgeColor = (level: Role['permissionLevel']) => {
    const colors = {
      'Super Admin': 'bg-blue-100 text-blue-800',
      'Admin': 'bg-purple-100 text-purple-800',
      'Manager': 'bg-yellow-100 text-yellow-800',
      'Standard': 'bg-gray-100 text-gray-800'
    };
    return colors[level];
  };

  return (
    <tr className="border-b border-gray-200">
      <td className="py-4 px-6 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-800">{role.name}</div>
      </td>
      <td className="py-4 px-6">
        <div className="text-sm text-gray-500">{role.description}</div>
      </td>
      <td className="py-4 px-6 whitespace-nowrap">
        <div className="text-sm text-gray-500">{role.users}</div>
      </td>
      <td className="py-4 px-6 whitespace-nowrap">
        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getBadgeColor(role.permissionLevel)}`}>
          {role.permissionLevel}
        </span>
      </td>
      <td className="py-4 px-6 whitespace-nowrap text-right">
        <button
          onClick={() => onEdit(role)}
          className="inline-flex items-center gap-1 text-gray-400 hover:text-color3"
          title="Edit Role"
        >
          <span className="text-sm">Edit</span>
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
            />
          </svg>
        </button>
      </td>
    </tr>
  );
};

export default RoleItem; 