import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { MaterialStatus } from '@prisma/client';
import { z } from 'zod';

const rejectRequestSchema = z.object({
  rejectionReason: z.string().min(1, 'Rejection reason is required').trim()
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'HR_ADMIN') {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const materialId = parseInt(params.id);
    if (isNaN(materialId)) {
      return NextResponse.json({ error: 'Invalid material ID format.' }, { status: 400 });
    }
    
    let rawBody;
    try {
      rawBody = await request.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const validationResult = rejectRequestSchema.safeParse(rawBody);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { rejectionReason } = validationResult.data;

    const result = await prisma.$transaction(async (tx) => {
      const currentMaterial = await tx.trainingMaterial.findUnique({
        where: { id: materialId },
        select: { status: true }
      });

      if (!currentMaterial) {
        throw new Error('Material not found');
      }
      const oldStatus = currentMaterial.status;

      const material = await tx.trainingMaterial.update({
        where: { id: materialId },
        data: {
          status: MaterialStatus.ARCHIVED,
          publishedAt: null
        },
        select: {
          id: true,
          title: true,
          trainingParticipants: {
            where: { isCreator: true },
            select: { user: { select: { id: true } } }
          }
        }
      });

      await tx.materialApproval.create({
        data: {
          trainingMaterialId: materialId,
          approverUserId: parseInt(session.user.id),
          status: MaterialStatus.ARCHIVED,
          comments: rejectionReason.trim()
        }
      });

      await tx.materialStatusLog.create({
        data: {
          trainingMaterialId: materialId,
          changedByUserId: parseInt(session.user.id),
          oldStatus: oldStatus,
          newStatus: MaterialStatus.ARCHIVED,
          reason: rejectionReason.trim()
        }
      });

      if (material.trainingParticipants.length > 0 && material.trainingParticipants[0].user) {
        await tx.systemNotification.create({
          data: {
            recipientUserId: material.trainingParticipants[0].user.id,
            message: `Your training material "${material.title}" has been archived. Reason: ${rejectionReason.trim()}`,
            type: 'MATERIAL_STATUS_CHANGE',
            relatedEntityType: 'TRAINING_MATERIAL',
            relatedEntityId: material.id
          }
        });
      }

      return material;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error rejecting training material:', error);
    if (error instanceof Error && error.message === 'Material not found') {
      return NextResponse.json({ error: 'Material not found' }, { status: 404 });
    }
    return NextResponse.json(
      { error: 'Failed to reject training material' },
      { status: 500 }
    );
  }
}