import { rm } from 'fs/promises';
import path from 'path';

export async function cleanupFiles(materialId: string | number) {
  try {
    // Clean up the entire material directory including modules, certificates, and assessments
    const materialDir = path.join(process.cwd(), 'storage', 'training-materials', materialId.toString());
    await rm(materialDir, { recursive: true, force: true });
  } catch (error) {
    console.error('Error cleaning up files:', error);
  }
}

export async function cleanupModuleFiles(materialId: string | number, moduleId: string | number) {
  try {
    // Clean up only the specific module directory
    const moduleDir = path.join(process.cwd(), 'storage', 'training-materials', materialId.toString(), 'modules', moduleId.toString());
    await rm(moduleDir, { recursive: true, force: true });
  } catch (error) {
    console.error('Error cleaning up module files:', error);
  }
}