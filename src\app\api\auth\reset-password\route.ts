import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';

const passwordComplexityRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;


export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { token, newPassword } = body;


    if (!token || typeof token !== 'string') {
      return NextResponse.json({ message: 'Reset token is missing or invalid.' }, { status: 400 });
    }
    if (!newPassword || typeof newPassword !== 'string') {
      return NextResponse.json({ message: 'New password is missing or invalid.' }, { status: 400 });
    }

 
    if (!passwordComplexityRegex.test(newPassword)) {
      return NextResponse.json({
        message: 'Password does not meet complexity requirements. It must contain at least 8 characters, one uppercase letter, one lowercase letter, one number, and one special character.',
      }, { status: 400 });
    }

    const hashedToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    const user = await prisma.user.findFirst({
      where: {
        passwordResetToken: hashedToken,
        passwordResetExpires: {
          gt: new Date(), 
        },
      },
    });
    if (!user) {
      return NextResponse.json({ message: 'Invalid or expired password reset token.' }, { status: 400 });
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null, 
        passwordResetExpires: null, 
        failedLoginAttempts: 0, 
        accountLockedUntil: null, 
      },
    });
    return NextResponse.json({ message: 'Password has been reset successfully.' }, { status: 200 });

  } catch (error) {
    console.error("Reset Password Error:", error);
    return NextResponse.json({ message: 'An internal server error occurred while resetting the password.' }, { status: 500 });
  }
}
