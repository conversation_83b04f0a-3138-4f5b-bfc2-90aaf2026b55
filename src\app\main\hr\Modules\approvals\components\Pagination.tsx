'use client';

import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { PaginationProps } from '../utils/types';

const Pagination: React.FC<PaginationProps> = ({ 
  currentPage, 
  totalPages, 
  totalItems, 
  indexOfFirstItem, 
  indexOfLastItem, 
  handlePageChange 
}) => {
  // Function to render page numbers with ellipses
  const renderPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5; // Max pages to show at once
    
    // Always show first page
    pageNumbers.push(
      <button
        key={1}
        onClick={() => handlePageChange(1)}
        className={`px-3 py-1 cursor-pointer rounded-lg border ${
          currentPage === 1
            ? 'bg-color3 text-white border-color3'
            : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
      >
        1
      </button>
    );
    
    // Calculate range of pages to show around current page
    const startPage = Math.max(2, currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(totalPages - 1, startPage + maxPagesToShow - 3);
    
    // Adjust if we're near the beginning
    if (startPage > 2) {
      pageNumbers.push(
        <span key="ellipsis-1" className="px-2 py-1">
          ...
        </span>
      );
    }
    
    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-1 cursor-pointer rounded-lg border ${
            currentPage === i
              ? 'bg-color3 text-white border-color3'
              : 'bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          {i}
        </button>
      );
    }
    
    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      pageNumbers.push(
        <span key="ellipsis-2" className="px-2 py-1">
          ...
        </span>
      );
    }
    
    // Always show last page if there is more than one page
    if (totalPages > 1) {
      pageNumbers.push(
        <button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          className={`px-3 py-1 cursor-pointer rounded-lg border ${
            currentPage === totalPages
              ? 'bg-color3 text-white border-color3'
              : 'bg-white text-gray-700 hover:bg-gray-50'
          }`}
        >
          {totalPages}
        </button>
      );
    }
    
    return pageNumbers;
  };

  return (
    <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
      <div className="flex-1 flex justify-between items-center">
        <p className="text-sm text-gray-700">
          {totalItems > 0 ? (
            <>
              Showing <span className="font-medium">{indexOfFirstItem}</span> to{' '}
              <span className="font-medium">
                {indexOfLastItem > totalItems ? totalItems : indexOfLastItem}
              </span>{' '}
              of <span className="font-medium">{totalItems}</span> results
            </>
          ) : (
            'No results found'
          )}
        </p>

        {totalItems > 0 && (
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`p-2 cursor-pointer rounded-lg border ${
                currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            
            {renderPageNumbers()}
            
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`p-2 cursor-pointer rounded-lg border ${
                currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Pagination;