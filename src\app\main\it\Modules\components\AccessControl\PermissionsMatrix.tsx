import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, FiLoader } from 'react-icons/fi';

interface Permission {
  name: string;
  roles: {
    [key: string]: boolean;
  };
}

const initialPermissions: Permission[] = [
  {
    name: 'View Knowledge Resources',
    roles: {
      'IT': true,
      'HR': true,
      'Training Unit': true,
      'Employee': true
    }
  },
  {
    name: 'Upload Personal Files',
    roles: {
      'IT': true,
      'HR': true,
      'Training Unit': true,
      'Employee': true
    }
  },
  {
    name: 'Upload Training Materials',
    roles: {
      'IT': true,
      'HR': true,
      'Training Unit': true,
      'Employee': false
    }
  },
  {
    name: 'Approve Training Materials',
    roles: {
      'IT': true,
      'HR': false,
      'Training Unit': true,
      'Employee': false
    }
  },
  {
    name: 'Manage Users',
    roles: {
      'IT': true,
      'HR': true,
      'Training Unit': false,
      'Employee': false
    }
  },
  {
    name: 'View Analytics',
    roles: {
      'IT': true,
      'HR': true,
      'Training Unit': true,
      'Employee': false
    }
  },
  {
    name: 'System Configuration',
    roles: {
      'IT': true,
      'HR': false,
      'Training Unit': false,
      'Employee': false
    }
  },
  {
    name: 'Security Management',
    roles: {
      'IT': true,
      'HR': false,
      'Training Unit': false,
      'Employee': false
    }
  },
  {
    name: 'HR Records Management',
    roles: {
      'IT': true,
      'HR': true,
      'Training Unit': false,
      'Employee': false
    }
  }
];

const roles = ['IT', 'HR', 'Training Unit', 'Employee'];

const PermissionsMatrix = () => {
  const [permissions, setPermissions] = useState<Permission[]>(initialPermissions);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handlePermissionToggle = (permissionName: string, role: string) => {
    setPermissions(prevPermissions => 
      prevPermissions.map(permission => 
        permission.name === permissionName
          ? {
              ...permission,
              roles: {
                ...permission.roles,
                [role]: !permission.roles[role]
              }
            }
          : permission
      )
    );
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      console.log('Saving permissions:', permissions);
      
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Permission Matrix</h2>
        <p className="text-gray-500">Configure what each role can access and modify in the system</p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th scope="col" className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Permission
              </th>
              {roles.map((role) => (
                <th 
                  key={role}
                  scope="col" 
                  className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {role}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {permissions.map((permission) => (
              <tr key={permission.name} className="hover:bg-gray-50">
                <td className="py-3 px-4 text-sm text-gray-900">
                  {permission.name}
                </td>
                {roles.map((role) => (
                  <td 
                    key={role} 
                    className="py-3 px-4"
                    onClick={() => handlePermissionToggle(permission.name, role)}
                  >
                    <button 
                      className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                      disabled={isSaving}
                    >
                      {permission.roles[role] ? (
                        <FiCheck className="w-5 h-5 text-green-500" />
                      ) : (
                        <FiX className="w-5 h-5 text-red-500" />
                      )}
                    </button>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-between items-center mt-6">
        {saveStatus === 'success' && (
          <p className="text-sm text-green-600">Changes saved successfully!</p>
        )}
        {saveStatus === 'error' && (
          <p className="text-sm text-red-600">Error saving changes. Please try again.</p>
        )}
        {saveStatus === 'idle' && <div />}
        
        <button
          type="button"
          onClick={handleSave}
          disabled={isSaving}
          className={`px-4 py-2 text-sm font-medium text-white bg-color3 rounded-lg hover:bg-color3/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3 flex items-center gap-2 ${
            isSaving ? 'opacity-75 cursor-not-allowed' : ''
          }`}
        >
          {isSaving && <FiLoader className="w-4 h-4 animate-spin" />}
          {isSaving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
};

export default PermissionsMatrix; 