import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { MaterialStatus, ProgressStatus } from '@prisma/client';
import { z } from 'zod';

// Schemas for the response
const TrainingStatsSchema = z.object({
  totalActiveLearners: z.number().int().nonnegative(),
  overallCompletionRate: z.number().min(0).max(100).nullable(), // Percentage, can be null if no data
  averageTimeToCompletion: z.string(), // e.g., "X days Y hours" or "N/A"
  totalPublishedCourses: z.number().int().nonnegative(),
});

const PopularCourseSchema = z.object({
  name: z.string(),
  enrollments: z.number().int().nonnegative(),
});

const MonthlyProgressPointSchema = z.object({
  month: z.string(),
  coursesCompleted: z.number().int().nonnegative(),
});

const TrainingAnalyticsResponseSchema = z.object({
  stats: TrainingStatsSchema,
  popularCourses: z.array(PopularCourseSchema),
  monthlyProgress: z.array(MonthlyProgressPointSchema),
});

const ErrorResponseSchema = z.object({
  error: z.string(),
});

// Helper function to format duration
function formatDuration(milliseconds: number): string {
  if (milliseconds <= 0) return "N/A";
  let seconds = Math.floor(milliseconds / 1000);
  let minutes = Math.floor(seconds / 60);
  let hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  hours = hours % 24;
  minutes = minutes % 60;
  
  const parts: string[] = [];
  if (days > 0) parts.push(`${days} day${days > 1 ? 's' : ''}`);
  if (hours > 0) parts.push(`${hours} hour${hours > 1 ? 's' : ''}`);
  if (minutes > 0 && days === 0) parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`); // Show minutes only if days are 0
  if (parts.length === 0 && seconds > 0) return "< 1 minute";
  if (parts.length === 0) return "N/A";

  return parts.join(', ');
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(ErrorResponseSchema.parse({ error: 'Unauthorized' }), { status: 401 });
    }

    // 0. General Training Stats
    const activeLearnerUsers = await prisma.userTrainingProgress.findMany({
      where: { status: ProgressStatus.IN_PROGRESS },
      distinct: ['userId'],
      select: { userId: true },
    });
    const totalActiveLearners = activeLearnerUsers.length;

    const progressCounts = await prisma.userTrainingProgress.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });
    const completedCount = progressCounts.find(pc => pc.status === ProgressStatus.COMPLETED)?._count.status || 0;
    const inProgressCount = progressCounts.find(pc => pc.status === ProgressStatus.IN_PROGRESS)?._count.status || 0;
    const overallCompletionRate = (completedCount + inProgressCount) > 0 
      ? Math.round((completedCount / (completedCount + inProgressCount)) * 100) 
      : null;

    const twelveMonthsAgoDate = new Date(); // Renamed for clarity
    twelveMonthsAgoDate.setMonth(twelveMonthsAgoDate.getMonth() - 12);
    twelveMonthsAgoDate.setDate(1);
    twelveMonthsAgoDate.setHours(0, 0, 0, 0);

    const recentCompletions = await prisma.userTrainingProgress.findMany({
      where: {
        status: ProgressStatus.COMPLETED,
        completedAt: { gte: twelveMonthsAgoDate },
        startedAt: { not: null }, // Ensure startedAt is not null
      },
      select: { startedAt: true, completedAt: true },
    });

    let totalDurationMs = 0;
    let validCompletionsForAvg = 0;
    recentCompletions.forEach(p => {
      if (p.completedAt && p.startedAt) {
        const duration = new Date(p.completedAt).getTime() - new Date(p.startedAt).getTime();
        if (duration > 0) { // Ensure duration is positive
            totalDurationMs += duration;
            validCompletionsForAvg++;
        }
      }
    });
    const averageTimeMs = validCompletionsForAvg > 0 ? totalDurationMs / validCompletionsForAvg : 0;
    const averageTimeToCompletion = formatDuration(averageTimeMs);

    const totalPublishedCourses = await prisma.trainingMaterial.count({
      where: { status: MaterialStatus.PUBLISHED },
    });

    const stats: z.infer<typeof TrainingStatsSchema> = {
      totalActiveLearners,
      overallCompletionRate,
      averageTimeToCompletion,
      totalPublishedCourses,
    };

    // 1. Popular Courses (by Enrollment)
    const popularCoursesData = await prisma.trainingMaterial.findMany({
      where: { status: MaterialStatus.PUBLISHED },
      select: { title: true, _count: { select: { trainingParticipants: true } } },
      orderBy: { trainingParticipants: { _count: 'desc' } },
      take: 10,
    });
    const popularCourses = popularCoursesData.map(course => PopularCourseSchema.parse({
      name: course.title,
      enrollments: course._count.trainingParticipants,
    }));

    // 2. Monthly Progress (Courses Completed)
    const completedTrainings = await prisma.userTrainingProgress.findMany({
      where: {
        status: ProgressStatus.COMPLETED,
        completedAt: { gte: twelveMonthsAgoDate },
      },
      select: { completedAt: true },
      orderBy: { completedAt: 'asc' },
    });

    const monthlyProgressMap = new Map<string, number>();
    const monthFormatter = new Intl.DateTimeFormat('en-US', { month: 'short', year: 'numeric' });
    for (let i = 11; i >= 0; i--) { // Corrected loop to go from past to present for initialization
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      date.setDate(1); // Ensure it's the first of the month for consistent key generation
      const monthKey = monthFormatter.format(date);
      monthlyProgressMap.set(monthKey, 0);
    }
    completedTrainings.forEach(progress => {
      if (progress.completedAt) {
        const monthKey = monthFormatter.format(new Date(progress.completedAt));
        if (monthlyProgressMap.has(monthKey)) {
            monthlyProgressMap.set(monthKey, (monthlyProgressMap.get(monthKey) || 0) + 1);
        }
      }
    });
    const monthlyProgress = Array.from(monthlyProgressMap.entries())
      .map(([month, coursesCompleted]) => MonthlyProgressPointSchema.parse({ month, coursesCompleted }))
      .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime());

    const response = TrainingAnalyticsResponseSchema.parse({
      stats,
      popularCourses,
      monthlyProgress,
    });
    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching training analytics:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(ErrorResponseSchema.parse({ error: `Validation error: ${error.errors.map(e => e.message).join(', ')}` }), { status: 400 });
    }
    return NextResponse.json(ErrorResponseSchema.parse({ error: 'Failed to fetch training analytics' }), { status: 500 });
  }
} 