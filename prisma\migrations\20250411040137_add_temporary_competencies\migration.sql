/*
  Warnings:

  - The primary key for the `material_competencies` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `id` on the `material_competencies` table. All the data in the column will be lost.
  - You are about to drop the column `temporary_competency_id` on the `material_competencies` table. All the data in the column will be lost.
  - You are about to drop the column `createdByUserId` on the `temporary_competencies` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `temporary_competencies` table. All the data in the column will be lost.
  - You are about to alter the column `status` on the `temporary_competencies` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - A unique constraint covering the columns `[competencyName]` on the table `temporary_competencies` will be added. If there are existing duplicate values, this will fail.
  - Made the column `competency_id` on table `material_competencies` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "material_competencies" DROP CONSTRAINT "material_competencies_temporary_competency_id_fkey";

-- DropForeignKey
ALTER TABLE "temporary_competencies" DROP CONSTRAINT "temporary_competencies_createdByUserId_fkey";

-- DropIndex
DROP INDEX "material_competencies_training_material_id_competency_id_te_key";

-- AlterTable
ALTER TABLE "material_competencies" DROP CONSTRAINT "material_competencies_pkey",
DROP COLUMN "id",
DROP COLUMN "temporary_competency_id",
ALTER COLUMN "competency_id" SET NOT NULL,
ADD CONSTRAINT "material_competencies_pkey" PRIMARY KEY ("training_material_id", "competency_id");

-- AlterTable
ALTER TABLE "temporary_competencies" DROP COLUMN "createdByUserId",
DROP COLUMN "description",
ADD COLUMN     "createdBy" INTEGER,
ADD COLUMN     "reviewedAt" TIMESTAMP(3),
ADD COLUMN     "reviewedBy" INTEGER,
ALTER COLUMN "status" DROP DEFAULT,
ALTER COLUMN "status" SET DATA TYPE VARCHAR(50);

-- CreateIndex
CREATE UNIQUE INDEX "temporary_competencies_competencyName_key" ON "temporary_competencies"("competencyName");

-- AddForeignKey
ALTER TABLE "temporary_competencies" ADD CONSTRAINT "temporary_competencies_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "temporary_competencies" ADD CONSTRAINT "temporary_competencies_reviewedBy_fkey" FOREIGN KEY ("reviewedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
