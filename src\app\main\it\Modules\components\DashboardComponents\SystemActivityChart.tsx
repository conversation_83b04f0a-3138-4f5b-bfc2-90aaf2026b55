import React, { memo, useRef, useEffect, useState } from 'react';
import { <PERSON>C<PERSON>, Bar, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { time: '00:00', activeUsers: 120, fileUploads: 45, searches: 80 },
  { time: '04:00', activeUsers: 80, fileUploads: 30, searches: 60 },
  { time: '08:00', activeUsers: 200, fileUploads: 90, searches: 150 },
  { time: '12:00', activeUsers: 250, fileUploads: 120, searches: 200 },
  { time: '16:00', activeUsers: 220, fileUploads: 100, searches: 180 },
  { time: '20:00', activeUsers: 150, fileUploads: 70, searches: 120 },
  { time: '24:00', activeUsers: 100, fileUploads: 40, searches: 90 },
];

const SystemActivityChart: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [key, setKey] = useState(0);

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      // Debounce the resize event
      const timeout = setTimeout(() => {
        setKey(prev => prev + 1);
      }, 100);

      return () => clearTimeout(timeout);
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 sm:p-6 flex-1 flex flex-col h-full min-h-0"
      style={{ contain: 'content' }}
    >
      <div className="mb-4">
        <h2 className="text-lg font-semibold text-gray-800">System Activity</h2>
        <p className="text-gray-500 text-xs sm:text-sm">Real-time system metrics</p>
      </div>
      
      <div className="flex-1 min-h-0" style={{ contain: 'size' }}>
        <ResponsiveContainer key={key} width="100%" height="100%">
          <BarChart data={data} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
            <XAxis 
              dataKey="time" 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={{ stroke: '#e5e7eb' }}
            />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '0.375rem',
                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend 
              verticalAlign="top" 
              height={36}
              wrapperStyle={{ paddingBottom: '1rem' }}
            />
            <Bar dataKey="activeUsers" name="Active Users" fill="#3B82F6" radius={[4, 4, 0, 0]} />
            <Bar dataKey="fileUploads" name="File Uploads" fill="#22C55E" radius={[4, 4, 0, 0]} />
            <Bar dataKey="searches" name="Searches" fill="#F59E42" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default memo(SystemActivityChart); 