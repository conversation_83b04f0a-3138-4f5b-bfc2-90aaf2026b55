"use client"

import React, { useState } from 'react'
import UserHeader from './components/UsermanagementComponents/UserHeader'
import UserFilters from './components/UsermanagementComponents/UserFilters'
import UserActions from './components/UsermanagementComponents/UserActions'
import UserTable from './components/UsermanagementComponents/UserTable'
import AddUserModal from './components/UsermanagementComponents/AddUserModal'
import { useUserManagement } from './hooks/useUserManagement'

// Mock data for initial development
const mockUsers = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    role: 'IT Admin',
    department: 'IT Department',
    status: 'Active',
    lastLogin: '2 minutes ago'
  },
  {
    id: '2',
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    role: 'HR Admin',
    department: 'Human Resources',
    status: 'Active',
    lastLogin: '1 hour ago'
  },
  {
    id: '3',
    firstName: '<PERSON>',
    lastName: 'Chen',
    email: '<EMAIL>',
    role: 'Employee',
    department: 'Engineering',
    status: 'Active',
    lastLogin: '3 hours ago'
  },
  {
    id: '4',
    firstName: 'Emily',
    lastName: 'Rodriguez',
    email: '<EMAIL>',
    role: 'Employee',
    department: 'Marketing',
    status: 'Away',
    lastLogin: '1 day ago'
  },
  {
    id: '5',
    firstName: 'David',
    lastName: 'Wilson',
    email: '<EMAIL>',
    role: 'Employee',
    department: 'Sales',
    status: 'Inactive',
    lastLogin: '2 weeks ago'
  },
  // Additional mock users
  {
    id: '6',
    firstName: 'Alexandra',
    lastName: 'Kim',
    email: '<EMAIL>',
    role: 'HR Admin',
    department: 'Human Resources',
    status: 'Active',
    lastLogin: '5 minutes ago'
  },
  {
    id: '7',
    firstName: 'James',
    lastName: 'Taylor',
    email: '<EMAIL>',
    role: 'IT Admin',
    department: 'IT Department',
    status: 'Away',
    lastLogin: '4 hours ago'
  },
  {
    id: '8',
    firstName: 'Maria',
    lastName: 'Garcia',
    email: '<EMAIL>',
    role: 'Employee',
    department: 'Engineering',
    status: 'Active',
    lastLogin: '2 days ago'
  },
  {
    id: '9',
    firstName: 'Robert',
    lastName: 'Lee',
    email: '<EMAIL>',
    role: 'Employee',
    department: 'Marketing',
    status: 'Inactive',
    lastLogin: '1 week ago'
  },
  {
    id: '10',
    firstName: 'Sophie',
    lastName: 'Anderson',
    email: '<EMAIL>',
    role: 'Employee',
    department: 'Sales',
    status: 'Active',
    lastLogin: '6 hours ago'
  }
]

export const UserManagement = () => {
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [itemsPerPage, setItemsPerPage] = useState(5)
  const {
    currentUsers,
    totalPages,
    currentPage,
    setCurrentPage,
    searchQuery,
    setSearchQuery,
    roleFilter,
    setRoleFilter,
    statusFilter,
    setStatusFilter,
    filteredUsers,
    exportUsers,
    isLoading
  } = useUserManagement({ 
    users: mockUsers,
    itemsPerPage 
  })

  const handleExport = async () => {
    await exportUsers();
  }

  const handleAddUser = () => {
    setAddModalOpen(true)
  }

  const handleAddUserSubmit = (newUser: any) => {
    // Create a new user object with the required format
    const user = {
      id: (mockUsers.length + 1).toString(),
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      email: newUser.email,
      role: newUser.role,
      department: newUser.department,
      status: 'Active',
      lastLogin: 'Just now'
    }
    mockUsers.push(user)
  }

  const handleUserAction = (userId: string, action: string) => {
    switch (action) {
      case 'update':
        // Update user in the list
        const updatedUserIndex = mockUsers.findIndex(user => user.id === userId)
        if (updatedUserIndex !== -1) {
          mockUsers[updatedUserIndex] = { ...mockUsers[updatedUserIndex], ...mockUsers.find(u => u.id === userId) }
        }
        break
      case 'deactivate':
        // Deactivate user
        const userIndex = mockUsers.findIndex(user => user.id === userId)
        if (userIndex !== -1) {
          mockUsers[userIndex] = { ...mockUsers[userIndex], status: 'Inactive' }
        }
        break
      default:
        console.log(`Unhandled action: ${action} for user ${userId}`)
    }
  }

  return (
    <div className="w-full">
      <UserHeader
        title="User Management"
        description="Manage user accounts, roles, and permissions for the ShareIT platform."
      />
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 sm:mb-0">Users</h2>
          <UserActions onExport={handleExport} onAddUser={handleAddUser} isLoading={isLoading} />
        </div>

        <UserFilters
          onSearch={setSearchQuery}
          onRoleFilter={setRoleFilter}
          onStatusFilter={setStatusFilter}
          onEntriesChange={(entries) => setItemsPerPage(entries)}
        />

        <UserTable 
          users={currentUsers} 
          onUserAction={handleUserAction}
          searchQuery={searchQuery}
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={itemsPerPage}
          totalItems={filteredUsers.length}
          onPageChange={setCurrentPage}
        />
      </div>

      <AddUserModal
        isOpen={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        onAdd={handleAddUserSubmit}
      />
    </div>
  )
}