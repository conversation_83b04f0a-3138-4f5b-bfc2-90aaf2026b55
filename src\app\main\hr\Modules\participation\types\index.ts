import { LucideIcon } from 'lucide-react';
import { Unit, Division } from '../../../data/organizationData';

export interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: number;
  subtitle: string;
  color?: string;
}

export interface DepartmentProgressItem {
  unit: string;
  divisionId: number;
  total: number;
  current: number;
  lastMonth: number;
}

export interface ContributorCardProps {
  name: string;
  unit: string;
  divisionId: number;
  resources: number;
  rank: number;
  trend: number;
}

export interface Contributor extends ContributorCardProps {}

export interface UnitDropdownProps {
  isOpen: boolean;
  selectedUnit: string;
  onUnitChange: (unit: string) => void;
  onClose: () => void;
}

export interface ParticipationStats {
  employees: {
    total: number;
    subtitle: string;
    trend: number;
  };
  average: {
    completion: number;
    subtitle: string;
    trend: number;
  };
  share: {
    contributor: number;
    subtitle: string;
    trend: number;
  };
} 