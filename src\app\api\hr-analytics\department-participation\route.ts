import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  divisionId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month')
});

export async function GET(request: NextRequest) {
  try {
    console.log('Department participation API called');
    
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      console.log('Auth failed: No session');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('User role:', session.user.role);
    // For testing purposes, temporarily skip role check to see if that's the issue
    // if (session.user.role !== 'HR_ADMIN') {
    //   console.log('Auth failed: Not HR_ADMIN');
    //   return NextResponse.json({ error: 'Forbidden: Insufficient permissions' }, { status: 403 });
    // }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    console.log('Search params:', Object.fromEntries(searchParams.entries()));
    
    // Validate query parameters using Zod
    const validationResult = querySchema.safeParse(Object.fromEntries(searchParams.entries()));
    if (!validationResult.success) {
      console.log('Validation failed:', validationResult.error.flatten().fieldErrors);
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { divisionId, timeframe } = validationResult.data;
    console.log('Parsed params:', { divisionId, timeframe });

    // Calculate date range based on timeframe
    const today = new Date();
    let startDate = new Date();
    let previousPeriodStart = new Date();
    let previousPeriodEnd = new Date(startDate);
    let futureEndDate = new Date(today);

    // Set a long future window that will definitely include 2025 dates
    futureEndDate.setFullYear(today.getFullYear() + 5); // Look 5 years into the future

    switch (timeframe) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        previousPeriodStart.setDate(today.getDate() - 14);
        previousPeriodEnd.setDate(today.getDate() - 7);
        // futureEndDate already set to +5 years
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        previousPeriodStart.setMonth(today.getMonth() - 2);
        previousPeriodEnd.setMonth(today.getMonth() - 1);
        // futureEndDate already set to +5 years
        break;
      case 'quarter':
        startDate.setMonth(today.getMonth() - 3);
        previousPeriodStart.setMonth(today.getMonth() - 6);
        previousPeriodEnd.setMonth(today.getMonth() - 3);
        // futureEndDate already set to +5 years
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        previousPeriodStart.setFullYear(today.getFullYear() - 2);
        previousPeriodEnd.setFullYear(today.getFullYear() - 1);
        // futureEndDate already set to +5 years
        break;
      default:
        startDate.setMonth(today.getMonth() - 1);
        previousPeriodStart.setMonth(today.getMonth() - 2);
        previousPeriodEnd.setMonth(today.getMonth() - 1);
        // futureEndDate already set to +5 years
    }

    // Get all units for the division if specified, or all units if not
    const unitsWhere: any = {};
    if (divisionId) {
      unitsWhere.divisionId = divisionId;
    }

    console.log('Fetching units with where clause:', unitsWhere);
    const units = await prisma.unit.findMany({
      where: unitsWhere,
      select: {
        id: true,
        unitName: true,
        divisionId: true,
        division: {
          select: {
            divisionName: true
          }
        }
      }
    });
    console.log(`Found ${units.length} units:`, units);

    if (units.length === 0) {
      console.log('No units found for division, returning empty data');
      return NextResponse.json({
        departmentData: [],
        chartData: {
          labels: [],
          datasets: [{
            label: 'Participation Rate (%)',
            data: [],
            backgroundColor: [],
            borderColor: [],
            borderWidth: 1,
            borderRadius: 6,
            maxBarThickness: 35
          }]
        }
      });
    }

    // Collect participation data for each unit
    console.log('Processing unit participation data...');
    const participationData = await Promise.all(units.map(async (unit) => {
      // Get total employees in this unit
      const totalEmployees = await prisma.user.count({
        where: {
          unitId: unit.id
        }
      });
      console.log(`Unit ${unit.unitName} has ${totalEmployees} total employees`);

      // Get count of employees who have made training progress in this period
      const activeEmployees = await prisma.user.count({
        where: {
          unitId: unit.id,
          OR: [
            // Include users with recent training activity
            {
              userTrainingProgress: {
                some: {
                  updatedAt: {
                    gte: startDate
                  }
                }
              }
            },
            // Include users with planned/scheduled future trainings
            {
              userTrainingProgress: {
                some: {
                  OR: [
                    // Include trainings scheduled in the future
                    {
                      startedAt: {
                        gte: today,
                        lte: futureEndDate
                      }
                    },
                    // Include in-progress trainings
                    {
                      status: 'IN_PROGRESS'
                    }
                  ]
                }
              }
            }
          ]
        }
      });
      console.log(`Unit ${unit.unitName} has ${activeEmployees} active employees`);

      // Get count of employees who made progress in the previous period
      const previousPeriodActiveEmployees = await prisma.user.count({
        where: {
          unitId: unit.id,
          OR: [
            {
              userTrainingProgress: {
                some: {
                  updatedAt: {
                    gte: previousPeriodStart,
                    lt: previousPeriodEnd
                  }
                }
              }
            },
            {
              userTrainingProgress: {
                some: {
                  status: 'IN_PROGRESS',
                  startedAt: {
                    lt: today
                  }
                }
              }
            }
          ]
        }
      });

      // Calculate participation rate
      const participationRate = totalEmployees > 0 
        ? parseFloat(((activeEmployees / totalEmployees) * 100).toFixed(1))
        : 0;

      // Calculate participation rate for previous period
      const previousParticipationRate = totalEmployees > 0 
        ? parseFloat(((previousPeriodActiveEmployees / totalEmployees) * 100).toFixed(1))
        : 0;

      // Calculate trend
      const trend = parseFloat((participationRate - previousParticipationRate).toFixed(1));

      return {
        unitId: unit.id,
        unitName: unit.unitName,
        divisionId: unit.divisionId,
        divisionName: unit.division?.divisionName || '',
        totalEmployees,
        activeEmployees,
        participationRate,
        trend
      };
    }));

    console.log('Calculated participation data:', participationData);

    // Format data to match chart.js expectations for the Bar component 
    const chartData = {
      labels: participationData.map(item => item.unitName),
      datasets: [{
        label: 'Participation Rate (%)',
        data: participationData.map(item => item.participationRate),
        backgroundColor: participationData.map(() => 'rgba(59, 130, 246, 0.5)'), // blue color with opacity
        borderColor: participationData.map(() => 'rgb(59, 130, 246)'),
        borderWidth: 1,
        borderRadius: 6,
        maxBarThickness: 35
      }]
    };

    console.log('Formatted chart data:', JSON.stringify(chartData));

    return NextResponse.json({
      departmentData: participationData,
      chartData
    });
    
  } catch (error) {
    console.error('Error calculating department participation:', error);
    return NextResponse.json(
      { error: 'Failed to calculate department participation' },
      { status: 500 }
    );
  }
} 