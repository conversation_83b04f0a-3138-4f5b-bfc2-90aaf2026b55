'use client'; 

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react'; 
import { useRouter } from 'next/navigation'; 
import type { IconType } from 'react-icons'; 
import Image from 'next/image'; 
import Nav from '@/components/layout/nav';
import { Button } from '@/components/ui/Button';
import Footer from '@/components/layout/footer';
import FeatureCard, { features } from '@/components/Cards/FeatureCard';
import Loader from '@/components/ui/Loader';
import Modal from '@/components/ui/modal';
import LoginForm from '@/components/ui/Loginform';
import ForgotPasswordForm from '@/components/ui/ForgotPasswordForm';

interface FeatureCardProps {
  icon: IconType;
  title: string;
  description: string;
}

// Define the possible views for the auth modal
type AuthModalView = 'login' | 'forgotPassword';

const LandingPage = () => {
  const { data: session, status } = useSession(); 
  const router = useRouter(); 
  const [modalView, setModalView] = useState<AuthModalView | null>(null);

  // Function to open the modal in login view
  const openLoginModal = () => setModalView('login');

  // Function to switch the modal view to forgotPassword
  const switchToForgotPassword = () => setModalView('forgotPassword');

  // Function to switch the modal view back to login
  const switchToLogin = () => setModalView('login');

  // Function to close the modal entirely
  const closeModal = () => setModalView(null);

  // Determine the modal title based on the current view
  const modalTitle = modalView === 'login' ? "Login to your account" : "Forgot Password";

  useEffect(() => {
   
    if (status === 'authenticated' && session?.user) {
  

      if (session.user.role === 'EMPLOYEE') {
        router.replace('/main/employee/Dashboard');
      } else if (session.user.role === 'HR_ADMIN') {
        router.replace('/main/hr/Dashboard');
      } else {
        
        console.warn(`Authenticated user with unhandled role '${session.user.role}' on landing page.`);

        router.replace('/unauthorized');
      }
    }
  }, [status, session, router]); 


  if (status === 'loading') {
    return <div className='flex items-center justify-center h-screen'><Loader /></div>
  }

  return (
    status === 'unauthenticated' && (
    <div>
      <Nav />
      {/* Hero Section - First Screen */}
      <section className="bg-white relative overflow-hidden">
        {/* Navy blue diagonal */}
        <div className="absolute left-0 top-0 w-[1000px] h-[400px] transform -translate-x-[300px] -translate-y-1/4">
          <div className="w-full h-full bg-[#E8EFF6] transform rotate-45"></div>
        </div>
        {/* Left diagonal shape */}
        <div className="absolute left-0 top-0 w-[800px] h-[700px] transform -translate-x-3/4 -translate-y-1/4">
          <div className="w-full h-full bg-[#1B2B4B] transform rotate-45"></div>
        </div>
        {/* Right diagonal shape */}
        <div className="absolute right-0 top-10 w-[600px] h-[500px] transform translate-x-2/4 -translate-y-1/4">
          <div className="w-full h-full bg-[#0066B3] transform rotate-45"></div>
        </div>
        <div className="min-h-screen flex items-center relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
            <div className="flex flex-col items-center justify-center gap-8">
              <div className="max-w-2xl text-center">
                <div className="flex items-center justify-center -mt-36 mb-10">
                  {/* <Image src={DostLogo} alt="DOST Logo" width={176} height={176} /> */}
                  <Image src="/images/dostlogo.svg" alt="DOST Logo" width={176} height={176} /> {/* Use direct string path */}
                </div>
                <h1 className="text-5xl font-bold text-black mb-6">
                  Learn from document-based courses
                </h1>
                <p className="text-xl text-gray-700 mb-4">
                  Access high-quality materials from trained professionals.
                </p>
                <p className="text-xl text-gray-700 mb-3">
                  Learn at your own pace with our document-first approach.
                </p>
              </div>
              <div className="flex gap-4">
                <Button
                  onClick={openLoginModal}
                  className="bg-color3 cursor-pointer text-white px-6 py-3 rounded-md hover:bg-[#0066B3] transition-colors flex items-center gap-2"
                >
                  Get Started
                  <span className="text-xl">→</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How it Works Section - Third Screen */}
      <section className="bg-white text-black border-t border-gray-200 relative overflow-hidden">
        <div className="min-h-screen flex items-center relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <h2 className="text-4xl font-bold text-center mb-4">How it works</h2>
            <p className="text-xl text-center mb-16">
              Learn at your own pace with our document-based approach to online education.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
              {features.map((feature: FeatureCardProps, index: number) => (
                <FeatureCard key={index} {...feature} />
              ))}
            </div>
          </div>
        </div>
      </section>

      <Footer />

      <Modal
        isOpen={modalView !== null}
        onClose={closeModal}
        title={modalTitle}
        size="small"
      >
        {modalView === 'login' && (
          <LoginForm
            onClose={closeModal}
            onForgotPassword={switchToForgotPassword}
          />
        )}
        {modalView === 'forgotPassword' && (
          <ForgotPasswordForm
            onSwitchToLogin={switchToLogin}
          />
        )}
      </Modal>
    </div>
    )
  );
};

// Export as default
export default LandingPage;
