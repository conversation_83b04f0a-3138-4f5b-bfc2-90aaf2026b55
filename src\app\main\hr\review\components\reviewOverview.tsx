'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LuLayoutGrid, <PERSON>Tag, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'react-icons/lu';
import { ReviewData, CompetencyStatus, CompetencyLink } from '../types';
import { useState, useRef, useEffect } from 'react';

// Define a specific type for the props by picking fields from ReviewData
// and adding/overriding types as needed for this component.
interface OverviewData {
  // Pick relevant fields from ReviewData
  description: ReviewData['description'];
  type: ReviewData['type'];
  language: ReviewData['language'];
  targetAudience: ReviewData['targetAudience'];
  department: ReviewData['department'];
  title: ReviewData['title'];
  // Use specific types for fields used in this component
  materialCompetencies: CompetencyLink[];
  objectives?: { id?: number; text: string; displayOrder?: number }[]; 
  category?: { id: number; categoryName: string } | string; // Allow for both possibilities based on usage
}

interface ReviewOverviewProps {
  data: OverviewData; // Use the specific type
  nextStep: () => void;
  onStatusChange?: (materialCompetencyId: number, status: CompetencyStatus) => void;
}

const ReviewOverview = ({ data, nextStep, onStatusChange }: ReviewOverviewProps) => {
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleStatusChange = (materialCompetencyId: number, newStatus: CompetencyStatus) => {
    setActiveDropdown(null);
    onStatusChange?.(materialCompetencyId, newStatus);
  };

  const getStatusColor = (status: CompetencyStatus) => {
    switch (status) {
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusBgColor = (status: CompetencyStatus) => {
    switch (status) {
      case 'approved': return 'bg-green-50';
      case 'rejected': return 'bg-red-50';
      default: return 'bg-yellow-50';
    }
  };

  return (
    <div className="max-w-5xl mx-auto space-y-8 py-8">
      {/* Course Details Header */}
      <div className="flex items-center space-x-2 text-gray-600">
        <LuLayoutGrid className="w-5 h-5" />
        <h1 className="text-xl font-medium">Course Details</h1>
      </div>

      {/* Main Content Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-6 md:p-8 space-y-6 md:space-y-8">
          {/* Course Title */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-500 uppercase">Course Title</h3>
            <h2 className="text-2xl font-semibold text-gray-900">{data.title}</h2>
          </div>

          {/* Course Type and Competencies Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-12">
            {/* Course Type */}
            <div className="relative">
              <div className="space-x-3">
                <h3 className="text-sm font-medium text-gray-500 mb-2 uppercase tracking-wider">Course Type</h3>
                <div className="bg-blue-50 rounded-xl p-5">
                  <div className="flex items-center gap-3">
                    <div className="w-14 h-14 bg-white rounded-xl shadow-sm flex items-center justify-center">
                      <LuBookOpen className="w-7 h-7 text-blue-600" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-blue-700 font-bold text-xl">{data.type}</div>
                      {data.category && (
                        <div className="text-md font-semibold text-color3">
                          {typeof data.category === 'string' 
                            ? `(${data.category})`
                            : data.category.categoryName && `${data.category.categoryName}`
                          }
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {/* Vertical Divider (visible only on desktop) */}
              <div className="hidden md:block absolute right-[-23px] top-0 bottom-0 w-px bg-gray-200"></div>
            </div>

            {/* Competencies */}
            <div className="relative space-y-3 pt-6 md:pt-0 mt-6 md:mt-0 border-t md:border-t-0 border-gray-100">
              <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Competencies</h3>
              <div className="flex flex-wrap items-start gap-2.5">
                {data.materialCompetencies?.map((competency) => {
                  const isTemporary = !!competency.temporaryCompetencyId;
                  
                  return (
                    <div key={competency.id} className="group relative">
                      <div className={`
                        inline-flex items-center gap-2 h-8 px-4 rounded-full text-sm font-medium whitespace-nowrap cursor-pointer
                        ${isTemporary 
                          ? `${getStatusBgColor(competency.status)} border border-gray-200` 
                          : 'bg-blue-50 text-blue-700 border border-blue-100'
                        }
                        transition-all duration-200 hover:shadow-sm group-hover:ring-2 group-hover:ring-offset-2 group-hover:ring-color3/20
                      `}
                      title={isTemporary ? `Click to change status - Current: ${competency.status.charAt(0).toUpperCase() + competency.status.slice(1)}` : ''}>
                        <span>{competency.name}</span>
                        {isTemporary && (
                          <div className="flex items-center gap-1.5">
                            <div className={`
                              w-2 h-2 rounded-full flex-shrink-0
                              ${competency.status === 'approved' ? 'bg-green-500' : 
                                competency.status === 'rejected' ? 'bg-red-500' : 
                                'bg-yellow-500'}
                            `} />
                            <span className="text-xs text-gray-500">
                              {competency.status === 'approved' ? 'Approved' :
                               competency.status === 'rejected' ? 'Rejected' :
                               'Pending'}
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {isTemporary && (
                        <div className="absolute right-0 top-full mt-1 opacity-0 group-hover:opacity-100 transition-all duration-200 z-10">
                          <div className="flex gap-1 shadow-lg rounded-lg bg-white p-1 border border-gray-100">
                            <button
                              onClick={() => handleStatusChange(competency.id, 'approved')}
                              className={`flex items-center gap-1.5 px-2 py-1.5 rounded-md bg-white transition-colors cursor-pointer ${competency.status === 'approved' ? 'text-gray-400' : 'text-green-600 hover:bg-green-50'}`}
                              title={competency.status === 'approved' ? 'Already Approved' : 'Approve Competency'}
                              disabled={competency.status === 'approved'}
                            >
                              <LuCheck className="w-3.5 h-3.5" />
                              <span className="text-xs font-medium">Approve</span>
                            </button>
                            <button
                              onClick={() => handleStatusChange(competency.id, 'rejected')}
                              className={`flex items-center gap-1.5 px-2 py-1.5 rounded-md bg-white transition-colors cursor-pointer ${competency.status === 'rejected' ? 'text-gray-400' : 'text-red-600 hover:bg-red-50'}`}
                              title={competency.status === 'rejected' ? 'Already Rejected' : 'Reject Competency'}
                              disabled={competency.status === 'rejected'}
                            >
                              <LuX className="w-3.5 h-3.5" />
                              <span className="text-xs font-medium">Reject</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
                {(!data.materialCompetencies || data.materialCompetencies.length === 0) && (
                  <p className="text-gray-500 italic text-sm">No competencies assigned.</p>
                )}
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-500 uppercase">Description</h3>
            <p className="text-gray-700 leading-relaxed">{data.description}</p>
          </div>

          {/* Learning Objectives */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-500 uppercase flex items-center">
              <LuBookOpen className="w-4 h-4 mr-2" />
              Learning Objectives
            </h3>
            <div className="space-y-3">
              {data.objectives?.map((objective, index) => (
                <div 
                  key={objective.id || index}
                  className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center">
                    <span className="text-sm font-medium">{objective.displayOrder ?? index + 1}</span>
                  </div>
                  <p className="text-gray-700 text-sm">{objective.text}</p>
                </div>
              ))}
              {(!data.objectives || data.objectives.length === 0) && (
                <p className="text-gray-500 italic text-sm">No learning objectives defined.</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-end">
        <button
          onClick={nextStep}
          className="px-6 py-3 bg-color3 cursor-pointer text-white rounded-lg hover:bg-color3/90 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center gap-2 text-sm font-medium"
        >
          Next: Review Resources
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ReviewOverview; 