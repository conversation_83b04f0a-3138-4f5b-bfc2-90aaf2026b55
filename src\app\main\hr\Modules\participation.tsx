'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useSidebar } from '@/components/layout/HRsidebar';
import { 
  ClipboardMinus, 
  Users, 
  CircleCheck, 
  BookOpenText, 
  TrendingUp, 
  TrendingDown, 
  ChevronDown, 
  Calendar,
  LucideIcon,
  Filter,
  Download,
  BarChart3,
  Award,
  Clock,
  Medal,
  Trophy,
  Crown
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import styles from './styles/customScrollbar.module.css';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Scale,
  CoreScaleOptions,
  ChartOptions,
  ChartDataset
} from 'chart.js';
import { StatCard } from '@/components/shared/StatCard';
import Loader from '@/components/ui/Loader';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Division color mapping (UI constants)
const divisionColors: { [key: number]: string } = {
  4: 'bg-blue-500',   // Office of the Regional Director
  5: 'bg-green-500',  // Technical Operations
  6: 'bg-purple-500', // Financial and Administrative Services
};

// Get division color helper function
const getDivisionColor = (divisionId: number): string => {
  return divisionColors[divisionId] || 'bg-gray-500'; // Fallback color
};

interface Division {
  id: number;
  divisionName: string;
  units: {
    id: number;
    unitName: string;
  }[];
}

interface DepartmentProgressItem {
  unit: string;
  divisionId: number;
  total: number;
  current: number;
  lastMonth: number;
}

interface ContributorCardProps {
  name: string;
  unit: string;
  divisionId: number;
  resources: number;
  rank: number;
  trend: number;
  badges: string[];
}

interface Contributor {
  name: string;
  unit: string;
  divisionId: number;
  resources: number;
  trend: number;
  rank: number;
}


const DepartmentProgress: React.FC<DepartmentProgressItem> = ({ unit, divisionId, total, current, lastMonth }) => {
  // Handle division by zero and NaN cases
  const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
  const previousPercentage = total > 0 ? Math.round((lastMonth / total) * 100) : 0;
  const trend = percentage - previousPercentage;
  const divisionColor = getDivisionColor(divisionId).replace('bg-', 'text-');
  
  // Get an appropriate background color based on percentage
  const getProgressColor = (value: number): string => {
    if (value >= 75) return 'bg-green-500';
    if (value >= 50) return 'bg-blue-500';
    if (value >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className='p-4 hover:bg-gray-50 rounded-lg transition-all duration-200 border border-gray-100 shadow-sm'>
      <div className='flex justify-between text-sm mb-2'>
        <div className='flex items-center gap-2'>
          <div className={`w-2.5 h-2.5 rounded-full ${getDivisionColor(divisionId)} ring-2 ring-offset-2 ring-opacity-30 ${getDivisionColor(divisionId).replace('bg-', 'ring-')}`}></div>
          <span className='font-medium text-gray-700'>{unit}</span>
        </div>
        <span className={`font-semibold ${divisionColor}`}>{isNaN(percentage) ? '0' : percentage}%</span>
      </div>
      <div className='flex items-center justify-between text-xs text-gray-500 mb-3'>
        <span>{current} of {total} employees participating</span>
        <div className={`flex items-center gap-1 px-2 py-0.5 rounded-full ${trend >= 0 ? 'text-green-700 bg-green-50' : 'text-red-700 bg-red-50'}`}>
          {trend >= 0 ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
          <span className="font-medium">{isNaN(trend) ? '0' : Math.abs(trend)}%</span>
        </div>
      </div>
      <div className='h-2.5 bg-gray-100 rounded-full overflow-hidden shadow-inner'>
        <div
          className={`h-full rounded-full transition-all duration-1000 ease-out ${getProgressColor(percentage)}`}
          style={{ 
            width: `${isNaN(percentage) ? 0 : percentage}%`, 
            opacity: 0.8,
            transition: 'width 1s ease-in-out'
          }}
        />
      </div>
    </div>
  );
};

const ContributorCard: React.FC<ContributorCardProps> = ({ name, unit, divisionId, resources, rank, trend, badges }) => {
  // Helper to determine rank icon
  const getRankIcon = () => {
    switch(rank) {
      case 1: return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2: return <Trophy className="h-5 w-5 text-gray-400" />;
      case 3: return <Medal className="h-5 w-5 text-amber-600" />;
      default: return null;
    }
  };
  
  // Helper to determine badge based on resources
  const getBadges = () => {
    const badgesList = [...badges];
    
    // Add automatic badges based on contribution count
    if (resources >= 20) badgesList.push('Expert');
    if (resources >= 10) badgesList.push('Pro');
    if (resources >= 5 && badgesList.length === 0) badgesList.push('Regular');
    
    return badgesList;
  };
  
  return (
    <div className='p-4 hover:bg-gray-50 rounded-lg transition-all duration-200 border border-gray-100 shadow-sm'>
    <div className='flex justify-between items-center'>
      <div className='flex justify-between items-center gap-3 w-full'>
        <div className='relative flex items-center gap-3'>
            <div className={`w-12 h-12 ${getDivisionColor(divisionId)} rounded-full flex items-center justify-center relative shadow-md`}>
            <span className='text-sm font-bold text-white'>{rank}</span>
              {rank <= 3 && (
                <div className="absolute -top-2 -right-2 bg-white rounded-full p-0.5 shadow-sm">
                  {getRankIcon()}
                </div>
              )}
          </div>
          <div>
            <p className='text-sm font-semibold text-gray-800'>{name}</p>
              <div className='flex items-center gap-1.5 mt-1'>
              <span className='text-xs text-gray-500'>{unit}</span>
                <div className='flex gap-1 flex-wrap mt-0.5'>
                  {getBadges().map((badge, index) => (
                    <span key={index} className={`
                      inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                      ${badge === 'Expert' ? 'bg-purple-100 text-purple-800' : 
                      badge === 'Pro' ? 'bg-blue-100 text-blue-800' : 
                      'bg-green-100 text-green-800'}
                    `}>
                    {badge}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className='flex flex-col items-end gap-1'>
            <div className='flex items-center gap-2 text-sm text-gray-700 font-medium'>
              <BookOpenText size={14} className="text-blue-600" />
              <span>{resources} resources</span>
          </div>
            <div className={`flex items-center gap-1 text-xs px-2 py-1 rounded-full ${trend >= 0 ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50'}`}>
            {trend >= 0 ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
            <span>{Math.abs(trend)}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
);
};

const ParticipationModule: React.FC = () => {
  const { isCollapsed } = useSidebar();
  const [timeFilter, setTimeFilter] = useState<string>('This Month');
  const [selectedUnit, setSelectedUnit] = useState<string>('All Units');
  const [selectedDepartmentUnit, setSelectedDepartmentUnit] = useState<string>('All Units');
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [isUnitFilterOpen, setIsUnitFilterOpen] = useState<boolean>(false);
  const [isDepartmentUnitFilterOpen, setIsDepartmentUnitFilterOpen] = useState<boolean>(false);
  const filterRef = useRef<HTMLDivElement>(null);
  const unitFilterRef = useRef<HTMLDivElement>(null);
  const departmentUnitFilterRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // New state for data from APIs
  const [isLoadingSummary, setIsLoadingSummary] = useState<boolean>(true);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState<boolean>(true);
  const [isLoadingContributors, setIsLoadingContributors] = useState<boolean>(true);

  // Summary state
  const [summaryData, setSummaryData] = useState<any>({
    participants: { total: 0, active: 0, trend: 0 },
    completionRate: { rate: 0, trend: 0 },
    knowledgeSharing: { total: 0, trend: 0 }
  });

  // Department participation state
  const [departmentData, setDepartmentData] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any>({
    labels: [],
    datasets: [{ label: 'Participation Rate', data: [], backgroundColor: 'rgba(59, 130, 246, 0.5)' }]
  });

  // Contributors state
  const [contributors, setContributors] = useState<any[]>([]);
  const [contributorsSummary, setContributorsSummary] = useState<any>({
    totalResources: 0,
    activeContributors: 0
  });

  // Add divisions state
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [isLoadingDivisions, setIsLoadingDivisions] = useState(true);

  // Fetch divisions data
  useEffect(() => {
    const fetchDivisions = async () => {
      try {
        setIsLoadingDivisions(true);
        const response = await fetch('/api/divisions');
        if (!response.ok) throw new Error('Failed to fetch divisions');
        
        const data = await response.json();
        setDivisions(data.divisions);
      } catch (error) {
        console.error('Error fetching divisions:', error);
        setDivisions([]);
      } finally {
        setIsLoadingDivisions(false);
      }
    };

    fetchDivisions();
  }, []);

  // Map timeFilter display to API timeframe parameter
  const getTimeframeParam = (): string => {
    switch (timeFilter) {
      case 'This Week': return 'week';
      case 'This Month': return 'month';
      case 'This Quarter': return 'quarter';
      case 'This Year': return 'year';
      default: return 'month';
    }
  };

  // Fetch summary data
  useEffect(() => {
    const fetchSummaryData = async () => {
      setIsLoadingSummary(true);
      try {
        const timeframe = getTimeframeParam();
        const response = await fetch(`/api/participation/summary?timeframe=${timeframe}`);
        
        if (response.ok) {
          const data = await response.json();
          setSummaryData(data);
        } else {
          console.error('Failed to fetch participation summary data');
          setSummaryData({
            participants: { total: 0, active: 0, trend: 0 },
            completionRate: { rate: 0, trend: 0 },
            knowledgeSharing: { total: 0, trend: 0 }
          });
        }
      } catch (error) {
        console.error('Error fetching participation summary:', error);
        setSummaryData({
          participants: { total: 0, active: 0, trend: 0 },
          completionRate: { rate: 0, trend: 0 },
          knowledgeSharing: { total: 0, trend: 0 }
        });
      } finally {
        setIsLoadingSummary(false);
      }
    };

    fetchSummaryData();
  }, [timeFilter]);

  // Fetch department participation data
  useEffect(() => {
    const fetchDepartmentData = async () => {
      setIsLoadingDepartments(true);
      try {
        const timeframe = getTimeframeParam();
        let url = `/api/participation/departments?timeframe=${timeframe}`;

        // Add unit filter if applicable
        if (selectedDepartmentUnit !== 'All Units') {
          // First check if it's a division name
          const division = divisions.find(d => d.divisionName === selectedDepartmentUnit);
          if (division) {
            url += `&divisionId=${division.id}`;
            console.log(`Filtering by division ID: ${division.id}`);
          } else {
            // If not a division, it must be a unit name - pass it directly
            // URL encode the unit name to handle special characters
            url += `&unitName=${encodeURIComponent(selectedDepartmentUnit)}`;
            console.log(`Filtering by unit name: ${selectedDepartmentUnit}`);
          }
        }

        console.log('Fetching department data with URL:', url);
        const response = await fetch(url);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Department data received:', data);
          
          if (data.departmentData && data.departmentData.length > 0) {
            setDepartmentData(data.departmentData);
          } else {
            console.log('No department data in response, using empty array');
            setDepartmentData([]);
          }
          
          if (data.chartData) {
            setChartData(data.chartData);
          } else {
            setChartData({
              labels: [],
              datasets: [{ label: 'Participation Rate', data: [] }]
            });
          }
        } else {
          console.error('Failed to fetch department participation data, status:', response.status);
          // Fallback to empty arrays for error state
          setDepartmentData([]);
          setChartData({
            labels: [],
            datasets: [{ label: 'Participation Rate', data: [] }]
          });
        }
      } catch (error) {
        console.error('Error fetching department participation:', error);
        // Use fallback data in case of error
        setDepartmentData([]);
        setChartData({
          labels: [],
          datasets: [{ label: 'Participation Rate', data: [] }]
        });
      } finally {
        setIsLoadingDepartments(false);
      }
    };

    fetchDepartmentData();
  }, [timeFilter, selectedDepartmentUnit]);

  // Fetch contributors data
  useEffect(() => {
    const fetchContributorsData = async () => {
      setIsLoadingContributors(true);
      try {
        const timeframe = getTimeframeParam();
        let url = `/api/participation/contributors?timeframe=${timeframe}&limit=5`;

        // Add unit filter if applicable
        if (selectedUnit !== 'All Units') {
          // First check if it's a division name
          const division = divisions.find(d => d.divisionName === selectedUnit);
          if (division) {
            url += `&divisionId=${division.id}`;
            console.log(`Filtering contributors by division ID: ${division.id}`);
          } else {
            // If not a division, it must be a unit name - pass it directly
            url += `&unitName=${encodeURIComponent(selectedUnit)}`;
            console.log(`Filtering contributors by unit name: ${selectedUnit}`);
          }
        }

        console.log('Fetching contributors data with URL:', url);
        const response = await fetch(url);
        
        if (response.ok) {
          const data = await response.json();
          console.log('Contributors data received:', data);
          
          if (data.contributors) {
            setContributors(data.contributors);
          } else {
            console.log('No contributors in response, using empty array');
            setContributors([]);
          }
          
          if (data.summary) {
            setContributorsSummary({
              totalResources: data.summary.totalResources || 0,
              activeContributors: data.summary.activeContributors || 0
            });
          } else {
            setContributorsSummary({
              totalResources: 0,
              activeContributors: 0
            });
          }
        } else {
          console.error('Failed to fetch contributors data, status:', response.status);
          setContributors([]);
          setContributorsSummary({
            totalResources: 0,
            activeContributors: 0
          });
        }
      } catch (error) {
        console.error('Error fetching contributors:', error);
        setContributors([]);
        setContributorsSummary({
          totalResources: 0,
          activeContributors: 0
        });
      } finally {
        setIsLoadingContributors(false);
      }
    };

    fetchContributorsData();
  }, [timeFilter, selectedUnit]);

  // Enhanced stats object using only real data
  const participationStats = {
    employees: { 
      total: summaryData.participants.total,
      subtitle: 'Active participants',
      trend: summaryData.participants.trend
    },
    average: { 
      completion: summaryData.completionRate.rate,
      subtitle: 'Avg. Completion Rate',
      trend: summaryData.completionRate.trend
    },
    share: { 
      contributor: summaryData.knowledgeSharing.total,
      subtitle: 'Total contributions',
      trend: summaryData.knowledgeSharing.trend
    }
  };

  const chartOptions: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        type: 'linear',
        beginAtZero: true,
        max: 100,
        grid: {
          display: false
        },
        ticks: {
          callback: function(tickValue: number | string, index: number, ticks: any) {
            return `${tickValue}%`;
          }
        }
      },
      x: {
        type: 'category',
        grid: {
          display: false
        }
      }
    }
  };

  const handleFilterChange = (filter: string): void => {
    setTimeFilter(filter);
    setIsFilterOpen(false);
  };

  const handleUnitChange = (unit: string): void => {
    setSelectedUnit(unit);
    setIsUnitFilterOpen(false);
  };

  const handleDepartmentUnitChange = (unit: string): void => {
    setSelectedDepartmentUnit(unit);
    setIsDepartmentUnitFilterOpen(false);
  };

  const handleClickOutside = (event: MouseEvent): void => {
    if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
      setIsFilterOpen(false);
    }
    if (unitFilterRef.current && !unitFilterRef.current.contains(event.target as Node)) {
      setIsUnitFilterOpen(false);
    }
    if (departmentUnitFilterRef.current && !departmentUnitFilterRef.current.contains(event.target as Node)) {
      setIsDepartmentUnitFilterOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const UnitDropdown: React.FC<{
    isOpen: boolean;
    selectedUnit: string;
    onUnitChange: (unit: string) => void;
    onClose: () => void;
    divisions: Division[];
    isLoading: boolean;
  }> = ({ isOpen, selectedUnit, onUnitChange, onClose, divisions, isLoading }) => (
    <div className="absolute right-0 mt-1 w-[320px] bg-white border border-gray-200 rounded-md shadow-lg z-10">
      <div className={`py-1 max-h-[400px] overflow-y-auto ${styles.customScrollbar}`}>
        <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
          <button
            onClick={() => {
              onUnitChange('All Units');
              onClose();
            }}
            className={`block w-full text-left py-1 px-2 text-sm rounded cursor-pointer ${
              selectedUnit === 'All Units'
                ? 'bg-color3 text-white'
                : 'hover:bg-gray-100 text-gray-700'
            }`}
          >
            All Units
          </button>
        </div>

        {isLoading ? (
          <div className="p-4 text-center text-gray-500">
            Loading divisions...
          </div>
        ) : divisions.length > 0 ? (
          divisions.map((division) => (
            <div key={division.id} className="border-b border-gray-200 last:border-b-0">
              <div className="px-3 py-2 bg-gray-100">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getDivisionColor(division.id)}`}></div>
                  <span className="text-sm font-semibold text-gray-700">
                    {division.divisionName}
                  </span>
                </div>
              </div>

              <div className="py-1 bg-white">
                {division.units.map((unit) => (
                  <button
                    key={unit.id}
                    onClick={() => {
                      onUnitChange(unit.unitName);
                      onClose();
                    }}
                    className={`block w-full text-left px-6 py-1.5 text-sm truncate group hover:bg-gray-50 cursor-pointer ${
                      selectedUnit === unit.unitName
                        ? 'bg-gray-50 text-color3 font-medium'
                        : 'text-gray-600'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <span className={`w-1 h-1 rounded-full ${
                        selectedUnit === unit.unitName ? 'bg-color3' : 'bg-gray-400 group-hover:bg-gray-500'
                      }`}></span>
                      {unit.unitName}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))
        ) : (
          <div className="p-4 text-center text-gray-500">
            No divisions available
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className='w-full max-h-screen '>
      <div className='max-w-full mx-auto space-y-6'>
        <div className='flex justify-between items-center'>
          <div>
            <h1 className='text-2xl font-semibold text-gray-800'>Participation Tracking</h1>
            <p className='text-sm text-gray-500'>Track employee participation in training programs</p>
          </div>
            <div className='relative' ref={filterRef}>
              <button 
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className='flex gap-2 items-center text-sm rounded-md bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-3 py-2 cursor-pointer'
              >
                <Calendar size={16} />
                <span>{timeFilter}</span>
                <ChevronDown size={14} />
              </button>
              {isFilterOpen && (
                <div className='absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10'>
                  <ul className='py-1'>
                    {['This Week', 'This Month', 'This Quarter', 'This Year'].map((filter) => (
                      <li key={filter}>
                        <button
                          onClick={() => handleFilterChange(filter)}
                        className={`block w-full text-left px-4 py-2 text-sm cursor-pointer ${
                          timeFilter === filter ? 'bg-gray-100 text-color3' : 'hover:bg-gray-50'
                        }`}
                        >
                          {filter}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          <StatCard
            title="Total Participants"
            value={isLoadingSummary ? "..." : participationStats.employees.total}
            icon={Users}
            trend={participationStats.employees.trend}
            subtitle={participationStats.employees.subtitle}
            color="text-color3"
          />
          <StatCard
            title="Avg. Completion Rate"
            value={isLoadingSummary ? "..." : `${participationStats.average.completion}%`}
            icon={CircleCheck}
            trend={participationStats.average.trend}
            subtitle={participationStats.average.subtitle}
            color="text-color3"
          />
          <StatCard
            title="Knowledge Sharing"
            value={isLoadingSummary ? "..." : participationStats.share.contributor}
            icon={BookOpenText}
            trend={participationStats.share.trend}
            subtitle={participationStats.share.subtitle}
            color="text-color3"
          />
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          <div className='bg-white rounded-xl shadow-lg shadow-gray-200/50 p-6 flex flex-col h-full border border-gray-100'>
            <div className='mb-6'>
              <div className='flex justify-between items-start mb-4'>
                <div>
                  <h2 className='text-lg font-semibold text-gray-800 mb-1'>Department Participation</h2>
                  <p className='text-sm text-gray-500'>Training participation by department</p>
                </div>
                <div className="relative" ref={departmentUnitFilterRef}>
                  <button
                    onClick={() => setIsDepartmentUnitFilterOpen(!isDepartmentUnitFilterOpen)}
                    className="flex items-center gap-2 px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-color3/50 focus:outline-none transition-all duration-200"
                  >
                    <Filter size={15} className="text-gray-600" />
                    <span className="text-gray-700 max-w-[180px] truncate font-medium">{selectedDepartmentUnit}</span>
                    <ChevronDown size={15} className="text-gray-600" />
                  </button>
                  {isDepartmentUnitFilterOpen && (
                    <UnitDropdown
                      isOpen={isDepartmentUnitFilterOpen}
                      selectedUnit={selectedDepartmentUnit}
                      onUnitChange={handleDepartmentUnitChange}
                      onClose={() => setIsDepartmentUnitFilterOpen(false)}
                      divisions={divisions}
                      isLoading={isLoadingDivisions}
                    />
                  )}
                </div>
              </div>
              <div className='bg-gray-50 rounded-lg p-5 mb-5 shadow-inner'>
                {isLoadingDepartments ? (
                  <div className="h-[200px] flex items-center justify-center">
                    <div className="flex flex-col items-center">
                      <Loader />
                      <p className="text-gray-500 text-sm">Loading chart data...</p>
                    </div>
                  </div>
                ) : chartData.datasets[0]?.data?.length > 0 ? (
                  <Bar 
                    data={{
                      ...chartData,
                      datasets: chartData.datasets.map((dataset: ChartDataset<'bar'>) => ({
                        ...dataset,
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        hoverBackgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 1,
                        borderRadius: 6,
                      }))
                    }} 
                    options={{
                      ...chartOptions,
                      plugins: {
                        ...chartOptions.plugins,
                        legend: {
                          display: false
                        },
                        tooltip: {
                          backgroundColor: 'rgba(17, 24, 39, 0.9)',
                          titleFont: {
                            size: 13,
                            weight: 'bold'
                          },
                          bodyFont: {
                            size: 12
                          },
                          padding: 12,
                          displayColors: false
                        }
                      },
                      animation: {
                        duration: 1000
                      }
                    }}
                    height={200} 
                  />
                ) : (
                  <div className="h-[200px] flex flex-col items-center justify-center">
                    <BarChart3 className="h-16 w-16 text-gray-300 mb-3" />
                    <p className="text-gray-500 font-medium">No chart data available</p>
                    <p className="text-gray-400 text-sm mt-1">Try selecting a different time period or department</p>
                  </div>
                )}
              </div>
            </div>
            {isLoadingDepartments ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <Loader />
                  <p className="text-gray-500 text-sm">Loading department data...</p>
                </div>
              </div>
            ) : departmentData.length > 0 ? (
              <div className={`space-y-3 flex-1 max-h-[400px] overflow-y-auto ${styles.customScrollbar}`}>
                {departmentData.map((dept, index) => (
                  <DepartmentProgress
                    key={index}
                    unit={dept.unitName}
                    divisionId={dept.divisionId}
                    total={dept.totalEmployees}
                    current={dept.activeEmployees}
                    lastMonth={dept.totalEmployees - dept.previousParticipationRate * dept.totalEmployees / 100}
                  />
                ))}
            </div>
            ) : (
              <div className="flex-1 flex flex-col items-center justify-center p-6 rounded-lg bg-gray-50">
                <Users className="h-16 w-16 text-gray-300 mb-3" />
                <p className="text-gray-600 font-medium text-center">No department data available</p>
                <p className="text-gray-400 text-sm text-center mt-1">Try selecting a different filter option</p>
              </div>
            )}
          </div>
   
          <div className='bg-white rounded-xl shadow-lg shadow-gray-200/50 p-6 flex flex-col h-full border border-gray-100'>
            <div className='mb-6'>
              <div className='flex justify-between items-start mb-4'>
                <div>
                  <h2 className='text-lg font-semibold text-gray-800 mb-1'>Top Knowledge Contributors</h2>
                  <p className='text-sm text-gray-500'>Most active employees in sharing knowledge</p>
                </div>
                <div className="relative" ref={unitFilterRef}>
                  <button
                    onClick={() => setIsUnitFilterOpen(!isUnitFilterOpen)}
                    className="flex items-center gap-2 px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-color3/20 focus:outline-none transition-all duration-200"
                  >
                    <Filter size={15} className="text-gray-600" />
                    <span className="text-gray-700 max-w-[180px] truncate font-medium">{selectedUnit}</span>
                    <ChevronDown size={15} className="text-gray-600" />
                  </button>
                  {isUnitFilterOpen && (
                    <UnitDropdown
                      isOpen={isUnitFilterOpen}
                      selectedUnit={selectedUnit}
                      onUnitChange={handleUnitChange}
                      onClose={() => setIsUnitFilterOpen(false)}
                      divisions={divisions}
                      isLoading={isLoadingDivisions}
                    />
                  )}
                </div>
              </div>
              <div className='grid grid-cols-2 gap-4 mb-6'>
                <div className='bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 text-center border border-blue-200 shadow-sm'>
                  <div className="flex items-center justify-center mb-2">
                    <BookOpenText size={20} className="text-blue-600" />
                  </div>
                  <p className='text-sm text-gray-600 mb-1'>Total Resources</p>
                  <p className='text-2xl font-bold text-blue-700'>{contributorsSummary.totalResources}</p>
                </div>
                <div className='bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 text-center border border-purple-200 shadow-sm'>
                  <div className="flex items-center justify-center mb-2">
                    <Users size={20} className="text-purple-600" />
                  </div>
                  <p className='text-sm text-gray-600 mb-1'>Active Contributors</p>
                  <p className='text-2xl font-bold text-purple-700'>{contributorsSummary.activeContributors}</p>
                </div>
              </div>
            </div>
            <div className={`space-y-4 flex-1 max-h-[500px] overflow-y-auto ${styles.customScrollbar}`}>
              {isLoadingContributors ? (
                <div className="flex items-center justify-center py-10">
                  <div className="flex flex-col items-center">
                    <Loader />
                    <p className="text-gray-500 font-medium">Loading contributors...</p>
                  </div>
                </div>
              ) : contributors.length > 0 ? (
                contributors.map((contributor, index) => (
                  <ContributorCard
                    key={index}
                    name={contributor.name}
                    unit={contributor.unit}
                    divisionId={contributor.divisionId}
                    resources={contributor.resources}
                    rank={contributor.rank}
                    trend={contributor.trend}
                    badges={[]}
                  />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-10 text-center bg-gray-50 rounded-xl">
                  <Award className="h-16 w-16 text-gray-300 mb-4" />
                  <p className="text-gray-700 font-medium">No contributors found</p>
                  <p className="text-gray-500 text-sm mt-1 max-w-xs">
                    There are no knowledge contributors matching your filter criteria. Try selecting a different unit or time period.
                  </p>
                </div>
              )}
            </div>
            {contributors.length > 0 && !isLoadingContributors && (
              <div className="mt-4 pt-4 border-t border-gray-100 text-center">
                <button className="text-blue-600 text-sm hover:text-blue-800 font-medium flex items-center gap-2 justify-center mx-auto">
                  View all contributors
                  <ChevronDown size={14} />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParticipationModule;