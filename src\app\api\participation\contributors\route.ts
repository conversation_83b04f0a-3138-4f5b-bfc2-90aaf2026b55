import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month'),
  divisionId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  unitId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 5) // Default to top 5 contributors
});

export async function GET(request: NextRequest) {
  try {
    
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const rawParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = querySchema.safeParse(rawParams);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { timeframe, divisionId, unitId, limit } = validationResult.data;
    console.log('Using parameters:', { timeframe, divisionId, unitId, limit });

    // Get unit name from query params if present
    const unitName = searchParams.get('unitName');
    if (unitName) {
      console.log('Unit name filter provided:', unitName);
    }

    // Calculate date ranges for current and previous periods
    const today = new Date();
    let startDate = new Date();
    let previousPeriodStart = new Date();
    let previousPeriodEnd = new Date(startDate);

    switch (timeframe) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        previousPeriodStart.setDate(today.getDate() - 14);
        previousPeriodEnd.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        previousPeriodStart.setMonth(today.getMonth() - 2);
        previousPeriodEnd.setMonth(today.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(today.getMonth() - 3);
        previousPeriodStart.setMonth(today.getMonth() - 6);
        previousPeriodEnd.setMonth(today.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        previousPeriodStart.setFullYear(today.getFullYear() - 2);
        previousPeriodEnd.setFullYear(today.getFullYear() - 1);
        break;
    }

    // Build user where clause based on division/unit filters
    let userWhere: any = {};
    
    if (unitId) {
      console.log('Filtering by unitId:', unitId);
      userWhere.unitId = unitId;
    } else if (unitName) {
      console.log('Filtering by unitName:', unitName);
      userWhere.unit = {
        unitName
      };
    } else if (divisionId) {
      console.log('Filtering by divisionId:', divisionId);
      userWhere.unit = {
        divisionId
      };
    }
    
    console.log('Final user where clause:', userWhere);
    
    // Get users who have created training materials (are knowledge contributors)
    const contributorsWithCounts = await prisma.user.findMany({
      where: {
        ...userWhere,
        involvedTrainingParticipants: {
          some: {
            isCreator: true
          }
        }
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        unitId: true,
        unit: {
          select: {
            unitName: true,
            divisionId: true,
            division: {
              select: {
                divisionName: true
              }
            }
          }
        },
        involvedTrainingParticipants: {
          where: {
            isCreator: true
          },
          select: {
            id: true,
            createdAt: true,
            trainingMaterialId: true
          }
        }
      }
    });
    
    // Count total resources and active contributors
    const totalResources = await prisma.trainingParticipant.count({
      where: {
        isCreator: true
      }
    });
    
    const activeContributors = await prisma.user.count({
      where: {
        involvedTrainingParticipants: {
          some: {
            isCreator: true
          }
        }
      }
    });

    // Process each contributor to calculate their statistics
    const contributorsWithStats = contributorsWithCounts.map(contributor => {
      // Count total materials created by this user
      const totalMaterials = contributor.involvedTrainingParticipants.length;
      
      // Count materials created in current period
      const currentPeriodMaterials = contributor.involvedTrainingParticipants.filter(
        p => p.createdAt >= startDate
      ).length;
      
      // Count materials created in previous period
      const previousPeriodMaterials = contributor.involvedTrainingParticipants.filter(
        p => p.createdAt >= previousPeriodStart && p.createdAt < previousPeriodEnd
      ).length;
      
      // Calculate trend percentage with safeguards for division by zero
      let trend = 0;
      if (previousPeriodMaterials > 0) {
        trend = Math.round(((currentPeriodMaterials - previousPeriodMaterials) / previousPeriodMaterials) * 100);
      } else if (currentPeriodMaterials > 0) {
        trend = 100; // If no previous but has current, show 100% increase
      }

      console.log(`Contributor ${contributor.firstName} ${contributor.lastName}: ${totalMaterials} resources, trend: ${trend}%`);
      
      return {
        id: contributor.id,
        name: `${contributor.firstName} ${contributor.lastName}`,
        unitId: contributor.unitId,
        unit: contributor.unit.unitName,
        divisionId: contributor.unit.divisionId,
        divisionName: contributor.unit.division.divisionName,
        resources: totalMaterials,
        trend
      };
    });
    
    // If no contributors, return empty array with proper structure
    if (contributorsWithStats.length === 0) {
      console.log('No contributors found with the provided filters');
    }

    // Sort by resources count (descending) and limit to requested amount
    const topContributors = contributorsWithStats
      .sort((a, b) => b.resources - a.resources)
      .slice(0, limit);
    
    // Add rank to each contributor
    const rankedContributors = topContributors.map((contributor, index) => ({
      ...contributor,
      rank: index + 1
    }));
    
    // Return the contributors data with summary stats
    return NextResponse.json({
      contributors: rankedContributors,
      summary: {
        totalResources,
        activeContributors
      }
    });
    
  } catch (error) {
    console.error('Error fetching knowledge contributors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch knowledge contributors' },
      { status: 500 }
    );
  }
} 