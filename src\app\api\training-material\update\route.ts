import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { cleanupFiles } from '@/lib/fileCleanup';
import { MaterialStatus as PrismaMaterialStatus } from '@prisma/client';
import { z } from 'zod';

const updateMaterialSchema = z.object({
  materialId: z.number({ required_error: 'Material ID is required' }),
  assessmentFormKey: z.string().optional(),
  certificateKey: z.string().nullable().optional(),
  status: z.nativeEnum(PrismaMaterialStatus, { required_error: 'Status is required' }),
}).refine((data) => {
  if (data.status === PrismaMaterialStatus.PENDING_APPROVAL && !data.assessmentFormKey) {
    return false;
  }
  return true;
}, {
  message: 'Assessment form key is required when submitting for approval.',
  path: ['assessmentFormKey'],
});

export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const currentUserId = parseInt(session.user.id, 10);
    if (isNaN(currentUserId)) {
      return NextResponse.json({ error: 'Invalid user ID in session' }, { status: 400 });
    }

    let rawData;
    try {
      rawData = await request.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const validationResult = updateMaterialSchema.safeParse(rawData);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    const { materialId, assessmentFormKey, certificateKey, status } = validationResult.data;

    const materialCreatorParticipant = await prisma.trainingParticipant.findFirst({
      where: {
        trainingMaterialId: materialId,
        userId: currentUserId,
        isCreator: true
      }
    });

    if (!materialCreatorParticipant) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    try {
      const updatedMaterial = await prisma.trainingMaterial.update({
        where: {
          id: materialId
        },
        data: {
          status,
          updatedAt: new Date()
        },
        select: { title: true }
      });

      await prisma.trainingParticipant.update({
        where: {
          userId_trainingMaterialId: {
            userId: currentUserId,
            trainingMaterialId: materialId,
          }
        },
        data: {
          certificateKey: certificateKey || null,
          assessmentFormKey: assessmentFormKey || null,
          updatedAt: new Date()
        }
      });

      if (status === PrismaMaterialStatus.PENDING_APPROVAL) {
        const materialTitle = updatedMaterial.title;

        const adminAndTrainingUnitRoles = ["HR_ADMIN", "ADMIN", "TRAINING_UNIT"]; 
        const usersToNotifyForReview = await prisma.user.findMany({
          where: { role: { is: { roleName: { in: adminAndTrainingUnitRoles } } } },
          select: { id: true },
        });

        if (usersToNotifyForReview.length > 0) {
          const reviewNotifications = usersToNotifyForReview.map(user => ({
            recipientUserId: user.id,
            message: `The training material \"${materialTitle}\" is pending for review.`,
            type: 'NEW_MATERIAL_SUBMITTED', 
            relatedEntityType: 'TRAINING_MATERIAL',
            relatedEntityId: materialId,
          }));
          await prisma.systemNotification.createMany({ data: reviewNotifications });
        }

        const participants = await prisma.trainingParticipant.findMany({
          where: {
            trainingMaterialId: materialId,
          },
          select: { 
            userId: true,
            assessmentFormKey: true,
            certificateKey: true
          }
        });

        if (participants.length > 0) {
          const participantMissingReqNotifications = [];
          for (const p of participants) {
            const missingItems = [];
            if (!p.assessmentFormKey) {
              missingItems.push("Assessment Form");
            }
            if (!p.certificateKey) {
              missingItems.push("Certificate");
            }

            if (missingItems.length > 0) {
              const missingItemsText = missingItems.join(' and ');
              participantMissingReqNotifications.push({
                recipientUserId: p.userId,
                message: `Please submit your missing requirements (${missingItemsText}) for the training material \"${materialTitle}\".`,
                type: 'MISSING_REQUIREMENTS',
                relatedEntityType: 'TRAINING_MATERIAL',
                relatedEntityId: materialId,
              });
            }
          }
          
          if (participantMissingReqNotifications.length > 0) {
            await prisma.systemNotification.createMany({ data: participantMissingReqNotifications });
          }
        }
      }

    } catch (error) {
      await cleanupFiles(materialId);
      throw error;
    }

    return NextResponse.json({
      success: true
    });

  } catch (error) {
    console.error('Error updating training material:', error);
    return NextResponse.json(
      { error: 'Failed to update training material' },
      { status: 500 }
    );
  }
}