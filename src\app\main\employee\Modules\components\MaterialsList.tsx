'use client';

import { FiChevronDown, FiChevronRight, FiCheckCircle, FiCircle, FiFile, FiLock } from 'react-icons/fi';
import { MaterialsListProps } from '../types/trainingTypes';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';

export const MaterialsList = ({ 
  modules, 
  selectedStep, 
  onStepSelect, 
  onModuleToggle,
  onMaterialClick,
  openModule 
}: MaterialsListProps) => {
  // Function to check if a module should be enabled
  const isModuleEnabled = (moduleIndex: number) => {
    if (moduleIndex === 0) return true; // First module is always enabled
    // Check if all previous modules are completed
    return modules.slice(0, moduleIndex).every(m => m.completed);
  };

  // Function to check if a material should be enabled
  const isMaterialEnabled = (moduleIndex: number, materialIndex: number) => {
    // First material in a module is always enabled if the module is enabled
    if (materialIndex === 0) return isModuleEnabled(moduleIndex); 
    
    // Other materials are enabled only if previous materials in the same module are viewed
    const module = modules[moduleIndex];
    return module.steps[0].materials.slice(0, materialIndex).every(m => m.viewed);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 md:p-6 border-b border-gray-200">
        <h2 className="text-base md:text-lg font-semibold text-gray-900">Course Content</h2>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 md:p-6 space-y-2">
          {modules.map((module, moduleIndex) => {
            const enabled = isModuleEnabled(moduleIndex);
            
            return (
              <div 
                key={module.id} 
                className={cn(
                  'rounded-lg transition-colors duration-200',
                  moduleIndex === openModule ? 'bg-blue-50' : '',
                  !enabled && 'opacity-50'
                )}
              >
                <button
                  onClick={() => enabled && onModuleToggle(module.id)}
                  disabled={!enabled}
                  className={cn(
                    'w-full flex items-center p-3 md:p-4 rounded-lg transition-colors duration-200',
                    moduleIndex === openModule ? 'text-blue-600' : 'text-gray-600 hover:bg-gray-50',
                    !enabled && 'cursor-not-allowed'
                  )}
                >
                  <span className={cn(
                    'flex items-center justify-center w-6 h-6 md:w-7 md:h-7 rounded-full text-sm md:text-base mr-3 transition-colors duration-200',
                    moduleIndex === openModule ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600',
                    !enabled && 'bg-gray-200'
                  )}>{module.moduleOrder}</span>
                  <span className={cn(
                    'text-sm md:text-base flex-1 text-left',
                    moduleIndex === openModule ? 'font-medium' : '',
                    !enabled && 'text-gray-400'
                  )}>{module.title}</span>
                  {module.completed ? (
                    <FiCheckCircle className="ml-2 w-5 h-5 md:w-6 md:h-6 text-green-500 flex-shrink-0" />
                  ) : (
                    <FiChevronDown 
                      className={cn(
                        'ml-2 w-5 h-5 md:w-6 md:h-6 transform transition-transform duration-200 flex-shrink-0',
                        moduleIndex === openModule ? 'rotate-180' : ''
                      )} 
                    />
                  )}
                </button>

                {moduleIndex === openModule && enabled && (
                  <div className="px-3 md:px-4 pb-3 md:pb-4">
                    {module.steps[0].materials.map((material, materialIndex) => {
                      const materialEnabled = isMaterialEnabled(moduleIndex, materialIndex);
                      
                      return (
                        <button 
                          key={materialIndex} 
                          onClick={() => {
                            if (materialEnabled) {
                              onMaterialClick(moduleIndex, materialIndex);
                              onStepSelect(module.steps[0].id);
                            }
                          }}
                          disabled={!materialEnabled}
                          className={cn(
                            'w-full flex items-center gap-3 py-2.5 px-3 md:px-4 mt-2 rounded-md transition-colors duration-200',
                            material.active ? 'bg-blue-100' : materialEnabled ? 'hover:bg-gray-50' : 'opacity-60 cursor-not-allowed'
                          )}
                        >
                          {materialEnabled ? (
                            <FiFile className={cn(
                              'w-5 h-5 md:w-6 md:h-6 flex-shrink-0',
                              material.active ? 'text-blue-600' : 'text-gray-400'
                            )} />
                          ) : (
                            <FiLock className="w-5 h-5 md:w-6 md:h-6 text-gray-400 flex-shrink-0" />
                          )}
                          <span className={cn(
                            'text-sm md:text-base text-left flex-1 truncate',
                            material.active ? 'text-gray-900 font-medium' : materialEnabled ? 'text-gray-600' : 'text-gray-400'
                          )}>{material.name}</span>
                          {material.viewed && (
                            <FiCheckCircle className="ml-2 w-5 h-5 md:w-6 md:h-6 text-green-500 flex-shrink-0" />
                          )}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}; 