'use client';

import { Fi<PERSON><PERSON>ch, FiBook, FiTarget } from 'react-icons/fi';
import type { IconType } from 'react-icons';

interface FeatureCardProps {
  icon: IconType;
  title: string;
  description: string;
}

export const features: FeatureCardProps[] = [
  {
    icon: FiSearch,
    title: "Find Courses",
    description: "Browse our catalog of courses across various categories and topics."
  },
  {
    icon: FiBook,
    title: "Study Materials",
    description: "Access high-quality PDF and PowerPoint materials created by experts."
  },
  {
    icon: FiTarget,
    title: "Track Progress",
    description: "Mark modules as complete and track your learning journey."
  }
];

const FeatureCard = ({ icon: Icon, title, description }: FeatureCardProps) => {
  return (
    <div className="flex flex-col items-center text-center">
      <div className="w-20 h-20 bg-[#0077CC] rounded-full flex items-center justify-center mb-6">
        <Icon className="w-10 h-10 text-white" />
      </div>
      <h3 className="text-2xl font-semibold mb-4">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
};

export default FeatureCard;
