import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { z } from 'zod';

function calculateSimilarity(str1: string, str2: string): number {
  if (str1.length === 0) return str2.length;
  if (str2.length === 0) return str1.length;

  const matrix = Array(str1.length + 1).fill(null).map(() => Array(str2.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[i][0] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str1.length; i++) {
    for (let j = 1; j <= str2.length; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,     
        matrix[i][j - 1] + 1,     
        matrix[i - 1][j - 1] + cost 
      );
    }
  }

  const maxLength = Math.max(str1.length, str2.length);
  const distance = matrix[str1.length][str2.length];
  const similarity = Math.round(((maxLength - distance) / maxLength) * 100);
  
  return similarity;
}

function sanitizeInput(input: string): string {
  return input.toLowerCase().replace(/[^\w\s]/g, '').trim();
}

const searchQuerySchema = z.object({
  query: z.string().min(2, 'Search query must be at least 2 characters long').optional().default('')
});

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const queryParams = {
      query: searchParams.get('query')
    };

    const validationResult = searchQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { query } = validationResult.data;

    if (query.length < 2) {
      return NextResponse.json({ competencies: [] });
    }

    const sanitizedQuery = sanitizeInput(query);

    const competencies = await prisma.competency.findMany({
      select: {
        id: true,
        competencyName: true,
      },
    });

    const results = competencies.map(comp => {
      const sanitizedName = sanitizeInput(comp.competencyName);
      const similarity = calculateSimilarity(sanitizedName, sanitizedQuery);
      
      return {
        id: comp.id,
        name: comp.competencyName,
        sanitizedName,
        similarity,
        isExisting: true
      };
    });

    const filteredResults = results
      .filter(comp => comp.similarity > 30) 
      .sort((a, b) => b.similarity - a.similarity);

    const exactMatch = filteredResults.some(comp => 
      comp.sanitizedName === sanitizedQuery || comp.similarity === 100
    );

    const responseData = {
      competencies: filteredResults.slice(0, 5), 
      exactMatch,
      sanitizedQuery,
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error searching competencies:', error);
    return NextResponse.json(
      { error: 'Failed to search competencies' },
      { status: 500 }
    );
  }
} 