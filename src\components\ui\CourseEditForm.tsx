import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/Button";
import MultiInput from "@/components/ui/multiInput";
import { FiArchive } from "react-icons/fi";
import { Plus, Trash2, ChevronDown } from "lucide-react";
import Modal from "./modal";
import FileUploader from "./FileUploader";
import FileList from "./FileList";

// Types
interface CourseData {
  title?: string;
  description?: string;
  category?: string;
  tag?: string;
  learningObjectives?: string[];
  modules?: Module[];
}

interface Module {
  id: number;
  title: string;
  files: FileData[];
  isOpen: boolean;
}

interface FileData {
  file: File;
  title: string;
  description: string;
}

interface CourseEditFormProps {
  isOpen: boolean;
  onClose: () => void;
  courseData?: CourseData;
  onSave: (data: CourseData) => Promise<void>;
  onArchive: () => void;
}

// Predefined options
const CATEGORIES = [
  "Training",
  "Symposia",
  "Continuing Professional Education (CPE)"
];

const PREDEFINED_TAGS = [
  "Programming",
  "Data Science",
  "Business",
  "Design",
  "Marketing",
  "Database",
  "AI/ML",
  "Web Development"
];

const EXAMPLE_OBJECTIVE = "Example: Understand the fundamentals of React and its core concepts";

const CourseEditForm: React.FC<CourseEditFormProps> = ({ 
  isOpen, 
  onClose, 
  courseData, 
  onSave, 
  onArchive 
}) => {
  const [activeTab, setActiveTab] = useState<'basic' | 'modules'>('basic');
  const [formData, setFormData] = useState<CourseData>({
    title: courseData?.title || "",
    description: courseData?.description || "",
    category: courseData?.category || "",
    tag: courseData?.tag || "",
    learningObjectives: courseData?.learningObjectives || [],
    modules: courseData?.modules || [{ id: 1, title: "", files: [], isOpen: true }]
  });
  const [selectedTags, setSelectedTags] = useState<string[]>(
    courseData?.tag ? courseData.tag.split(", ") : []
  );
  const [tagInput, setTagInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const tagInputRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagInputRef.current && !tagInputRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Module management functions
  const addModule = () => {
    const closedModules = formData.modules?.map(module => ({ ...module, isOpen: false })) || [];
    const newModules = [
      ...closedModules,
      {
        id: (formData.modules?.length || 0) + 1,
        title: "",
        files: [],
        isOpen: true
      }
    ];
    setFormData({ ...formData, modules: newModules });
  };

  const removeModule = (moduleIndex: number) => {
    if (!formData.modules) return;
    const newModules = formData.modules.filter((_, i) => i !== moduleIndex);
    setFormData({ ...formData, modules: newModules });
  };

  const updateModuleTitle = (moduleIndex: number, title: string) => {
    if (!formData.modules) return;
    const newModules = [...formData.modules];
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      title
    };
    setFormData({ ...formData, modules: newModules });
  };

  const toggleModule = (moduleIndex: number) => {
    if (!formData.modules) return;
    const newModules = [...formData.modules];
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      isOpen: !newModules[moduleIndex].isOpen
    };
    setFormData({ ...formData, modules: newModules });
  };

  const handleModuleFileUpload = (moduleIndex: number, files: File[]) => {
    if (!formData.modules || !files.length) return;
    const newModules = [...formData.modules];
    const newFiles = files.map(file => ({
      file,
      title: "",
      description: ""
    }));
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      files: [...newModules[moduleIndex].files, ...newFiles]
    };
    setFormData({ ...formData, modules: newModules });
  };

  const updateFileInfo = (moduleIndex: number, fileIndex: number, field: 'title' | 'description', value: string) => {
    if (!formData.modules) return;
    const newModules = [...formData.modules];
    newModules[moduleIndex].files[fileIndex] = {
      ...newModules[moduleIndex].files[fileIndex],
      [field]: value
    };
    setFormData({ ...formData, modules: newModules });
  };

  const removeFile = (moduleIndex: number, fileIndex: number) => {
    if (!formData.modules) return;
    const newModules = [...formData.modules];
    newModules[moduleIndex].files.splice(fileIndex, 1);
    setFormData({ ...formData, modules: newModules });
  };

  // Basic info functions
  const handleInputFocus = () => {
    setIsDropdownOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error("Error saving course:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBasicInfoChange = (field: keyof CourseData, value: string | string[] | Module[]) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleTagSelect = (tag: string) => {
    if (!selectedTags.includes(tag)) {
      const newTags = [...selectedTags, tag];
      setSelectedTags(newTags);
      handleBasicInfoChange('tag', newTags.join(', '));
    }
    setTagInput('');
  };

  const handleTagRemove = (tag: string) => {
    const updatedTags = selectedTags.filter(t => t !== tag);
    setSelectedTags(updatedTags);
    handleBasicInfoChange('tag', updatedTags.join(', '));
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(e.target.value);
  };

  const filteredTags = PREDEFINED_TAGS.filter(tag =>
    tag.toLowerCase().includes(tagInput.toLowerCase())
  );

  const highlightMatch = (tag: string) => {
    const regex = new RegExp(`(${tagInput})`, 'gi');
    return tag.replace(regex, (match) => `<span class='text-blue-500 font-semibold'>${match}</span>`);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Edit Course" size="large">
      <div className="mb-6">
        <div className="flex border-b border-gray-200 mb-6">
          <button
            type="button"
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'basic'
                ? 'text-color3 border-b-2 border-color3 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('basic')}
          >
            Basic Information
          </button>
          <button
            type="button"
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'modules'
                ? 'text-color3 border-b-2 border-color3 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('modules')}
          >
            Modules
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {activeTab === 'basic' ? (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Course Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleBasicInfoChange('title', e.target.value)}
                    placeholder="Enter course title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleBasicInfoChange('description', e.target.value)}
                    placeholder="Provide a detailed description of the course"
                    required
                    className="h-40"
                  />
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <select
                      id="category"
                      value={formData.category}
                      onChange={(e) => handleBasicInfoChange('category', e.target.value)}
                      className="w-full p-2.5 border rounded-md"
                      required
                    >
                      <option value="">Select category</option>
                      {CATEGORIES.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="space-y-3 relative" ref={tagInputRef}>
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2">
                    {selectedTags.map((tag) => (
                      <div key={tag} className="flex items-center bg-color3 text-white rounded-full px-3 py-1">
                        <span>{tag}</span>
                        <button
                          type="button"
                          onClick={() => handleTagRemove(tag)}
                          className="ml-2 text-white cursor-pointer hover:text-gray-300"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                    <Input
                      type="text"
                      value={tagInput}
                      onChange={handleTagInputChange}
                      onFocus={handleInputFocus}
                      placeholder="Type to view tags..."
                    />
                  </div>
                  {isDropdownOpen && (
                    <div className="absolute z-10 w-full max-h-[200px] overflow-y-auto bg-white border border-gray-300 rounded-md mt-1">
                      {filteredTags.map((tag) => (
                        <div
                          key={tag}
                          onClick={() => handleTagSelect(tag)}
                          className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                          dangerouslySetInnerHTML={{ __html: highlightMatch(tag) }}
                        />
                      ))}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Learning Objectives</Label>
                  <div className="ml-2 flex items-center gap-2">
                    <span className="text-sm text-gray-500">{EXAMPLE_OBJECTIVE}</span>
                  </div>
                  <MultiInput
                    values={formData.learningObjectives || []}
                    onChange={(values) => handleBasicInfoChange('learningObjectives', values)}
                    placeholder="Enter learning objective..."
                    required
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-4">
                  <Label>Training Modules</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addModule}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" /> Add Module
                  </Button>
                </div>

                <div className="space-y-2">
                  {formData.modules?.map((module, moduleIndex) => (
                    <div key={moduleIndex} className="border rounded-lg overflow-hidden">
                      <div className="flex items-center p-4 bg-gray-50">
                        <button
                          type="button"
                          onClick={() => toggleModule(moduleIndex)}
                          className="flex-1 flex items-center text-left"
                        >
                          <ChevronDown
                            className={`w-5 h-5 cursor-pointer mr-2 transform transition-transform duration-200 ${
                              module.isOpen ? "rotate-180" : ""
                            }`}
                          />
                          <div className="flex-1 flex items-center gap-3">
                            <span className="font-medium whitespace-nowrap">Module {module.id}</span>
                            <Input
                              value={module.title}
                              onChange={(e) => updateModuleTitle(moduleIndex, e.target.value)}
                              placeholder="Enter module title"
                              className="w-full"
                              onClick={(e) => e.stopPropagation()}
                            />
                          </div>
                        </button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeModule(moduleIndex);
                          }}
                          className="text-red-500 cursor-pointer ml-2"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>

                      <div
                        className={`transition-all duration-300 ${
                          module.isOpen ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
                        } overflow-hidden`}
                      >
                        <div className="p-4 space-y-4">
                          <div>
                            <FileUploader
                              onFilesSelected={(files) => handleModuleFileUpload(moduleIndex, Array.from(files))}
                              accept=".pdf,.docx,.pptx,.mp4,.mov,.avi"
                              multiple={true}
                            />
                          </div>

                          {module.files.length > 0 && (
                            <div className="space-y-4">
                              {module.files.map((fileData, fileIndex) => (
                                <div key={fileIndex} className="p-3 bg-gray-50 rounded-md space-y-3">
                                  <div className="flex justify-between items-start">
                                    <FileList
                                      files={[fileData.file]}
                                      onRemove={() => removeFile(moduleIndex, fileIndex)}
                                    />
                                  </div>
                                  
                                  <div>
                                    <Label>File Title</Label>
                                    <Input
                                      value={fileData.title}
                                      onChange={(e) => updateFileInfo(moduleIndex, fileIndex, "title", e.target.value)}
                                      placeholder="Enter file title"
                                      className="mt-1"
                                    />
                                  </div>

                                  <div>
                                    <Label>File Description</Label>
                                    <Textarea
                                      value={fileData.description}
                                      onChange={(e) => updateFileInfo(moduleIndex, fileIndex, "description", e.target.value)}
                                      placeholder="Enter file description"
                                      className="mt-1"
                                    />
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-between pt-6 border-t mt-6">
            <Button
              type="button"
              onClick={onArchive}
              className="flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600"
            >
              <FiArchive />
              Archive Course
            </Button>
            <div className="flex gap-3">
              <Button
                type="button"
                onClick={onClose}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-color3 text-white px-6 py-2 rounded-md hover:bg-color3/95"
              >
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CourseEditForm;


