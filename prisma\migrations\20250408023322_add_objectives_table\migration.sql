/*
  Warnings:

  - You are about to drop the column `materialDetails` on the `training_material` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "training_material" DROP COLUMN "materialDetails";

-- CreateTable
CREATE TABLE "objectives" (
    "id" SERIAL NOT NULL,
    "trainingMaterialId" INTEGER NOT NULL,
    "text" TEXT NOT NULL,
    "displayOrder" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "objectives_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "objectives_trainingMaterialId_idx" ON "objectives"("trainingMaterialId");

-- CreateIndex
CREATE UNIQUE INDEX "objectives_trainingMaterialId_displayOrder_key" ON "objectives"("trainingMaterialId", "displayOrder");

-- Add<PERSON><PERSON>ignKey
ALTER TABLE "objectives" ADD CONSTRAINT "objectives_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES "training_material"("id") ON DELETE CASCADE ON UPDATE CASCADE;
