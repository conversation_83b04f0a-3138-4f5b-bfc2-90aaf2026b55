import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

const CERTIFICATION_CATEGORIES = [
  'Training',
  'Symposia',
  'Continuing Professional Education (CPE)'
];

const queryParamsSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().positive()).default('1'),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().positive().max(100)).default('10'),
  search: z.string().optional().default(''),
  status: z.enum(['all', 'DRAFT', 'PENDING_APPROVAL', 'PUBLISHED', 'ARCHIVED']).default('all'),
  sortBy: z.enum(['newest', 'oldest']).default('newest')
});

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const queryParams = {
    page: searchParams.get('page'),
    limit: searchParams.get('limit'),
    search: searchParams.get('search'),
    status: searchParams.get('status'),
    sortBy: searchParams.get('sortBy')
  };

  const validationResult = queryParamsSchema.safeParse(queryParams);
  if (!validationResult.success) {
    return NextResponse.json({
      error: 'Invalid query parameters',
      details: validationResult.error.flatten().fieldErrors
    }, { status: 400 });
  }

  const { page, limit, search, status, sortBy } = validationResult.data;

  const where: any = {};
  
  where.category = {
    categoryName: {
      in: CERTIFICATION_CATEGORIES
    }
  };
  
  if (search) {
    const searchConditions = [
      { title: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
    
    where.OR = searchConditions;
  }
  
  if (status !== 'all') {
    where.status = status;
  }

  try {
    const totalItems = await prisma.trainingMaterial.count({ where });
    const totalPages = Math.ceil(totalItems / limit);
    const materials = await prisma.trainingMaterial.findMany({
      where,
      include: {
        category: true,
        trainingParticipants: {
          include: { 
            user: {
              include: {
                unit: true
              }
            }
          },
        },
      },
      orderBy: {
        createdAt: sortBy === 'newest' ? 'desc' : 'asc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    const formattedMaterials = materials.map(material => {
      const creator = material.trainingParticipants.find(tp => tp.isCreator);
      
      return {
        id: material.id,
        title: material.title,
        officialTags: [],
        temporaryTags: [],
        status: material.status,
        submittedDate: material.createdAt,
        category: material.category.categoryName,
        author: creator ? {
          id: creator.user.id,
          name: `${creator.user.firstName} ${creator.user.lastName}`,
          department: creator.user.unit.unitName
        } : null
      };
    });

    return NextResponse.json({
      materials: formattedMaterials,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching certifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch training materials' },
      { status: 500 }
    );
  }
}
