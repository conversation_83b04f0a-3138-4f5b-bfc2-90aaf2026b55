import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(
  request: NextRequest,
  context: any
) {
  
  let resolvedContext = context;
  if (context && typeof context.then === 'function') {
    resolvedContext = await context;
  }

  let params = resolvedContext.params;
  if (params && typeof params.then === 'function') {
    params = await params;
  }
  try {
    const session = await getServerSession(authOptions);
    
    const allowedRoles: string[] = ['EMPLOYEE', 'TRAINING_UNIT', 'HR_ADMIN'];

    if (!session?.user || !session.user.role || !allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const materialId = parseInt(params.id);
    if (isNaN(materialId)) {

      return NextResponse.json({ error: 'Invalid material ID format. ID must be a number.' }, { status: 400 });
    }


    const material = await prisma.trainingMaterial.findUnique({
      where: { id: materialId },
      include: {
        trainingParticipants: { 
          include: { 
            user: {       
              include: { 
                unit: true 
              }
            }
          }
        },
        materialCompetencies: { 
          include: {
            competency: true, 
            temporaryCompetency: true 
          }
        },
        
        modules: {
          orderBy: { 
            moduleOrder: 'asc'
          },
          select: { 
            id: true,
            title: true,
            description: true,
            moduleOrder: true, 
            ModuleResources: true
          }
        },
        category: true,
        materialStatusLogs: { 
          include: { changedByUser: true },
          orderBy: { createdAt: 'desc' }, 
        },
        objectives: true,
      },
    });

    if (!material) {
      return NextResponse.json({ error: 'Material not found' }, { status: 404 });
    }

    return NextResponse.json(material);
  } catch (error) {
    console.error('Error fetching training material:', error);
    return NextResponse.json(
      { error: 'Failed to fetch training material' },
      { status: 500 }
    );
  }
}
