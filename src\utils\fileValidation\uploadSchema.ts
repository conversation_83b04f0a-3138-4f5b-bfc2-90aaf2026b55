import { z } from 'zod';

export const uploadSchema = z.object({
  type: z.enum(['module', 'certificate', 'assessment'], {
    errorMap: () => ({ message: 'Invalid upload type' })
  }),
  materialId: z.string().min(1, 'Material ID is required').transform((val) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed)) {
      throw new Error('Invalid Material ID format');
    }
    return parsed;
  }),
  moduleId: z.string().optional().transform((val) => {
    if (!val) return undefined;
    const parsed = parseInt(val, 10);
    if (isNaN(parsed)) {
      throw new Error('Invalid Module ID format');
    }
    return parsed;
  }),
}).refine(
  (data) => {
    if (data.type === 'module' && !data.moduleId) {
      return false;
    }
    return true;
  },
  {
    message: 'Module ID is required for module uploads',
    path: ['moduleId']
  }
);

export type UploadSchema = z.infer<typeof uploadSchema>;

export const validateUploadRequest = (data: unknown): { success: boolean; data?: UploadSchema; error?: string } => {
  try {
    const validatedData = uploadSchema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors.map(e => e.message).join(', ')
      };
    }
    return { success: false, error: 'Invalid request data' };
  }
}; 