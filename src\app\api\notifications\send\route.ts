import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';

// Define the Zod schema for the notification payload
const notificationPayloadSchema = z.object({
  recipientUserId: z.number().int().positive({ message: "recipientUserId must be a positive integer" }),
  message: z.string().trim().min(1, { message: "message cannot be empty" }),
  title: z.string().optional(),
  type: z.string().optional(),
  relatedEntityType: z.string().optional(),
  relatedEntityId: z.union([z.string(), z.number()]).optional().transform(val => {
    if (val === undefined || val === null) return null;
    const num = parseInt(String(val), 10);
    return isNaN(num) ? null : num; // Or throw an error / handle invalid number string differently if needed
  }).refine(val => val === null || (typeof val === 'number' && !isNaN(val)), {
    message: "relatedEntityId must be a number or a string that can be parsed into a number",
  }),
});

// Infer the NotificationPayload type from the Zod schema
type NotificationPayload = z.infer<typeof notificationPayloadSchema>;

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 });
    }

    const allowedRoles = ['HR_ADMIN', 'HR_STAFF', 'ADMIN', 'SYSTEM'];
    if (!allowedRoles.includes(session.user.role as string)) {
      return NextResponse.json({ error: 'Forbidden: Insufficient permissions' }, { status: 403 });
    }

    // Validate request body using Zod
    let rawPayload;
    try {
      rawPayload = await request.json();
    } catch (error) {
      console.error('Invalid JSON in request body:', error);
      return NextResponse.json({ error: 'Invalid JSON format' }, { status: 400 });
    }

    const validationResult = await notificationPayloadSchema.safeParseAsync(rawPayload);

    if (!validationResult.success) {
      console.error('Invalid request payload:', validationResult.error.flatten().fieldErrors);
      return NextResponse.json({ 
        error: 'Invalid request payload', 
        details: validationResult.error.flatten().fieldErrors 
      }, { status: 400 });
    }

    const payload: NotificationPayload = validationResult.data;

    const {
      recipientUserId,
      message,
      // title, // title is now directly in payload
      type,
      relatedEntityType,
      relatedEntityId // This is now a number or null due to Zod transform
    } = payload;



    const recipientExists = await prisma.user.findUnique({
        where: { id: recipientUserId },
        select: { id: true }
    });
    if (!recipientExists) {
         return NextResponse.json({ error: 'Recipient user not found' }, { status: 404 });
    }

    const notification = await prisma.systemNotification.create({
      data: {
        recipientUserId: recipientUserId,
        message: message, // Zod schema now handles trimming
        isRead: false,
        type: type || 'MISSING_REQUIREMENTS', // Default type if not provided
        relatedEntityType: relatedEntityType,
        relatedEntityId: relatedEntityId, // Already a number or null
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Notification sent successfully',
      notification: {
        id: notification.id,
        message: notification.message,
        type: notification.type,
      }
    });

  } catch (error) {
    console.error('Error sending notification:', error);
    return NextResponse.json({ error: 'Failed to send notification due to an internal server error.' }, { status: 500 });
  }
} 