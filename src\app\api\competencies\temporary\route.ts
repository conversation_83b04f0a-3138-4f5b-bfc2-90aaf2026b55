import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { z } from 'zod';

function sanitizeInput(input: string): string {
  return input.toLowerCase().replace(/[^\w\s]/g, '').trim();
}

const createTemporaryCompetencySchema = z.object({
  competencyName: z.string()
    .min(2, 'Competency name must be at least 2 characters long')
    .max(100, 'Competency name must be less than 100 characters')
    .transform(val => sanitizeInput(val))
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    const body = await request.json();
    const validationResult = createTemporaryCompetencySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request body',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { competencyName: sanitizedName } = validationResult.data;

    const existingCompetency = await prisma.competency.findFirst({
      where: {
        competencyName: {
          equals: sanitizedName,
          mode: 'insensitive'
        }
      }
    });

    if (existingCompetency) {
      return NextResponse.json(
        { error: 'Competency already exists', competency: existingCompetency },
        { status: 409 }
      );
    }

    const existingTemporary = await prisma.temporaryCompetency.findFirst({
      where: {
        competencyName: {
          equals: sanitizedName,
          mode: 'insensitive'
        }
      }
    });

    if (existingTemporary) {
      return NextResponse.json(
        { 
          message: 'This competency is already pending approval',
          temporaryCompetency: existingTemporary
        },
        { status: 200 }
      );
    }

    const temporaryCompetency = await prisma.$queryRaw`
      INSERT INTO temporary_competencies (competency_name, status, created_by, created_at, updated_at)
      VALUES (${sanitizedName}, 'pending', ${userId ? parseInt(userId.toString()) : null}, NOW(), NOW())
      RETURNING *
    `;

    return NextResponse.json({
      message: 'Competency submitted for approval',
      temporaryCompetency
    });
  } catch (error) {
    console.error('Error creating temporary competency:', error);
    return NextResponse.json(
      { error: 'Failed to create temporary competency' },
      { status: 500 }
    );
  }
} 