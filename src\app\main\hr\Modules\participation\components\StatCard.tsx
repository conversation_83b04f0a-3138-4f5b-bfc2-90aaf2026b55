import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { StatCardProps } from '../types';

export const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  subtitle, 
  color = 'text-color3' 
}) => {
  const isPositiveTrend = trend !== undefined && trend > 0;

  return (
    <div className='bg-white rounded-lg shadow-md p-6 w-full min-h-[140px] relative overflow-hidden'>
      <div className='flex justify-between items-start'>
        <div className='space-y-2'>
          <h3 className='text-sm font-medium text-gray-700'>{title}</h3>
          <p className={`text-3xl font-bold ${color}`}>{value}</p>
          <p className='text-xs text-gray-500'>{subtitle}</p>
          {trend !== undefined && (
            <div className={`flex items-center gap-1 text-xs ${isPositiveTrend ? 'text-green-600' : 'text-red-600'}`}>
              {isPositiveTrend ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
              <span>{Math.abs(trend)}% from last month</span>
            </div>
          )}
        </div>
        <Icon size={20} className={color} />
      </div>
    </div>
  );
}; 