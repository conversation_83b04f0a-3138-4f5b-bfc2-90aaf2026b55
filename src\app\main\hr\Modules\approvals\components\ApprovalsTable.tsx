'use client';

import React from 'react';
import { Search } from 'lucide-react';
import { PendingApproval } from '../utils/types';
import PublicationRow from './PublicationRow';
import Pagination from './Pagination';

interface ApprovalsTableProps {
  currentItems: PendingApproval[];
  searchQuery: string;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  indexOfFirstItem: number;
  indexOfLastItem: number;
  handlePageChange: (page: number) => void;
  onReview: (item: PendingApproval) => void;
}

const ApprovalsTable: React.FC<ApprovalsTableProps> = ({
  currentItems,
  searchQuery,
  currentPage,
  totalPages,
  totalItems,
  indexOfFirstItem,
  indexOfLastItem,
  handlePageChange,
  onReview
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Training Material
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Creator / Department
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Submitted Date
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentItems.length > 0 ? (
              currentItems.map(item => (
                <PublicationRow 
                  key={item.id} 
                  item={item} 
                  searchQuery={searchQuery} 
                  onReview={onReview} 
                />
              ))
            ) : (
              <tr>
                <td colSpan={6} className="px-6 py-8 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <Search className="w-10 h-10 text-gray-300 mb-2" />
                    <p className="text-gray-500 text-sm">
                      {searchQuery.trim() 
                        ? `No publications found matching "${searchQuery}"`
                        : 'No publications found'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {totalItems > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          indexOfFirstItem={indexOfFirstItem}
          indexOfLastItem={Math.min(indexOfLastItem, totalItems)}
          handlePageChange={handlePageChange}
        />
      )}
    </div>
  );
};

export default ApprovalsTable;