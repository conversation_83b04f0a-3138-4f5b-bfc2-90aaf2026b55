"use client"

import { CheckCircle2, FileText, Users, BadgeIcon as Certificate, Upload } from "lucide-react"
import { Button } from "@/components/ui/Button"
import { useState } from "react"
import { toast, Toaster } from "sonner"
import { useRouter } from 'next/navigation'
import type { FormData, Module, Participant, FileData } from '../types'

interface SubmitStepProps {
  formData?: FormData;
  prevStep: () => void;
  onClose?: () => void;
}

interface CreatedModule {
  id: number; // Database ID
  title: string;
  moduleOrder: number;
  // Add other fields if necessary from the prisma schema / include
}

interface CreatedMaterial {
  id: number;
  // Add other fields if needed
  modules: CreatedModule[];
}

export default function SubmitStep({ formData = {} as FormData, prevStep, onClose }: SubmitStepProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    certificate?: number;
    assessment?: number;
    modules?: Record<string, number>;
  }>({});

  const {
    title = "",
    description = "",
    categoryId,
    modules = [],
    existingCompetencyIds = [],
    newCompetencyNames = [],
    participants = [],
    learningObjectives = [],
    certifications = [],
    complianceForms = []
  } = formData;

  const uploadFile = async (
    materialId: string,
    file: File,
    type: 'certificate' | 'assessmentForm' | 'module',
    dbModuleId?: string,
    moduleOrder?: string
  ) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('materialId', materialId); // Common for all types

    let apiUrl = '/api/training-material/submit-document'; // Default for certificate/assessment

    if (type === 'module') {
      apiUrl = '/api/training-material/upload'; // Correct consolidated endpoint
      formData.append('type', 'module'); // *** ADDED: Send type for module uploads ***

      if (!dbModuleId) {
        console.error("Module upload called without a database module ID.");
        throw new Error("Missing required module ID for module file upload.");
      }
      formData.append('moduleId', dbModuleId); // Backend expects 'moduleId'

      // moduleOrder is NO LONGER USED by the consolidated API endpoint
      // if (moduleOrder) {
      //      formData.append('moduleOrder', moduleOrder);
      // }
    } else {
      // For certificate/assessment, submit-document expects 'type'.
      formData.append('type', type);
    }

    // Cleanup logic removed as formData is specific to this call now

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Failed to upload ${type} file`);
      }

      const data = await response.json();
      return data.fileKey;
    } catch (error) {
      console.error(`Error uploading ${type}:`, error); // Keep this error log
      throw error;
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!complianceForms.length) {
        toast.error("Assessment form is required");
        return;
      }

      // Create training material first to get the ID
      const materialResponse = await fetch('/api/training-material/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          categoryId: categoryId || 1,
          modules: modules.map((module: Module) => ({
            title: module.title,
            files: module.files.map((file: FileData) => ({
              type: file.type,
              link: file.type === 'link' ? file.link : undefined,
              description: file.description,
              videoType: file.videoType
            }))
          })),
          existingCompetencyIds,
          newCompetencyNames,
          participantIds: participants.map((p: Participant) => p.id),
          learningObjectives
        }),
      });

      if (!materialResponse.ok) {
        const errorData = await materialResponse.json().catch(() => ({})); // Try to parse error
        console.error("Create API Error Data:", errorData); // Keep this error log
        throw new Error(`Failed to create training material: ${materialResponse.statusText}`);
      }

      const { material } = await materialResponse.json() as { material: CreatedMaterial }; // Get the full material object
      const materialId = material.id;
      const createdModules = material.modules.sort((a, b) => a.moduleOrder - b.moduleOrder); // Ensure modules are sorted by order

      // Upload module files using DB IDs
      for (let i = 0; i < modules.length; i++) {
        const moduleData = modules[i]; // Frontend module with temporary ID (Renamed from module)
        const dbModule = createdModules[i]; // Corresponding DB module with correct ID

        if (!dbModule) {
          console.error(`Mismatch between frontend modules (${modules.length}) and created modules (${createdModules.length}) at index ${i}`); // Keep this error log
          toast.error("An internal error occurred during module creation. Please try again.");
          return; // Stop processing if there's a mismatch
        }

        for (const file of moduleData.files) { // Use renamed variable
          if (file.type === 'file' && file.file) {
            try {
              setUploadProgress(prev => ({
                ...prev,
                modules: {
                  ...prev.modules,
                  [moduleData.id]: 0 // Keep using temporary ID for progress display if needed
                }
              }));

              // Pass both actual module ID (dbModule.id) for DB link
              // and module order (i + 1) for folder naming
              await uploadFile(
                materialId.toString(),
                file.file,
                'module',
                dbModule.id.toString(), // Actual DB ID -> sent as 'moduleId'
                (i + 1).toString()     // Order (1-based index) -> sent as 'moduleOrder'
              );

              setUploadProgress(prev => ({
                ...prev,
                modules: {
                  ...prev.modules,
                  [moduleData.id]: 100
                }
              }));
            } catch (error) { // Renamed back to error as it is used
              console.error(`Error uploading file ${file.file?.name}:`, error); // Keep this error log
              toast.error(`Failed to upload file ${file.file?.name}. Please try again.`);
              return;
            }
          }
        }
      }

      // Upload assessment form (required)
      let assessmentFormKey = null;
      try {
        setUploadProgress(prev => ({ ...prev, assessment: 0 }));
        assessmentFormKey = await uploadFile(materialId.toString(), complianceForms[0], 'assessmentForm');
        setUploadProgress(prev => ({ ...prev, assessment: 100 }));
      } catch (error) { // Renamed back to error as it is used
        console.error('Error uploading assessment:', error); // Keep this error log
        toast.error("Failed to upload assessment form. Please try again.");
        return;
      }

      // Upload certificate if present (optional)
      let certificateKey = null;
      if (certifications.length > 0) {
        try {
          setUploadProgress(prev => ({ ...prev, certificate: 0 }));
          certificateKey = await uploadFile(materialId.toString(), certifications[0], 'certificate');
          setUploadProgress(prev => ({ ...prev, certificate: 100 }));
        } catch (error) { // Renamed back to error as it is used
          console.error('Error uploading certificate:', error); // Keep this error log
          toast.error("Failed to upload certificate. Please try again.");
          return;
        }
      }

      // Update training material with file keys and set status to pending approval
      const updatePayload = {
        materialId: Number(materialId), // Ensure materialId is sent as a number
        assessmentFormKey,
        certificateKey,
        status: 'PENDING_APPROVAL'
      };

      const updateResponse = await fetch('/api/training-material/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatePayload),
      });

      if (!updateResponse.ok) {
        throw new Error('Failed to update training material');
      }

      toast.success("Course submitted for approval successfully!");
      
      // Clean up and redirect
      setIsSubmitting(false);
      setUploadProgress({});
      onClose?.();

      // Redirect to the home page with a slight delay to allow the toast to be seen
      setTimeout(() => {
        router.push('/main/employee/Dashboard');
      }, 1500);

    } catch (error) {
      console.error('Error submitting course:', error); // Keep this error log
      toast.error("Failed to submit course. Please try again.");
      setIsSubmitting(false);
      setUploadProgress({});
    }
  };

  return (
    <div className="space-y-6">
      <Toaster position="top-right" />
      <div>
        <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <span className="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-100 text-sm">5</span>
          Submit for Approval
        </h2>
        <p className="mt-1 text-sm text-gray-500">
          Review your submission and submit for approval. Tagged participants will be notified to review the materials.
        </p>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-6">Submission Summary</h3>

        <div className="space-y-6">
          <div className="flex items-start">
            <FileText className="w-5 h-5 text-color3 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium">Overview</h4>
              <p className="text-sm text-gray-500">Title: {title}</p>
              <p className="text-sm text-gray-500 line-clamp-2">Description: {description}</p>
            </div>
          </div>

          <div className="flex items-start">
            <Upload className="w-5 h-5 text-color3 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium">Uploaded Files</h4>
              <p className="text-sm text-gray-500">
                {modules.length} modules, {modules.reduce((count: number, module: Module) => count + module.files.length, 0)} resources
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <Users className="w-5 h-5 text-color3 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium">Tagged Participants</h4>
              <p className="text-sm text-gray-500">{participants.length} participants will be notified</p>
            </div>
          </div>

          <div className="flex items-start">
            <Certificate className="w-5 h-5 text-color3 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium">Certifications & Forms</h4>
              <div className="space-y-2">
                <p className="text-sm text-gray-500">
                  {certifications.length} certificate{certifications.length !== 1 ? 's' : ''}, {complianceForms.length} assessment form{complianceForms.length !== 1 ? 's' : ''}
                </p>
                {uploadProgress.certificate !== undefined && (
                  <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${uploadProgress.certificate}%` }}
                    />
                  </div>
                )}
                {uploadProgress.assessment !== undefined && (
                  <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${uploadProgress.assessment}%` }}
                    />
                  </div>
                )}
                {uploadProgress.modules && Object.keys(uploadProgress.modules).map(moduleId => (
                  <div key={moduleId} className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-500 transition-all duration-300"
                      style={{ width: `${(uploadProgress.modules?.[moduleId] ?? 0)}%` }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-green-50 p-6 rounded-lg">
        <div className="flex items-start">
          <CheckCircle2 className="w-5 h-5 text-green-500 mr-3 mt-0.5" />
          <div>
            <h4 className="font-medium text-green-800">What happens next?</h4>
            <ul className="text-sm text-green-700 list-disc list-inside space-y-2 mt-3">
              <li>System will notify all tagged participants</li>
              <li>Admin or approver will validate and approve the content</li>
              <li>You&apos;ll receive a notification once approved</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button 
          className="border cursor-pointer border-gray-300 px-6 py-2 rounded-md hover:bg-gray-50" 
          variant="outline" 
          onClick={prevStep}
          disabled={isSubmitting}
        >
          Back
        </Button>
        <Button 
          className="bg-green-600 cursor-pointer text-white px-6 py-2 rounded-md hover:bg-green-700 disabled:opacity-50" 
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Submit for Approval"}
        </Button>
      </div>
    </div>
  )
}
