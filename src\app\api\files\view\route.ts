import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs/promises';
import mime from 'mime-types';
import { stat } from 'fs/promises';
import { z } from 'zod';

const viewFileQuerySchema = z.object({
  key: z.string()
    .min(1, 'File key is required')
    .refine(val => !val.includes('..'), 'Invalid file key: path traversal not allowed')
});

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const queryParams = {
      key: searchParams.get('key')
    };

    const validationResult = viewFileQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { key: fileKey } = validationResult.data;

    const baseStorageDir = path.join(process.cwd(), 'storage');
    const filePath = path.join(baseStorageDir, fileKey);

    try {
      await stat(filePath);
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        console.error(`File not found at path: ${filePath}`);
        return NextResponse.json({ error: 'File not found' }, { status: 404 });
      }
      throw error;
    }

    const fileBuffer = await fs.readFile(filePath);
    const mimeType = mime.lookup(filePath) || 'application/octet-stream';

    const headers = new Headers();
    headers.set('Content-Type', mimeType);
    headers.set('Content-Disposition', 'inline');

    return new NextResponse(fileBuffer, { status: 200, headers });

  } catch (error) {
    console.error('Error serving file:', error);
    return NextResponse.json({ error: 'Internal server error while serving file' }, { status: 500 });
  }
}