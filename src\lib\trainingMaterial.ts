// src/lib/api/trainingMaterial.ts
export const trainingMaterialApi = {
    async list(params: {
      page?: number;
      limit?: number;
      search?: string;
      status?: string;
      sortBy?: 'newest' | 'oldest';
    }) {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value) queryParams.append(key, value.toString());
      });
      
      const response = await fetch(`/api/training-material/list?${queryParams}`);
      if (!response.ok) throw new Error('Failed to fetch training materials');
      return response.json();
    },
  
    async archive(id: number) {
      const response = await fetch(`/api/training-material/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'ARCHIVED' })
      });
      if (!response.ok) throw new Error('Failed to archive training material');
      return response.json();
    },

    async restore(id: number) {
      const response = await fetch(`/api/training-material/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'PUBLISHED' })
      });
      if (!response.ok) throw new Error('Failed to restore training material');
      return response.json();
    }
  };