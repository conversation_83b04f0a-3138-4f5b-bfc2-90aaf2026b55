import React from 'react';

interface HighlightedTextProps {
  text: string;
  searchQuery: string;
}

const HighlightedText: React.FC<HighlightedTextProps> = ({ text, searchQuery }) => {
  if (!searchQuery) return <span>{text}</span>;

  const parts = text.split(new RegExp(`(${searchQuery})`, 'gi'));

  return (
    <span>
      {parts.map((part, i) => {
        const isMatch = part.toLowerCase() === searchQuery.toLowerCase();
        return isMatch ? (
          <span key={i} className="font-bold text-color3">
            {part}
          </span>
        ) : (
          <span key={i}>{part}</span>
        );
      })}
    </span>
  );
};

export default HighlightedText; 