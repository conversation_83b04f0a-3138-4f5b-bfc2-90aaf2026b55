import React, { useState, useEffect, JS<PERSON>, forwardRef, useImperativeHandle } from 'react';
import { Bar } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { FolderOpen, Users, CheckCircle, BookOpen } from 'lucide-react'; // Added icons for stat cards

// Define the structure of the data expected from the API
interface TrainingStats {
  totalActiveLearners: number;
  overallCompletionRate: number | null;
  totalPublishedCourses: number;
}

interface PopularCourse {
  name: string;
  enrollments: number;
}

interface MonthlyProgressPoint {
  month: string;
  coursesCompleted: number;
}

interface TrainingAnalyticsData {
  stats: TrainingStats;
  popularCourses: PopularCourse[];
  monthlyProgress: MonthlyProgressPoint[];
}

interface TrainingAnalyticsProps {
  className?: string;
  timeframe?: string;
}

const EmptyState = ({ message }: { message: string }) => (
  <div className="flex flex-col items-center justify-center py-12 px-4 h-full">
    <div className="text-gray-400 text-center">
      <FolderOpen className="w-16 h-16 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-1">No Data Available</h3>
      <p className="text-gray-500">{message}</p>
    </div>
  </div>
);

const StatCard = ({ icon, label, value, color }: { icon: JSX.Element, label: string, value: string | number, color: string }) => (
  <div className={`flex items-center gap-3 bg-white rounded-lg shadow p-4 border border-gray-100 min-w-[180px]`}>
    <div className={`rounded-full p-2 ${color} bg-opacity-10`}>{icon}</div>
    <div>
      <div className="text-lg font-semibold text-gray-800">{value}</div>
      <div className="text-xs text-gray-500 mt-1">{label}</div>
    </div>
  </div>
);

const TrainingAnalytics = forwardRef<any, TrainingAnalyticsProps>(({ 
  className,
  timeframe = 'This Week'
}, ref) => {
  const [analyticsData, setAnalyticsData] = useState<TrainingAnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useImperativeHandle(ref, () => ({
    getExportData: () => {
      if (!analyticsData) return null;

      const headers = [
        'Metric',
        'Value',
        'Details'
      ];

      // Overview Statistics
      const data = [
        ['Overview Statistics'],
        ['Active Learners', analyticsData.stats.totalActiveLearners, 'Total number of active users'],
        ['Overall Completion Rate', `${analyticsData.stats.overallCompletionRate}%`, 'Percentage of completed trainings'],
        ['Total Published Courses', analyticsData.stats.totalPublishedCourses, 'Number of available courses'],
        [], // Empty row as separator
        ['Popular Courses by Enrollment'],
        ['Course Name', 'Enrollments', 'Percentage of Total'],
        ...analyticsData.popularCourses.map(course => [
          course.name,
          course.enrollments,
          `${((course.enrollments / analyticsData.stats.totalActiveLearners) * 100).toFixed(1)}%`
        ]),
        [], // Empty row as separator
        ['Monthly Training Progress'],
        ['Month', 'Completed Courses', 'Monthly Trend'],
        ...analyticsData.monthlyProgress.map((progress, index, array) => {
          let trend = 'Initial Month';
          if (index > 0) {
            const prevMonth = array[index - 1].coursesCompleted;
            const diff = progress.coursesCompleted - prevMonth;
            trend = diff > 0 ? `Increased (+${diff})` 
                  : diff < 0 ? `Decreased (${diff})` 
                  : 'No Change';
          }
          return [
            progress.month,
            progress.coursesCompleted,
            trend
          ];
        })
      ];

      return {
        title: 'Training Analytics Report',
        headers,
        data,
        timeframe
      };
    }
  }));

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/analytics/training');
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch training analytics');
        }

        setAnalyticsData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [timeframe]);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 w-full ${className} flex justify-center items-center h-[400px]`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 w-full ${className} flex flex-col justify-center items-center h-[400px]`}>
        <p className="text-red-500">Error loading training analytics:</p>
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  if (!analyticsData || (!analyticsData.popularCourses.length && !analyticsData.monthlyProgress.length)) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 w-full ${className}`}>
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <h2 className="text-lg font-semibold text-gray-800">Training Analytics</h2>
        </div>
        <EmptyState message="There is no training analytics data to display yet." />
      </div>
    );
  }

  // Stat Cards Data
  const statCards = [
    {
      icon: <Users className="w-6 h-6 text-blue-600" />,
      label: 'Active Learners',
      value: analyticsData.stats.totalActiveLearners,
      color: 'text-blue-600',
    },
    {
      icon: <CheckCircle className="w-6 h-6 text-green-600" />,
      label: 'Overall Completion Rate',
      value: analyticsData.stats.overallCompletionRate !== null ? `${analyticsData.stats.overallCompletionRate}%` : 'N/A',
      color: 'text-green-600',
    },
    {
      icon: <BookOpen className="w-6 h-6 text-purple-600" />,
      label: 'Published Courses',
      value: analyticsData.stats.totalPublishedCourses,
      color: 'text-purple-600',
    },
  ];

  // Popular courses chart data
  const popularCoursesChartData: ChartData<'bar'> = {
    labels: analyticsData.popularCourses.map(course => course.name),
    datasets: [
      {
        label: 'Enrollments',
        data: analyticsData.popularCourses.map(course => course.enrollments),
        backgroundColor: 'rgba(59, 130, 246, 0.6)', // Blue color for enrollments
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      }
    ]
  };

  const popularCoursesChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: (value) => Number.isInteger(value) ? value : null, // Only show integer ticks
          stepSize: 1, // Ensure ticks are at least 1 unit apart
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        align: 'start',
        labels: {
          boxWidth: 12,
          padding: 15
        }
      }
    }
  };
  
  // Monthly progress chart data
  const monthlyProgressChartData: ChartData<'bar'> = {
    labels: analyticsData.monthlyProgress.map(point => point.month),
    datasets: [{
      label: 'Courses Completed',
      data: analyticsData.monthlyProgress.map(point => point.coursesCompleted),
      backgroundColor: 'rgba(34, 197, 94, 0.6)', // Green color for completions
      borderColor: 'rgba(34, 197, 94, 1)',
      borderWidth: 1
    }]
  };

  const monthlyProgressChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: (value) => Number.isInteger(value) ? value : null, // Only show integer ticks
          stepSize: 1, // Ensure ticks are at least 1 unit apart
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    },
    plugins: {
      legend: {
        display: true, // Display legend for clarity
        position: 'top',
        align: 'start',
        labels: {
          boxWidth: 12,
          padding: 15
        }
      }
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 w-full ${className}`}>
      <div className="flex items-center justify-between mb-4 sm:mb-6">
        <div>
        <h2 className="text-lg font-semibold text-gray-800">Training Analytics</h2>
          <p className="text-sm text-gray-500">Overview of training performance and engagement</p>
          <p className="text-xs text-gray-400 mt-1">Showing data for: {timeframe}</p>
        </div>
      </div>

      {/* Stat Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {statCards.map((card, idx) => (
          <StatCard key={idx} {...card} />
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Popular Courses by Enrollment */}
        <div className="bg-gray-50 rounded-lg p-3 sm:p-5 h-full">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-700">Course Enrollment Overview</h3>
            <div className="text-xs text-gray-500">Top Courses by Enrollment</div>
          </div>
          <div className="h-[250px] sm:h-[280px]">
            {analyticsData.popularCourses.length > 0 ? (
                <Bar data={popularCoursesChartData} options={popularCoursesChartOptions} />
            ) : (
                <EmptyState message="No data available for popular courses."/>
            )}
          </div>
        </div>

        {/* Monthly Progress */}
        <div className="bg-gray-50 rounded-lg p-3 sm:p-5 lg:col-span-1">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-700">Monthly Training Completions</h3>
            <div className="text-xs text-gray-500">Last 12 months</div>
          </div>
          <div className="h-[250px] sm:h-[280px]">
             {analyticsData.monthlyProgress.length > 0 ? (
                <Bar data={monthlyProgressChartData} options={monthlyProgressChartOptions} />
            ) : (
                <EmptyState message="No data available for monthly progress."/>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

export default TrainingAnalytics; 