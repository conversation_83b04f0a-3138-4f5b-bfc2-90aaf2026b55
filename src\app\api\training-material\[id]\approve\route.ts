import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';

const approveRequestSchema = z.object({
  approvedCompetencyIds: z.array(z.number().int().positive()).optional()
});

interface ApproveRequestBody {
  approvedCompetencyIds?: number[];
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    const allowedRoles: string[] = ['HR_ADMIN']; 

    if (!session?.user || !session.user.role || !allowedRoles.includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden: Insufficient permissions' }, { status: 403 });
    }

    const materialId = parseInt(params.id);
    if (isNaN(materialId)) {
      return NextResponse.json({ error: 'Invalid material ID format.' }, { status: 400 });
    }

    let rawBody;
    try {
      rawBody = await request.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const validationResult = approveRequestSchema.safeParse(rawBody);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { approvedCompetencyIds = [] } = validationResult.data;

    const result = await prisma.$transaction(async (tx) => {
      const material = await tx.trainingMaterial.findUnique({
        where: { id: materialId },
        select: { status: true }
      });

      if (!material) {
        throw new Error('MaterialNotFound'); 
      }

      if (material.status !== 'PENDING_APPROVAL') {
        throw new Error(`Conflict: Material status is ${material.status}`);
      }

      const updatedMaterial = await tx.trainingMaterial.update({
        where: { id: materialId },
        data: {
          status: 'PUBLISHED',
        },
      });

      const conversionResults = [];
      if (approvedCompetencyIds.length > 0) {
        for (const matCompId of approvedCompetencyIds) {
          const materialCompetency = await tx.materialCompetency.findUnique({
            where: { id: matCompId },
            include: { temporaryCompetency: true }
          });

          if (materialCompetency && materialCompetency.temporaryCompetency) {
              
              const newPermanentCompetency = await tx.competency.create({
                data: {
                  competencyName: materialCompetency.temporaryCompetency.competencyName,
                  description: materialCompetency.temporaryCompetency.description,
                }
              });

              await tx.materialCompetency.update({
                where: { id: matCompId },
                data: {
                  competency_id: newPermanentCompetency.id, 
                  temporary_competency_id: null, 
                }
              });

              await tx.temporaryCompetency.delete({ 
                  where: { id: materialCompetency.temporaryCompetency.id } 
              });
              
              conversionResults.push({ id: matCompId, status: 'converted', newCompetencyId: newPermanentCompetency.id });
          } else {
            conversionResults.push({ id: matCompId, status: 'skipped', reason: 'Not found or not linked to a temporary competency' });
            console.warn(`Skipping conversion for MaterialCompetency ID ${matCompId}: Not found or not linked to a temp competency.`);
          }
        }
      }
      
      return { updatedMaterial, conversionResults };
    });

    return NextResponse.json({
      message: 'Training material approved successfully.',
      material: result.updatedMaterial,
      competencyConversions: result.conversionResults
    });

  } catch (error) {
    console.error('Error approving training material:', error);
    if (error instanceof Error) {
      if (error.message === 'MaterialNotFound') {
        return NextResponse.json({ error: 'Material not found' }, { status: 404 });
      }
      if (error.message.startsWith('Conflict:')) {
        return NextResponse.json({ error: error.message }, { status: 409 });
      }
    }
    return NextResponse.json({ error: 'Failed to approve training material due to an internal error.' }, { status: 500 });
  }
} 