import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import fs from 'fs';
import fsp from 'fs/promises';
import path from 'path';
import mime from 'mime-types';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized: Session invalid' }, { status: 401 });
    }
    const sessionUserId = parseInt(session.user.id, 10);
    if (isNaN(sessionUserId)) {
       return NextResponse.json({ error: 'Unauthorized: Invalid user ID format' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const key = searchParams.get('key');

    if (!key || typeof key !== 'string') {
      return NextResponse.json({ error: 'Missing or invalid \'key\' parameter' }, { status: 400 });
    }

    const participantRecord = await prisma.trainingParticipant.findFirst({
      where: {
        OR: [
          { assessmentFormKey: key },
          { certificateKey: key },
        ],
      },
      select: { userId: true, trainingMaterialId: true },
    });

    if (!participantRecord) {
      return NextResponse.json({ error: 'Forbidden: Key not found or associated with any participant' }, { status: 403 });
    }

    const isOwner = participantRecord.userId === sessionUserId;
    const isAdmin = session.user.role === 'HR_ADMIN';

    if (!isOwner && !isAdmin) {
      return NextResponse.json({ error: 'Forbidden: You do not have permission to access this file' }, { status: 403 });
    }

    const storageBasePath = process.env.LOCAL_STORAGE_PATH;

    if (!storageBasePath) {
      console.error('LOCAL_STORAGE_PATH environment variable is not set.');
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    const sanitizedKey = key.replace(/\\/g, '/');

    const requestedPath = path.join(storageBasePath, sanitizedKey);
    const resolvedPath = path.resolve(requestedPath);


    if (!resolvedPath.startsWith(path.resolve(storageBasePath))) {
      console.warn(`Path traversal attempt blocked: Key='${key}', Resolved='${resolvedPath}', Base='${storageBasePath}'`);
      return NextResponse.json({ error: 'Invalid file key' }, { status: 400 });
    }

    let stats;
    try {
      stats = await fsp.stat(resolvedPath);
    } catch (statError: unknown) {
      console.error(`[Download Route] Stat Error for path ${resolvedPath}:`, statError);
      if (typeof statError === 'object' && statError !== null && 'code' in statError) {
          if (statError.code === 'ENOENT') {
            return NextResponse.json({ error: 'File not found on server' }, { status: 404 });
          }
      }
      throw statError;
    }

    if (!stats.isFile()) {
        return NextResponse.json({ error: 'Requested path is not a file' }, { status: 400 });
    }

    const mimeType = mime.lookup(resolvedPath) || 'application/octet-stream';

    const filename = path.basename(resolvedPath);

    const fileStream = fs.createReadStream(resolvedPath);

    const headers = new Headers();
    headers.set('Content-Type', mimeType);
    headers.set('Content-Length', stats.size.toString());
    headers.set('Content-Disposition', `inline; filename="${filename}"`); 

    return new NextResponse(fileStream as unknown as ReadableStream<Uint8Array>, { 
      status: 200, 
      headers 
    });

  } catch (error) {
    console.error('Error in download route:', error);
    return NextResponse.json({ error: 'An internal server error occurred' }, { status: 500 });
  }
}