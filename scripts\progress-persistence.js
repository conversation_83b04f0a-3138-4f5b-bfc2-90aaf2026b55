const https = require('https');
const http = require('http');
require('dotenv').config();

// Read secret from environment or config
const CRON_SECRET = process.env.CRON_SECRET;
const APP_URL = process.env.APP_URL || 'http://localhost:3000';

const executeJob = async () => {
  try {


    const url = `${APP_URL}/api/cron/progress-persistence?secret=${CRON_SECRET}`;
    const client = url.startsWith('https') ? https : http;

    const req = client.request(url, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          JSON.parse(data); // Validate JSON but not using the result
        } catch (e) {
          console.error('Failed to parse response:', e);
        }
      });
    });

    req.on('error', (error) => {
      console.error('Error executing progress persistence job:', error);
    });

    req.end();
  } catch (error) {
    console.error('Failed to execute progress persistence job:', error);
  }
};

// Execute job
executeJob();

// If you want to run it as a scheduled task rather than in PM2's cron
if (process.env.RUN_INTERVAL) {
  const interval = parseInt(process.env.RUN_INTERVAL, 10) || 15 * 60 * 1000; // Default 15 min
  setInterval(executeJob, interval);
}