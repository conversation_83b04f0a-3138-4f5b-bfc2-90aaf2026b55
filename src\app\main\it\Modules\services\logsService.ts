import axios from 'axios';

export interface Log {
  id: string;
  timestamp: string;
  user: string;
  eventType: string;
  ipAddress: string;
  description: string;
  status: string;
}

export interface LogsFilter {
  startDate?: string;
  endDate?: string;
  eventType?: string;
  status?: string;
  searchQuery?: string;
  page?: number;
  itemsPerPage?: number;
}

// For development, we'll use the mock data but structured as an API service
const mockLogs = [
  {
    id: 'AUD-10458',
    timestamp: '2023-10-15 14:32:45',
    user: '<EMAIL>',
    eventType: 'Login',
    ipAddress: '************',
    description: 'User login successful',
    status: 'Success'
  },
  {
    id: 'AUD-10457',
    timestamp: '2023-10-15 14:30:12',
    user: 'unknown',
    eventType: 'Login',
    ipAddress: '************',
    description: 'Failed login attempt for user admin',
    status: 'Failed'
  },
  {
    id: 'AUD-10456',
    timestamp: '2023-10-15 14:15:33',
    user: '<EMAIL>',
    eventType: 'User',
    ipAddress: '************',
    description: 'User account created: <EMAIL>',
    status: 'Success'
  },
  {
    id: 'AUD-10455',
    timestamp: '2023-10-15 13:45:21',
    user: '<EMAIL>',
    eventType: 'File',
    ipAddress: '************',
    description: 'File uploaded: Marketing Campaign.pptx',
    status: 'Success'
  },
  {
    id: 'AUD-10454',
    timestamp: '2023-10-15 13:22:05',
    user: 'system',
    eventType: 'Security',
    ipAddress: '***********',
    description: 'Suspicious file upload detected and quarantined',
    status: 'Warning'
  },
  {
    id: 'AUD-10453',
    timestamp: '2023-10-15 12:58:42',
    user: '<EMAIL>',
    eventType: 'System',
    ipAddress: '************',
    description: 'System settings updated: Password policy',
    status: 'Success'
  },
  {
    id: 'AUD-10452',
    timestamp: '2023-10-15 12:45:18',
    user: '<EMAIL>',
    eventType: 'File',
    ipAddress: '************',
    description: 'File deleted: Old Project Proposal.docx',
    status: 'Success'
  }
];

class LogsService {
  private baseUrl = '/api/logs'; // Will be used when real API is ready

  async getLogs(filters: LogsFilter): Promise<{ logs: Log[]; total: number }> {
    // TODO: Replace with real API call
    // const response = await axios.get(this.baseUrl, { params: filters });
    // return response.data;

    // Mock implementation
    let filteredLogs = [...mockLogs];

    if (filters.startDate && filters.endDate) {
      filteredLogs = filteredLogs.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate >= new Date(filters.startDate!) && 
               logDate <= new Date(filters.endDate!);
      });
    }

    if (filters.eventType && filters.eventType !== 'All Events') {
      filteredLogs = filteredLogs.filter(log => 
        log.eventType === filters.eventType
      );
    }

    if (filters.status) {
      filteredLogs = filteredLogs.filter(log => 
        log.status === filters.status
      );
    }

    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filteredLogs = filteredLogs.filter(log =>
        Object.values(log).some(value =>
          value.toString().toLowerCase().includes(query)
        )
      );
    }

    // Handle pagination
    const start = ((filters.page || 1) - 1) * (filters.itemsPerPage || 10);
    const paginatedLogs = filteredLogs.slice(
      start,
      start + (filters.itemsPerPage || 10)
    );

    return {
      logs: paginatedLogs,
      total: filteredLogs.length
    };
  }

  async exportLogs(filters: LogsFilter): Promise<Blob> {
    const { logs } = await this.getLogs({ ...filters, page: 1, itemsPerPage: 1000 });
    
    // Convert logs to CSV
    const headers = ['Event ID', 'Timestamp', 'User', 'Event Type', 'IP Address', 'Description', 'Status'];
    const rows = logs.map(log => [
      log.id,
      log.timestamp,
      log.user,
      log.eventType,
      log.ipAddress,
      log.description,
      log.status
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  }
}

export const logsService = new LogsService(); 