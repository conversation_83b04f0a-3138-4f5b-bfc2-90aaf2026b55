'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import ITSidebar from '@/components/layout/ITsidebar';
import { SidebarProvider } from '@/components/layout/ITsidebar';
import ITnav from '@/components/layout/ITnav';
import ITDashboard from '../Modules/ITDashboard';
import { UserManagement } from '../Modules/UserManagement';
import AccessControl from '../Modules/AccessControl';
import Logs from '../Modules/Logs';
import Loader from '@/components/ui/Loader';


const ITPage = () => {
  const searchParams = useSearchParams();
  const [activeModule, setActiveModule] = useState<'dashboard' | 'usermanagement' | 'accesscontrol' | 'logs' | 'database' | 'storage'>('dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    const module = searchParams?.get('module') || null;
    switch (module) {
      case 'usermanagement':
        setActiveModule('usermanagement');
        break;
      case 'accesscontrol':
        setActiveModule('accesscontrol');
        break;
      case 'logs':
        setActiveModule('logs');
        break;
      default:
        setActiveModule('dashboard');
    }
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [searchParams]);

  return (
    <>
      {isLoading && <Loader />}
      <SidebarProvider>
        <div className="flex min-h-screen bg-gray-50">
          <ITSidebar onCollapse={(collapsed) => setIsSidebarCollapsed(collapsed)} />
          <div className="flex-1 min-w-0">
            <ITnav />
            <main 
              className={`relative min-w-0 p-4 sm:p-5 transition-all duration-300 mt-16 ${
                isSidebarCollapsed ? 'ml-[80px]' : 'ml-[270px]'
              }`}
            >
              <div className="w-full min-w-0 max-w-[1920px] mx-auto">
                {activeModule === 'dashboard' && <ITDashboard />}
                {activeModule === 'usermanagement' && <UserManagement />}
                {activeModule === 'accesscontrol' && <AccessControl />}
                {activeModule === 'logs' && <Logs />}
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    </>
  );
};

export default ITPage; 