--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO postgres;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: postgres
--

COMMENT ON SCHEMA public IS '';


--
-- Name: EmployeeType; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."EmployeeType" AS ENUM (
    'COS',
    'PERMANENT'
);


ALTER TYPE public."EmployeeType" OWNER TO postgres;

--
-- Name: MaterialStatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."MaterialStatus" AS ENUM (
    'DRAFT',
    'PENDING_APPROVAL',
    'PUBLISHED',
    'ARCHIVED'
);


ALTER TYPE public."MaterialStatus" OWNER TO postgres;

--
-- Name: PermissionName; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."PermissionName" AS ENUM (
    'CREATE_TRAINING_MATERIAL',
    'EDIT_OWN_TRAINING_MATERIAL',
    'EDIT_ANY_TRAINING_MATERIAL',
    'DELETE_OWN_TRAINING_MATERIAL',
    'DELETE_ANY_TRAINING_MATERIAL',
    'PUBLISH_TRAINING_MATERIAL',
    'ARCHIVE_TRAINING_MATERIAL',
    'APPROVE_TRAINING_MATERIAL',
    'VIEW_PUBLISHED_TRAINING_MATERIAL',
    'VIEW_ALL_TRAINING_MATERIAL',
    'MANAGE_MODULES',
    'UPLOAD_FILESS',
    'VIEW_OWN_PROGRESS',
    'VIEW_ALL_USER_PROGRESS',
    'MANAGE_USERS',
    'MANAGE_ROLES_PERMISSIONS',
    'TAG_PARTICIPANTS',
    'MANAGE_CATEGORIES',
    'MANAGE_COMPETENCIES'
);


ALTER TYPE public."PermissionName" OWNER TO postgres;

--
-- Name: ProgressStatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."ProgressStatus" AS ENUM (
    'NOT_STARTED',
    'IN_PROGRESS',
    'COMPLETED'
);


ALTER TYPE public."ProgressStatus" OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO postgres;

--
-- Name: auditLogs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."auditLogs" (
    id integer NOT NULL,
    "userId" integer,
    action character varying(255) NOT NULL,
    "targetEntity" text,
    "targetId" integer,
    details jsonb,
    "ipAddress" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."auditLogs" OWNER TO postgres;

--
-- Name: auditLogs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."auditLogs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."auditLogs_id_seq" OWNER TO postgres;

--
-- Name: auditLogs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."auditLogs_id_seq" OWNED BY public."auditLogs".id;


--
-- Name: categories; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    "categoryName" character varying(255) NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.categories OWNER TO postgres;

--
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.categories_id_seq OWNER TO postgres;

--
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- Name: competencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.competencies (
    id integer NOT NULL,
    "competencyName" character varying(255) NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.competencies OWNER TO postgres;

--
-- Name: competencies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.competencies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.competencies_id_seq OWNER TO postgres;

--
-- Name: competencies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.competencies_id_seq OWNED BY public.competencies.id;


--
-- Name: divisions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.divisions (
    id integer NOT NULL,
    "divisionName" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.divisions OWNER TO postgres;

--
-- Name: divisions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.divisions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.divisions_id_seq OWNER TO postgres;

--
-- Name: divisions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.divisions_id_seq OWNED BY public.divisions.id;


--
-- Name: materialApprovals; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."materialApprovals" (
    id integer NOT NULL,
    "trainingMaterialId" integer NOT NULL,
    "approverUserId" integer NOT NULL,
    status public."MaterialStatus" DEFAULT 'PENDING_APPROVAL'::public."MaterialStatus" NOT NULL,
    comments text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."materialApprovals" OWNER TO postgres;

--
-- Name: materialApprovals_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."materialApprovals_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."materialApprovals_id_seq" OWNER TO postgres;

--
-- Name: materialApprovals_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."materialApprovals_id_seq" OWNED BY public."materialApprovals".id;


--
-- Name: materialStatusLogs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."materialStatusLogs" (
    id integer NOT NULL,
    "trainingMaterialId" integer NOT NULL,
    "changedByUserId" integer,
    "oldStatus" text,
    "newStatus" text NOT NULL,
    reason text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."materialStatusLogs" OWNER TO postgres;

--
-- Name: materialStatusLogs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."materialStatusLogs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."materialStatusLogs_id_seq" OWNER TO postgres;

--
-- Name: materialStatusLogs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."materialStatusLogs_id_seq" OWNED BY public."materialStatusLogs".id;


--
-- Name: material_competencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.material_competencies (
    training_material_id integer NOT NULL,
    competency_id integer NOT NULL,
    assigned_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.material_competencies OWNER TO postgres;

--
-- Name: moduleFiles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."moduleFiles" (
    id integer NOT NULL,
    "moduleId" integer NOT NULL,
    "fileName" character varying(255) NOT NULL,
    "fileKey" character varying(512) NOT NULL,
    "fileType" character varying(50) NOT NULL,
    file_size bigint,
    duration integer,
    "totalPages" integer,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."moduleFiles" OWNER TO postgres;

--
-- Name: moduleFiles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."moduleFiles_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."moduleFiles_id_seq" OWNER TO postgres;

--
-- Name: moduleFiles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."moduleFiles_id_seq" OWNED BY public."moduleFiles".id;


--
-- Name: modules; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.modules (
    id integer NOT NULL,
    "trainingMaterialId" integer NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    "moduleOrder" integer NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.modules OWNER TO postgres;

--
-- Name: modules_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.modules_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.modules_id_seq OWNER TO postgres;

--
-- Name: modules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.modules_id_seq OWNED BY public.modules.id;


--
-- Name: permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.permissions (
    id integer NOT NULL,
    name public."PermissionName" NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.permissions OWNER TO postgres;

--
-- Name: permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.permissions_id_seq OWNER TO postgres;

--
-- Name: permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.permissions_id_seq OWNED BY public.permissions.id;


--
-- Name: rolePermissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."rolePermissions" (
    id integer NOT NULL,
    "roleId" integer NOT NULL,
    "permissionId" integer NOT NULL
);


ALTER TABLE public."rolePermissions" OWNER TO postgres;

--
-- Name: rolePermissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."rolePermissions_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."rolePermissions_id_seq" OWNER TO postgres;

--
-- Name: rolePermissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."rolePermissions_id_seq" OWNED BY public."rolePermissions".id;


--
-- Name: roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.roles (
    id integer NOT NULL,
    "roleName" text NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.roles OWNER TO postgres;

--
-- Name: roles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.roles_id_seq OWNER TO postgres;

--
-- Name: roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.roles_id_seq OWNED BY public.roles.id;


--
-- Name: systemNotifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."systemNotifications" (
    id integer NOT NULL,
    "recipientUserId" integer,
    message text NOT NULL,
    type character varying(100) NOT NULL,
    "isRead" boolean DEFAULT false NOT NULL,
    "relatedEntityType" text,
    "relatedEntityId" integer,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."systemNotifications" OWNER TO postgres;

--
-- Name: systemNotifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."systemNotifications_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."systemNotifications_id_seq" OWNER TO postgres;

--
-- Name: systemNotifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."systemNotifications_id_seq" OWNED BY public."systemNotifications".id;


--
-- Name: training_material; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.training_material (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    "categoryId" integer NOT NULL,
    status public."MaterialStatus" DEFAULT 'DRAFT'::public."MaterialStatus" NOT NULL,
    "publishedAt" timestamp(3) without time zone,
    "materialDetails" jsonb NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.training_material OWNER TO postgres;

--
-- Name: training_material_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.training_material_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.training_material_id_seq OWNER TO postgres;

--
-- Name: training_material_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.training_material_id_seq OWNED BY public.training_material.id;


--
-- Name: training_participants; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.training_participants (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "trainingMaterialId" integer NOT NULL,
    "isCreator" boolean DEFAULT false NOT NULL,
    "certificateKey" character varying(512),
    "assessmentFormKey" character varying(512),
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.training_participants OWNER TO postgres;

--
-- Name: training_participants_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.training_participants_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.training_participants_id_seq OWNER TO postgres;

--
-- Name: training_participants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.training_participants_id_seq OWNED BY public.training_participants.id;


--
-- Name: units; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.units (
    id integer NOT NULL,
    "unitName" text NOT NULL,
    "divisionId" integer NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.units OWNER TO postgres;

--
-- Name: units_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.units_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.units_id_seq OWNER TO postgres;

--
-- Name: units_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.units_id_seq OWNED BY public.units.id;


--
-- Name: userFileProgress; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."userFileProgress" (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "moduleFileId" integer NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    "lastPage" integer,
    "lastTimestamp" integer,
    status public."ProgressStatus" DEFAULT 'NOT_STARTED'::public."ProgressStatus" NOT NULL,
    "completedAt" timestamp(3) without time zone,
    "lastAccessedAt" timestamp(3) without time zone,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."userFileProgress" OWNER TO postgres;

--
-- Name: userFileProgress_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."userFileProgress_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."userFileProgress_id_seq" OWNER TO postgres;

--
-- Name: userFileProgress_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."userFileProgress_id_seq" OWNED BY public."userFileProgress".id;


--
-- Name: userModuleProgress; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."userModuleProgress" (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "moduleId" integer NOT NULL,
    status public."ProgressStatus" DEFAULT 'NOT_STARTED'::public."ProgressStatus" NOT NULL,
    "startedAt" timestamp(3) without time zone,
    "completedAt" timestamp(3) without time zone,
    "lastAccessedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."userModuleProgress" OWNER TO postgres;

--
-- Name: userModuleProgress_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."userModuleProgress_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."userModuleProgress_id_seq" OWNER TO postgres;

--
-- Name: userModuleProgress_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."userModuleProgress_id_seq" OWNED BY public."userModuleProgress".id;


--
-- Name: userPermissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."userPermissions" (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "permissionId" integer NOT NULL
);


ALTER TABLE public."userPermissions" OWNER TO postgres;

--
-- Name: userPermissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."userPermissions_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."userPermissions_id_seq" OWNER TO postgres;

--
-- Name: userPermissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."userPermissions_id_seq" OWNED BY public."userPermissions".id;


--
-- Name: userSearches; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."userSearches" (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "searchQuery" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."userSearches" OWNER TO postgres;

--
-- Name: userSearches_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."userSearches_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."userSearches_id_seq" OWNER TO postgres;

--
-- Name: userSearches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."userSearches_id_seq" OWNED BY public."userSearches".id;


--
-- Name: userSessions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."userSessions" (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    token text NOT NULL,
    "ipAddress" text NOT NULL,
    "userAgent" text NOT NULL,
    "expiresAt" timestamp(3) without time zone NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."userSessions" OWNER TO postgres;

--
-- Name: userSessions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."userSessions_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."userSessions_id_seq" OWNER TO postgres;

--
-- Name: userSessions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."userSessions_id_seq" OWNED BY public."userSessions".id;


--
-- Name: userTrainingProgress; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."userTrainingProgress" (
    id integer NOT NULL,
    "userId" integer NOT NULL,
    "trainingMaterialId" integer NOT NULL,
    status public."ProgressStatus" DEFAULT 'NOT_STARTED'::public."ProgressStatus" NOT NULL,
    "progressPercentage" integer DEFAULT 0 NOT NULL,
    "startedAt" timestamp(3) without time zone,
    "completedAt" timestamp(3) without time zone,
    "lastAccessedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."userTrainingProgress" OWNER TO postgres;

--
-- Name: userTrainingProgress_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."userTrainingProgress_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public."userTrainingProgress_id_seq" OWNER TO postgres;

--
-- Name: userTrainingProgress_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."userTrainingProgress_id_seq" OWNED BY public."userTrainingProgress".id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    "unitId" integer NOT NULL,
    "roleId" integer NOT NULL,
    "firstName" character varying(100) NOT NULL,
    "lastName" character varying(100) NOT NULL,
    password text NOT NULL,
    email text NOT NULL,
    "employeeType" public."EmployeeType" NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "lastLoginAt" timestamp(3) without time zone,
    "failedLoginAttempts" integer DEFAULT 0 NOT NULL,
    "accountLockedUntil" timestamp(3) without time zone,
    "passwordResetExpires" timestamp(3) without time zone,
    "passwordResetToken" text
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: auditLogs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."auditLogs" ALTER COLUMN id SET DEFAULT nextval('public."auditLogs_id_seq"'::regclass);


--
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- Name: competencies id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.competencies ALTER COLUMN id SET DEFAULT nextval('public.competencies_id_seq'::regclass);


--
-- Name: divisions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.divisions ALTER COLUMN id SET DEFAULT nextval('public.divisions_id_seq'::regclass);


--
-- Name: materialApprovals id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialApprovals" ALTER COLUMN id SET DEFAULT nextval('public."materialApprovals_id_seq"'::regclass);


--
-- Name: materialStatusLogs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialStatusLogs" ALTER COLUMN id SET DEFAULT nextval('public."materialStatusLogs_id_seq"'::regclass);


--
-- Name: moduleFiles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."moduleFiles" ALTER COLUMN id SET DEFAULT nextval('public."moduleFiles_id_seq"'::regclass);


--
-- Name: modules id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.modules ALTER COLUMN id SET DEFAULT nextval('public.modules_id_seq'::regclass);


--
-- Name: permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions ALTER COLUMN id SET DEFAULT nextval('public.permissions_id_seq'::regclass);


--
-- Name: rolePermissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."rolePermissions" ALTER COLUMN id SET DEFAULT nextval('public."rolePermissions_id_seq"'::regclass);


--
-- Name: roles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles ALTER COLUMN id SET DEFAULT nextval('public.roles_id_seq'::regclass);


--
-- Name: systemNotifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."systemNotifications" ALTER COLUMN id SET DEFAULT nextval('public."systemNotifications_id_seq"'::regclass);


--
-- Name: training_material id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_material ALTER COLUMN id SET DEFAULT nextval('public.training_material_id_seq'::regclass);


--
-- Name: training_participants id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_participants ALTER COLUMN id SET DEFAULT nextval('public.training_participants_id_seq'::regclass);


--
-- Name: units id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.units ALTER COLUMN id SET DEFAULT nextval('public.units_id_seq'::regclass);


--
-- Name: userFileProgress id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userFileProgress" ALTER COLUMN id SET DEFAULT nextval('public."userFileProgress_id_seq"'::regclass);


--
-- Name: userModuleProgress id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userModuleProgress" ALTER COLUMN id SET DEFAULT nextval('public."userModuleProgress_id_seq"'::regclass);


--
-- Name: userPermissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userPermissions" ALTER COLUMN id SET DEFAULT nextval('public."userPermissions_id_seq"'::regclass);


--
-- Name: userSearches id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userSearches" ALTER COLUMN id SET DEFAULT nextval('public."userSearches_id_seq"'::regclass);


--
-- Name: userSessions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userSessions" ALTER COLUMN id SET DEFAULT nextval('public."userSessions_id_seq"'::regclass);


--
-- Name: userTrainingProgress id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userTrainingProgress" ALTER COLUMN id SET DEFAULT nextval('public."userTrainingProgress_id_seq"'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
43abb48d-90c1-4a1b-9db8-b7b91c20689a	f283e542319b46ee9880048bd2cf17bd9c27de3b3f8ef0517835c9a51e41fd37	2025-04-07 11:03:23.023687+08	20250402072436_init	\N	\N	2025-04-07 11:03:22.826136+08	1
69234c77-14cc-4e58-a852-0d2e768bf9fd	a2a16480409b531946b5f36fa7d6fa7bf38c688ce0935ec0bb100b3cf4514198	2025-04-07 15:24:18.845553+08	20250407072418_add_password_reset_fields	\N	\N	2025-04-07 15:24:18.826415+08	1
\.


--
-- Data for Name: auditLogs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."auditLogs" (id, "userId", action, "targetEntity", "targetId", details, "ipAddress", "createdAt") FROM stdin;
\.


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.categories (id, "categoryName", description, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: competencies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.competencies (id, "competencyName", description, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: divisions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.divisions (id, "divisionName", "createdAt", "updatedAt") FROM stdin;
1	Financial and Administrative Services	2025-04-07 03:09:10.025	2025-04-07 03:09:10.025
2	Office of the Regional Director	2025-04-07 03:09:10.025	2025-04-07 03:09:10.025
3	Technical Operations	2025-04-07 03:09:10.025	2025-04-07 03:09:10.025
\.


--
-- Data for Name: materialApprovals; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."materialApprovals" (id, "trainingMaterialId", "approverUserId", status, comments, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: materialStatusLogs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."materialStatusLogs" (id, "trainingMaterialId", "changedByUserId", "oldStatus", "newStatus", reason, "createdAt") FROM stdin;
\.


--
-- Data for Name: material_competencies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.material_competencies (training_material_id, competency_id, assigned_at) FROM stdin;
\.


--
-- Data for Name: moduleFiles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."moduleFiles" (id, "moduleId", "fileName", "fileKey", "fileType", file_size, duration, "totalPages", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: modules; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.modules (id, "trainingMaterialId", title, description, "moduleOrder", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.permissions (id, name, description, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: rolePermissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."rolePermissions" (id, "roleId", "permissionId") FROM stdin;
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.roles (id, "roleName", description, "createdAt", "updatedAt") FROM stdin;
4	IT_ADMIN	System Administrator	2025-04-07 03:09:10.164	2025-04-07 03:09:10.164
3	HR_ADMIN	Human Resource Administrator	2025-04-07 03:09:10.163	2025-04-07 03:09:10.163
1	TRAINING_UNIT	Training Unit Administrator	2025-04-07 03:09:10.164	2025-04-07 03:09:10.164
2	EMPLOYEE	Regular Staff Member	2025-04-07 03:09:10.163	2025-04-07 03:09:10.163
\.


--
-- Data for Name: systemNotifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."systemNotifications" (id, "recipientUserId", message, type, "isRead", "relatedEntityType", "relatedEntityId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: training_material; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.training_material (id, title, description, "categoryId", status, "publishedAt", "materialDetails", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: training_participants; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.training_participants (id, "userId", "trainingMaterialId", "isCreator", "certificateKey", "assessmentFormKey", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: units; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.units (id, "unitName", "divisionId", "createdAt", "updatedAt") FROM stdin;
2	PLANNING	2	2025-04-07 03:09:10.097	2025-04-07 03:09:10.097
1	PSTO SORSOGON	2	2025-04-07 03:09:10.097	2025-04-07 03:09:10.097
3	PSTO ALBAY	2	2025-04-07 03:09:10.097	2025-04-07 03:09:10.097
4	PROCUREMENT UNIT	1	2025-04-07 03:09:10.097	2025-04-07 03:09:10.097
5	HUMAN RESOURCES MANAGEMENT	1	2025-04-07 03:09:10.097	2025-04-07 03:09:10.097
\.


--
-- Data for Name: userFileProgress; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."userFileProgress" (id, "userId", "moduleFileId", progress, "lastPage", "lastTimestamp", status, "completedAt", "lastAccessedAt", created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: userModuleProgress; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."userModuleProgress" (id, "userId", "moduleId", status, "startedAt", "completedAt", "lastAccessedAt", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: userPermissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."userPermissions" (id, "userId", "permissionId") FROM stdin;
\.


--
-- Data for Name: userSearches; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."userSearches" (id, "userId", "searchQuery", "createdAt") FROM stdin;
\.


--
-- Data for Name: userSessions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."userSessions" (id, "userId", token, "ipAddress", "userAgent", "expiresAt", created_at, "updatedAt") FROM stdin;
\.


--
-- Data for Name: userTrainingProgress; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."userTrainingProgress" (id, "userId", "trainingMaterialId", status, "progressPercentage", "startedAt", "completedAt", "lastAccessedAt", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, "unitId", "roleId", "firstName", "lastName", password, email, "employeeType", "createdAt", "updatedAt", "lastLoginAt", "failedLoginAttempts", "accountLockedUntil", "passwordResetExpires", "passwordResetToken") FROM stdin;
1	5	3	Jamil	Cervano	$2b$10$9DnZWVw9Ish.ETjCE0BXv.9P4Cu0wyCrGrJe10UAdGHiMpAVzMcTW	<EMAIL>	PERMANENT	2025-04-07 03:09:10.238	2025-04-07 03:09:10.238	\N	0	\N	\N	\N
3	5	3	Guirald	Escober	$2b$10$vOk4DisoKvKX3bR6leLIQ.RHHmGny4ylWcJzFT1iFJPORcWesHmg2	<EMAIL>	PERMANENT	2025-04-07 03:09:10.238	2025-04-07 08:19:59.373	\N	0	\N	\N	\N
2	5	3	Kenneth	Espela	$2b$10$9DnZWVw9Ish.ETjCE0BXv.9P4Cu0wyCrGrJe10UAdGHiMpAVzMcTW	<EMAIL>	PERMANENT	2025-04-07 03:09:10.238	2025-04-07 03:09:10.238	\N	0	\N	\N	\N
4	1	2	John	Amin	$2b$10$9DnZWVw9Ish.ETjCE0BXv.9P4Cu0wyCrGrJe10UAdGHiMpAVzMcTW	<EMAIL>	PERMANENT	2025-04-07 03:09:10.237	2025-04-07 03:09:10.237	\N	0	\N	\N	\N
\.


--
-- Name: auditLogs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."auditLogs_id_seq"', 1, false);


--
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.categories_id_seq', 1, false);


--
-- Name: competencies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.competencies_id_seq', 1, false);


--
-- Name: divisions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.divisions_id_seq', 3, true);


--
-- Name: materialApprovals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."materialApprovals_id_seq"', 1, false);


--
-- Name: materialStatusLogs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."materialStatusLogs_id_seq"', 1, false);


--
-- Name: moduleFiles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."moduleFiles_id_seq"', 1, false);


--
-- Name: modules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.modules_id_seq', 1, false);


--
-- Name: permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.permissions_id_seq', 1, false);


--
-- Name: rolePermissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."rolePermissions_id_seq"', 1, false);


--
-- Name: roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.roles_id_seq', 4, true);


--
-- Name: systemNotifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."systemNotifications_id_seq"', 1, false);


--
-- Name: training_material_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.training_material_id_seq', 1, false);


--
-- Name: training_participants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.training_participants_id_seq', 1, false);


--
-- Name: units_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.units_id_seq', 5, true);


--
-- Name: userFileProgress_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."userFileProgress_id_seq"', 1, false);


--
-- Name: userModuleProgress_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."userModuleProgress_id_seq"', 1, false);


--
-- Name: userPermissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."userPermissions_id_seq"', 1, false);


--
-- Name: userSearches_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."userSearches_id_seq"', 1, false);


--
-- Name: userSessions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."userSessions_id_seq"', 1, false);


--
-- Name: userTrainingProgress_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."userTrainingProgress_id_seq"', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 4, true);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: auditLogs auditLogs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."auditLogs"
    ADD CONSTRAINT "auditLogs_pkey" PRIMARY KEY (id);


--
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- Name: competencies competencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.competencies
    ADD CONSTRAINT competencies_pkey PRIMARY KEY (id);


--
-- Name: divisions divisions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.divisions
    ADD CONSTRAINT divisions_pkey PRIMARY KEY (id);


--
-- Name: materialApprovals materialApprovals_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialApprovals"
    ADD CONSTRAINT "materialApprovals_pkey" PRIMARY KEY (id);


--
-- Name: materialStatusLogs materialStatusLogs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialStatusLogs"
    ADD CONSTRAINT "materialStatusLogs_pkey" PRIMARY KEY (id);


--
-- Name: material_competencies material_competencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.material_competencies
    ADD CONSTRAINT material_competencies_pkey PRIMARY KEY (training_material_id, competency_id);


--
-- Name: moduleFiles moduleFiles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."moduleFiles"
    ADD CONSTRAINT "moduleFiles_pkey" PRIMARY KEY (id);


--
-- Name: modules modules_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.modules
    ADD CONSTRAINT modules_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: rolePermissions rolePermissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."rolePermissions"
    ADD CONSTRAINT "rolePermissions_pkey" PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: systemNotifications systemNotifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."systemNotifications"
    ADD CONSTRAINT "systemNotifications_pkey" PRIMARY KEY (id);


--
-- Name: training_material training_material_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_material
    ADD CONSTRAINT training_material_pkey PRIMARY KEY (id);


--
-- Name: training_participants training_participants_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_participants
    ADD CONSTRAINT training_participants_pkey PRIMARY KEY (id);


--
-- Name: units units_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.units
    ADD CONSTRAINT units_pkey PRIMARY KEY (id);


--
-- Name: userFileProgress userFileProgress_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userFileProgress"
    ADD CONSTRAINT "userFileProgress_pkey" PRIMARY KEY (id);


--
-- Name: userModuleProgress userModuleProgress_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userModuleProgress"
    ADD CONSTRAINT "userModuleProgress_pkey" PRIMARY KEY (id);


--
-- Name: userPermissions userPermissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userPermissions"
    ADD CONSTRAINT "userPermissions_pkey" PRIMARY KEY (id);


--
-- Name: userSearches userSearches_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userSearches"
    ADD CONSTRAINT "userSearches_pkey" PRIMARY KEY (id);


--
-- Name: userSessions userSessions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userSessions"
    ADD CONSTRAINT "userSessions_pkey" PRIMARY KEY (id);


--
-- Name: userTrainingProgress userTrainingProgress_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userTrainingProgress"
    ADD CONSTRAINT "userTrainingProgress_pkey" PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: categories_categoryName_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "categories_categoryName_key" ON public.categories USING btree ("categoryName");


--
-- Name: competencies_competencyName_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "competencies_competencyName_key" ON public.competencies USING btree ("competencyName");


--
-- Name: divisions_divisionName_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "divisions_divisionName_key" ON public.divisions USING btree ("divisionName");


--
-- Name: moduleFiles_fileKey_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "moduleFiles_fileKey_key" ON public."moduleFiles" USING btree ("fileKey");


--
-- Name: modules_trainingMaterialId_moduleOrder_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "modules_trainingMaterialId_moduleOrder_key" ON public.modules USING btree ("trainingMaterialId", "moduleOrder");


--
-- Name: permissions_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX permissions_name_key ON public.permissions USING btree (name);


--
-- Name: rolePermissions_roleId_permissionId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "rolePermissions_roleId_permissionId_key" ON public."rolePermissions" USING btree ("roleId", "permissionId");


--
-- Name: roles_roleName_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "roles_roleName_key" ON public.roles USING btree ("roleName");


--
-- Name: training_participants_userId_trainingMaterialId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "training_participants_userId_trainingMaterialId_key" ON public.training_participants USING btree ("userId", "trainingMaterialId");


--
-- Name: units_divisionId_unitName_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "units_divisionId_unitName_key" ON public.units USING btree ("divisionId", "unitName");


--
-- Name: units_unitName_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "units_unitName_key" ON public.units USING btree ("unitName");


--
-- Name: userFileProgress_userId_moduleFileId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "userFileProgress_userId_moduleFileId_key" ON public."userFileProgress" USING btree ("userId", "moduleFileId");


--
-- Name: userModuleProgress_userId_moduleId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "userModuleProgress_userId_moduleId_key" ON public."userModuleProgress" USING btree ("userId", "moduleId");


--
-- Name: userPermissions_userId_permissionId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "userPermissions_userId_permissionId_key" ON public."userPermissions" USING btree ("userId", "permissionId");


--
-- Name: userTrainingProgress_userId_trainingMaterialId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "userTrainingProgress_userId_trainingMaterialId_key" ON public."userTrainingProgress" USING btree ("userId", "trainingMaterialId");


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: users_passwordResetToken_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "users_passwordResetToken_key" ON public.users USING btree ("passwordResetToken");


--
-- Name: auditLogs auditLogs_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."auditLogs"
    ADD CONSTRAINT "auditLogs_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: materialApprovals materialApprovals_approverUserId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialApprovals"
    ADD CONSTRAINT "materialApprovals_approverUserId_fkey" FOREIGN KEY ("approverUserId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: materialApprovals materialApprovals_trainingMaterialId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialApprovals"
    ADD CONSTRAINT "materialApprovals_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES public.training_material(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: materialStatusLogs materialStatusLogs_changedByUserId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialStatusLogs"
    ADD CONSTRAINT "materialStatusLogs_changedByUserId_fkey" FOREIGN KEY ("changedByUserId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: materialStatusLogs materialStatusLogs_trainingMaterialId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."materialStatusLogs"
    ADD CONSTRAINT "materialStatusLogs_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES public.training_material(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: material_competencies material_competencies_competency_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.material_competencies
    ADD CONSTRAINT material_competencies_competency_id_fkey FOREIGN KEY (competency_id) REFERENCES public.competencies(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: material_competencies material_competencies_training_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.material_competencies
    ADD CONSTRAINT material_competencies_training_material_id_fkey FOREIGN KEY (training_material_id) REFERENCES public.training_material(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: moduleFiles moduleFiles_moduleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."moduleFiles"
    ADD CONSTRAINT "moduleFiles_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES public.modules(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: modules modules_trainingMaterialId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.modules
    ADD CONSTRAINT "modules_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES public.training_material(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: rolePermissions rolePermissions_permissionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."rolePermissions"
    ADD CONSTRAINT "rolePermissions_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES public.permissions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: rolePermissions rolePermissions_roleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."rolePermissions"
    ADD CONSTRAINT "rolePermissions_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: systemNotifications systemNotifications_recipientUserId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."systemNotifications"
    ADD CONSTRAINT "systemNotifications_recipientUserId_fkey" FOREIGN KEY ("recipientUserId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: training_material training_material_categoryId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_material
    ADD CONSTRAINT "training_material_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES public.categories(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: training_participants training_participants_trainingMaterialId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_participants
    ADD CONSTRAINT "training_participants_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES public.training_material(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: training_participants training_participants_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.training_participants
    ADD CONSTRAINT "training_participants_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: units units_divisionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.units
    ADD CONSTRAINT "units_divisionId_fkey" FOREIGN KEY ("divisionId") REFERENCES public.divisions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userFileProgress userFileProgress_moduleFileId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userFileProgress"
    ADD CONSTRAINT "userFileProgress_moduleFileId_fkey" FOREIGN KEY ("moduleFileId") REFERENCES public."moduleFiles"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userFileProgress userFileProgress_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userFileProgress"
    ADD CONSTRAINT "userFileProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userModuleProgress userModuleProgress_moduleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userModuleProgress"
    ADD CONSTRAINT "userModuleProgress_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES public.modules(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userModuleProgress userModuleProgress_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userModuleProgress"
    ADD CONSTRAINT "userModuleProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userPermissions userPermissions_permissionId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userPermissions"
    ADD CONSTRAINT "userPermissions_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES public.permissions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userPermissions userPermissions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userPermissions"
    ADD CONSTRAINT "userPermissions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userSearches userSearches_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userSearches"
    ADD CONSTRAINT "userSearches_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userSessions userSessions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userSessions"
    ADD CONSTRAINT "userSessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userTrainingProgress userTrainingProgress_trainingMaterialId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userTrainingProgress"
    ADD CONSTRAINT "userTrainingProgress_trainingMaterialId_fkey" FOREIGN KEY ("trainingMaterialId") REFERENCES public.training_material(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: userTrainingProgress userTrainingProgress_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."userTrainingProgress"
    ADD CONSTRAINT "userTrainingProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: users users_roleId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "users_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: users users_unitId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "users_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES public.units(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

