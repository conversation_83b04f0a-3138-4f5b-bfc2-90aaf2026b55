import NextAuth, { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import prisma from "@/lib/prisma";
import bcrypt from 'bcrypt';

export const authOptions: NextAuthOptions = {
    providers: [
        CredentialsProvider({
            name: 'Credentials',
            credentials: {
              email: { label: "Email", type: "email" },
              password: { label: "Password", type: "password" },
              remember: { label: "Remember Me", type: "checkbox" }
            },
            async authorize(credentials) {
              if (!credentials?.email || !credentials?.password) {
                return null;
              }

              try {
                const user = await prisma.user.findUnique({
                  where: { email: credentials.email },
                  include: {
                    role: true 
                  }
                });

                if (user && user.password && user.role) { 
                  const isValidPassword = await bcrypt.compare(credentials.password, user.password);

                  if (isValidPassword) {
                    return {
                      id: user.id.toString(),
                      email: user.email,
                      name: `${user.firstName} ${user.lastName}`,
                      role: user.role.roleName,
                      remember: credentials.remember === 'true'
                    };
                  }
                  return null;
                }
                return null;
              } catch {
                return null; 
              }
            }
        })
    ],

    callbacks: {
        async jwt({ token, user }) {
            if (user) {
              if ('role' in user && user.role) {
                  token.role = (user as { role: string }).role;
              }
              token.id = user.id;

              token.remember = ('remember' in user && typeof user.remember === 'boolean') ? user.remember : undefined;

              if (token.remember === false) {
                  const shortExpirySeconds = 3600;
                  token.exp = Math.floor(Date.now() / 1000) + shortExpirySeconds;
              }
            }
            return token;
        },
        async session({ session, token }) {
            if (session.user && token.role) {
                session.user.role = token.role as string; 
            }
            if (session.user && token.id) {
                session.user.id = token.id as string;    
            }
            return session;
        }
    },

    pages: {
        signIn: '/',
    },

    session: {
        strategy: "jwt",
        maxAge: 15 * 24 * 60 * 60,
    },

    secret: process.env.NEXTAUTH_SECRET,

    debug: process.env.NODE_ENV === 'development',
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
