import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';
import prisma from '@/lib/prisma';

const requestSchema = z.object({
  action: z.enum(['start', 'complete', 'update']),
  trainingMaterialId: z.union([z.number(), z.string().regex(/^\d+$/, 'must be a numeric id')]),
  progressPercentage: z
    .number()
    .min(0, 'Progress must be between 0-100')
    .max(100, 'Progress must be between 0-100')
    .optional(),
});

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { action, trainingMaterialId, progressPercentage } = requestSchema.parse(body);

    const userId = Number(session.user.id);
    const trainingId =
      typeof trainingMaterialId === 'string'
        ? Number(trainingMaterialId)
        : trainingMaterialId;

    if (Number.isNaN(userId) || Number.isNaN(trainingId)) {
      return NextResponse.json(
        { error: 'Invalid numeric id supplied' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'start': {
        const now = new Date();

        // First check if a record already exists to preserve original startedAt
        const existingProgress = await prisma.userTrainingProgress.findUnique({
          where: {
            userId_trainingMaterialId: {
              userId,
              trainingMaterialId: trainingId
            }
          }
        });

        const progress = await prisma.userTrainingProgress.upsert({
          where: {
            userId_trainingMaterialId: {
              userId,
              trainingMaterialId: trainingId
            }
          },
          update: {
            status: 'IN_PROGRESS',
            // Preserve original startedAt if it exists
            startedAt: existingProgress?.startedAt || now,
            lastAccessedAt: now,
            progressPercentage: progressPercentage || 0
          },
          create: {
            userId,
            trainingMaterialId: trainingId,
            status: 'IN_PROGRESS',
            progressPercentage: progressPercentage || 0,
            startedAt: now,
            lastAccessedAt: now
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Training progress started',
          progress
        });
      }

      case 'complete': {
        const now = new Date();

        const progress = await prisma.userTrainingProgress.upsert({
          where: {
            userId_trainingMaterialId: {
              userId,
              trainingMaterialId: trainingId
            }
          },
          update: {
            status: 'COMPLETED',
            progressPercentage: 100,
            completedAt: now,
            lastAccessedAt: now
          },
          create: {
            userId,
            trainingMaterialId: trainingId,
            status: 'COMPLETED',
            progressPercentage: 100,
            startedAt: now,
            completedAt: now,
            lastAccessedAt: now
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Training marked as completed',
          progress
        });
      }

      case 'update': {
        // Update the progress
        if (progressPercentage === undefined) {
          return NextResponse.json(
            { error: 'Progress percentage is required for update action' },
            { status: 400 }
          );
        }

        // This validation is redundant with the zod schema but kept for clarity
        if (progressPercentage < 0 || progressPercentage > 100) {
          return NextResponse.json(
            { error: 'progressPercentage must be 0-100' },
            { status: 400 }
          );
        }

        const now = new Date();
        const status = progressPercentage >= 100 ? 'COMPLETED' : 'IN_PROGRESS';
        const completedAt = progressPercentage >= 100 ? now : null;

        const progress = await prisma.userTrainingProgress.upsert({
          where: {
            userId_trainingMaterialId: {
              userId,
              trainingMaterialId: trainingId
            }
          },
          update: {
            status,
            progressPercentage,
            completedAt,
            lastAccessedAt: now
          },
          create: {
            userId,
            trainingMaterialId: trainingId,
            status,
            progressPercentage,
            startedAt: now,
            completedAt,
            lastAccessedAt: now
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Training progress updated',
          progress
        });
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Training progress error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.format() },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}