import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  unitId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  divisionId: z.string().optional().transform(val => val ? parseInt(val, 10) : null)
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'HR_ADMIN') {
      return NextResponse.json({ error: 'Forbidden: Insufficient permissions' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    
    // Validate query parameters using Zod
    const validationResult = querySchema.safeParse(Object.fromEntries(searchParams.entries()));
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { unitId, divisionId } = validationResult.data;

    // Build the where clause based on filters
    const whereClause: any = {};
    
    if (unitId) {
      whereClause.unitId = unitId;
    } else if (divisionId) {
      whereClause.unit = {
        divisionId
      };
    }

    // Get total count
    const totalCount = await prisma.user.count({
      where: whereClause
    });

    // Get counts by employee type
    const countByType = await prisma.user.groupBy({
      by: ['employeeType'],
      _count: true,
      where: whereClause
    });

    // Get trend (simple mock for now - would be calculated from historical data)
    // In a real implementation, you would compare to previous period
    const trend = 2.5; // 2.5% increase from previous period

    return NextResponse.json({
      totalCount,
      countByType: countByType.map(item => ({
        type: item.employeeType,
        count: item._count
      })),
      trend
    });
    
  } catch (error) {
    console.error('Error fetching employee count:', error);
    return NextResponse.json(
      { error: 'Failed to fetch employee count' },
      { status: 500 }
    );
  }
} 