// import { useState } from 'react';
// import { FiClock } from 'react-icons/fi';
// import CourseEditForm from '@/components/ui/CourseEditForm';

// interface CourseCardProps {
//   title: string;
//   instructor: string;
//   progress?: number;
//   lastAccessed: string;
//   lastViewed: string;
//   tag: string;
//   action: string;
//   category: string;
//   onEdit: (formData: any) => Promise<void>;
//   onArchive: () => void;
// }

// const CourseCard: React.FC<CourseCardProps> = ({ 
//   title, 
//   instructor, 
//   progress, 
//   lastAccessed, 
//   lastViewed, 
//   tag, 
//   action, 
//   category, 
//   onEdit, 
//   onArchive 
// }) => {
//   const [isEditModalOpen, setIsEditModalOpen] = useState(false);

//   const handleActionClick = () => {
//     if (action === "Continue Learning" || action === "Review Again") {
//     } else if (action === "Edit Course") {
//       setIsEditModalOpen(true);
//     }
//   };

//   const handleSave = async (formData: any) => {
//     try {
//       await onEdit(formData);
//       setIsEditModalOpen(false);
//     } catch (error) {
//       console.error("Error saving course:", error);
//     }
//   };

//   return (
//     <>
//       <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow p-4 w-auto max-h-96 flex flex-col justify-between">
//         <div className="flex items-center justify-between gap-3 mb-3">
//           <span className="bg-white border border-color3 text-color3 px-3 py-1 rounded-full text-sm">{tag}</span>
//           <h1 className="text-black text-sm text-end">{category}</h1>
//         </div>

//         <div className="p-2 flex flex-col justify-between gap-4 flex-grow">
//           <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">{title}</h3>
//           <p className="text-gray-600 text-center mb-4">{instructor}</p>

//           {progress !== undefined && (
//             <div className="mb-4">
//               <div className="flex justify-between text-sm mb-1">
//                 <span className="text-gray-900 font-medium">Progress</span>
//                 <span className="text-gray-900 font-medium">{progress}%</span>
//               </div>
//               <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
//                 <div
//                   className="h-full bg-[#0077CC] rounded-full"
//                   style={{ width: `${progress}%` }}
//                 />
//               </div>
//             </div>
//           )}

//           <div className="text-sm text-gray-500 mb-4">
//             <div className="flex items-center justify-center gap-1 mb-1">
//               <FiClock size={12} />
//               <span>Last accessed {lastAccessed}</span>
//             </div>
//             <div className="text-xs text-gray-400 text-center">
//               Last viewed: {lastViewed}
//             </div>
//           </div>

//           {action === "Archive" ? (
//             <button 
//               onClick={onArchive}
//               className="w-full bg-[#dd1c1a] border border-[#dd1c1a] text-white py-2 rounded-lg hover:bg-[#a31621] hover:text-white transition-colors"
//             >
//               Archive
//             </button>
//           ) : (
//             <button 
//               onClick={handleActionClick}
//               className="w-full bg-[#0077CC] text-white py-2 rounded-lg hover:bg-[#0066B3] transition-colors cursor-pointer"
//             >
//               {action}
//             </button>
//           )}
//         </div>
//       </div>

//       {isEditModalOpen && (
//         <CourseEditForm
//           isOpen={isEditModalOpen}
//           onClose={() => setIsEditModalOpen(false)}
//           courseData={{
//             title,
//             description: "",
//             category,
//             tag,
//             learningObjectives: [],
//           }}
//           onSave={handleSave}
//           onArchive={onArchive}
//         />
//       )}
//     </>
//   );
// };

// export default CourseCard;
