"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/Button"
import { Label } from "@/components/ui/label"
import FileUploader from "@/components/ui/FileUploader"
import FileList from "@/components/ui/FileList"
import { Alert, AlertDescription } from "@/components/ui/Alert"
import { AlertCircle, XCircle, X } from "lucide-react"
import { toast, Toaster } from "sonner"

// Constants for validation
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_CERT_TYPES = ['.pdf', '.jpg', '.jpeg', '.png'];
const ALLOWED_FORM_TYPES = ['.pdf', '.docx'];
const ERROR_TIMEOUT = 5000; // 5 seconds

interface FormData {
  certifications?: File[];
  complianceForms?: File[];
}

interface CertificationsStepProps {
  formData: FormData;
  updateFormData: (data: FormData) => void;
  nextStep: () => void;
  prevStep: () => void;
}

interface ValidationError {
  id: string;
  message: string;
  type: 'error' | 'warning';
  timestamp: number;
}

export default function CertificationsStep({ formData, updateFormData, nextStep, prevStep }: CertificationsStepProps) {
  const [certifications, setCertifications] = useState<File[]>(formData.certifications || [])
  const [complianceForms, setComplianceForms] = useState<File[]>(formData.complianceForms || [])
  const [errors, setErrors] = useState<ValidationError[]>([])

  // Auto-remove errors after timeout
  useEffect(() => {
    const now = Date.now();
    const timer = setInterval(() => {
      setErrors(prevErrors => 
        prevErrors.filter(error => 
          error.type === 'error' || // Keep errors until manually closed
          now - error.timestamp < ERROR_TIMEOUT // Keep recent warnings
        )
      );
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const addError = (message: string, type: 'error' | 'warning') => {
    const newError: ValidationError = {
      id: Math.random().toString(36).substr(2, 9),
      message,
      type,
      timestamp: Date.now()
    };
    setErrors(prev => [...prev, newError]);
  };

  const removeError = (errorId: string) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  };

  const validateFile = (file: File, isCompliance: boolean): ValidationError | null => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        id: Math.random().toString(36).substr(2, 9),
        message: `File "${file.name}" exceeds the maximum size of 10MB`,
        type: 'error',
        timestamp: Date.now()
      };
    }

    // Check file type
    const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
    const allowedTypes = isCompliance ? ALLOWED_FORM_TYPES : ALLOWED_CERT_TYPES;
    
    if (!fileExt || !allowedTypes.includes(fileExt)) {
      return {
        id: Math.random().toString(36).substr(2, 9),
        message: `File "${file.name}" is not an allowed file type. Allowed types: ${allowedTypes.join(', ')}`,
        type: 'error',
        timestamp: Date.now()
      };
    }

    return null;
  }

  const handleCertificationsUpload = (files: File[]) => {
    setErrors([]);
    
    // Only allow one certificate
    if (certifications.length >= 1) {
      addError('Only one certificate file is allowed. Remove the existing one first.', 'error');
      return;
    }

    // Only allow one file upload at a time
    if (files.length > 1) {
      addError('Please upload only one certificate file at a time', 'error');
      return;
    }

    const file = files[0];
    const error = validateFile(file, false);
    if (error) {
      setErrors(prev => [...prev, error]);
      return;
    }

    setCertifications([file]);
    updateFormData({ certifications: [file], complianceForms });
    toast.success("Certificate uploaded successfully");
  }

  const handleComplianceFormsUpload = (files: File[]) => {
    setErrors([]);

    // Only allow one assessment form
    if (complianceForms.length >= 1) {
      addError('Only one assessment form is allowed. Remove the existing one first.', 'error');
      return;
    }

    // Only allow one file upload at a time
    if (files.length > 1) {
      addError('Please upload only one assessment form file at a time', 'error');
      return;
    }

    const file = files[0];
    const error = validateFile(file, true);
    if (error) {
      setErrors(prev => [...prev, error]);
      return;
    }

    setComplianceForms([file]);
    updateFormData({ certifications, complianceForms: [file] });
    toast.success("Assessment form uploaded successfully");
  }

  const removeCertification = (index: number) => {
    const newCertifications = [...certifications];
    newCertifications.splice(index, 1);
    setCertifications(newCertifications);
    updateFormData({ certifications: newCertifications, complianceForms });
    toast.success("Certification removed");
  }

  const removeComplianceForm = (index: number) => {
    const newComplianceForms = [...complianceForms];
    newComplianceForms.splice(index, 1);
    setComplianceForms(newComplianceForms);
    updateFormData({ certifications, complianceForms: newComplianceForms });
    toast.success("Compliance form removed");
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setErrors([]);
    const validationErrors: ValidationError[] = [];

    // Validate compliance forms
    if (complianceForms.length === 0) {
      validationErrors.push({
        id: Math.random().toString(36).substr(2, 9),
        message: "An Assessment Form is required to continue",
        type: 'error',
        timestamp: Date.now()
      });
    }

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    nextStep();
  }

  return (
    <form onSubmit={handleSubmit}>
      <Toaster position="top-right" />
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Certifications & Forms Submission</h2>
          <p className="mt-1 text-sm text-gray-500">Upload required certifications and compliance forms.</p>
        </div>

        <Alert className="bg-amber-50 text-amber-800 border-amber-200">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            All documents will be checked for compliance before approval. An accomplished Assessment Form is required to continue.
            <ul className="mt-2 list-disc list-inside text-sm">
              <li>Maximum file size: 10MB</li>
              <li>Certificate (Optional): Only 1 file allowed ({ALLOWED_CERT_TYPES.join(', ')})</li>
              <li>Assessment Form (Required): Only 1 file allowed ({ALLOWED_FORM_TYPES.join(', ')})</li>
            </ul>
          </AlertDescription>
        </Alert>

        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error) => (
              <Alert 
                key={error.id} 
                className={`${
                  error.type === 'error' 
                    ? 'bg-red-50 text-red-800 border-red-200' 
                    : 'bg-amber-50 text-amber-800 border-amber-200'
                } relative`}
              >
                <div className="flex items-start">
                  <XCircle className="h-4 w-4 mt-1 flex-shrink-0" />
                  <AlertDescription className="ml-2">{error.message}</AlertDescription>
                  <button
                    onClick={() => removeError(error.id)}
                    className="absolute right-2 top-2 p-1 rounded-full hover:bg-gray-200/50"
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Close</span>
                  </button>
                </div>
                {error.type === 'warning' && (
                  <div className="mt-1 h-1 w-full bg-gray-200 rounded">
                    <div
                      className="h-1 bg-amber-500 rounded transition-all duration-1000"
                      style={{
                        width: `${Math.max(0, 100 - ((Date.now() - error.timestamp) / ERROR_TIMEOUT) * 100)}%`
                      }}
                    />
                  </div>
                )}
              </Alert>
            ))}
          </div>
        )}

        <div className="space-y-6">
          <div>
            <Label htmlFor="certifications">Certificate ({certifications.length}/1)</Label>
            <p className="text-sm text-gray-500 mb-2">Upload a relevant certification (Optional)</p>
            <FileUploader
              id="certifications"
              onFilesSelected={handleCertificationsUpload}
              accept={ALLOWED_CERT_TYPES.join(',')}
            />
            {certifications.length > 0 && (
              <div className="mt-4">
                <FileList files={certifications} onRemove={removeCertification} />
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="complianceForms">Assessment Form ({complianceForms.length}/1)</Label>
            <p className="text-sm text-gray-500 mb-2">Upload the mandatory assessment form</p>
            <FileUploader
              id="complianceForms"
              onFilesSelected={handleComplianceFormsUpload}
              accept={ALLOWED_FORM_TYPES.join(',')}
            />
            {complianceForms.length > 0 && (
              <div className="mt-4">
                <FileList files={complianceForms} onRemove={removeComplianceForm} />
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-between">
          <Button 
            className="border cursor-pointer border-gray-300 px-6 py-2 rounded-md hover:bg-gray-50" 
            variant="outline" 
            onClick={prevStep}
          >
            Previous
          </Button>
          <Button 
            className="bg-color3 cursor-pointer text-white px-6 py-2 rounded-md hover:bg-color3/95" 
            type="submit"
          >
            Next Step
          </Button>
        </div>
      </div>
    </form>
  )
}
