import React, { useState, useEffect } from 'react';
import { Bar, Doughnut } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface PerformanceMetricsProps {
  className?: string;
  timeframe?: string;
}

interface ReviewStatus {
  completed: number;
  pending: number;
  overdue: number;
}

interface PerformanceData {
  totalReviews: number;
  averageScore: number;
  reviewStatus: ReviewStatus;
  kpiAchievement: {
    labels: string[];
    data: number[];
  };
  goalCompletion: {
    labels: string[];
    data: number[];
  };
  departmentScores: {
    labels: string[];
    data: number[];
  };
}

const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({ 
  className,
  timeframe = 'This Week'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<PerformanceData | null>(null);

  useEffect(() => {
    const fetchPerformanceData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/hr-analytics/performance');
        if (!response.ok) throw new Error('Failed to fetch performance data');
        const performanceData = await response.json();
        setData(performanceData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching performance data');
        setData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPerformanceData();
  }, [timeframe]);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className} flex justify-center items-center min-h-[400px]`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className} flex flex-col justify-center items-center min-h-[400px]`}>
        <TrendingUp className="w-12 h-12 text-red-500 mb-4" />
        <p className="text-red-500 text-center">
          {error || 'No performance data available'}
        </p>
      </div>
    );
  }

  const kpiData: ChartData<'bar'> = {
    labels: data.kpiAchievement.labels,
    datasets: [{
      label: 'KPI Achievement',
      data: data.kpiAchievement.data,
      backgroundColor: '#3B82F6',
      borderColor: '#2563EB',
      borderWidth: 1
    }]
  };

  const goalCompletionData: ChartData<'bar'> = {
    labels: data.goalCompletion.labels,
    datasets: [{
      label: 'Goal Completion',
      data: data.goalCompletion.data,
      backgroundColor: '#10B981',
      borderColor: '#059669',
      borderWidth: 1
    }]
  };

  const reviewStatusData: ChartData<'doughnut'> = {
    labels: ['Completed', 'Pending', 'Overdue'],
    datasets: [{
      data: [
        data.reviewStatus.completed,
        data.reviewStatus.pending,
        data.reviewStatus.overdue
      ],
      backgroundColor: ['#10B981', '#F59E0B', '#EF4444'],
      borderWidth: 0
    }]
  };

  const barOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: value => `${value}%`
        }
      }
    },
    plugins: {
      legend: {
        display: false
      }
    }
  };

  const doughnutOptions: ChartOptions<'doughnut'> = {
    cutout: '70%',
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      }
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 sm:p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-800">Performance Overview</h2>
          <p className="text-sm text-gray-500">Employee performance metrics</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-800">{data.averageScore.toFixed(1)}</div>
          <div className="text-sm text-gray-500">Average Score</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">KPI Achievement</h3>
          <div className="h-[300px]">
            <Bar data={kpiData} options={barOptions} />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Goal Completion</h3>
          <div className="h-[300px]">
            <Bar data={goalCompletionData} options={barOptions} />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Review Status</h3>
          <div className="h-[300px] relative">
            <Doughnut data={reviewStatusData} options={doughnutOptions} />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-4">Review Summary</h3>
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-700">Total Reviews</p>
                  <p className="text-2xl font-bold text-gray-900">{data.totalReviews}</p>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-xs text-gray-500">Completed</p>
                    <p className="text-sm font-medium text-green-600">{data.reviewStatus.completed}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-gray-500">Pending</p>
                    <p className="text-sm font-medium text-amber-600">{data.reviewStatus.pending}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-gray-500">Overdue</p>
                    <p className="text-sm font-medium text-red-600">{data.reviewStatus.overdue}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMetrics; 