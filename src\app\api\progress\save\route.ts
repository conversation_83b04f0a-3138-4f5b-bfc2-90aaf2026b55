import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import redis from '@/lib/redis';
import { z } from 'zod';
import prisma from '@/lib/prisma';
import {
  materialProgressSchema,
  stepProgressSchema,
  moduleProgressSchema,
  courseProgressSchema,
  MaterialProgress,
  StepProgress,
  ModuleProgress,
  CourseProgress
} from '@/lib/schemas/progress';
import {
  acquireLock,
  releaseLock
} from '@/lib/services/progressCache';

const requestSchema = z.object({
  type: z.enum(['material', 'all']).default('all'),
  userId: z.string().optional(),
  courseId: z.string().optional(),
});

// Helper function to persist progress to DB
async function persistMaterialProgress(key: string): Promise<boolean> {
  try {
    const lockKey = `${key}`;
    const lockAcquired = await acquireLock(lock<PERSON><PERSON>, 30);

    if (!lockAcquired) {

      return false;
    }

    try {

      const cached = await redis.get(key);
      if (!cached) {

        return false;
      }


      const progress = materialProgressSchema.parse(JSON.parse(cached));




      let resourceId: number | null = null;
      let resource = null;

      if (progress.materialId.startsWith('http')) {


        resource = await prisma.trainingResource.findFirst({
          where: {
            resourceUrl: progress.materialId
          }
        });


        if (!resource) {
          const urlWithoutProtocol = progress.materialId.replace(/^https?:\/\//, '');
          resource = await prisma.trainingResource.findFirst({
            where: {
              resourceUrl: {
                contains: urlWithoutProtocol
              }
            }
          });
        }
      } else if (progress.materialId.includes('/api/files/view')) {

        const urlParams = new URLSearchParams(progress.materialId.split('?')[1]);
        const fileKey = urlParams.get('key');

        if (fileKey) {

          resource = await prisma.trainingResource.findFirst({
            where: {
              fileKey: fileKey
            }
          });

          // If not found, try a more flexible match
          if (!resource) {
            resource = await prisma.trainingResource.findFirst({
              where: {
                fileKey: {
                  contains: fileKey
                }
              }
            });
          }

          // If still not found, try matching against fileName
          if (!resource) {
            // Extract filename from path or key
            const fileName = fileKey.split('/').pop() || '';
            if (fileName) {

              resource = await prisma.trainingResource.findFirst({
                where: {
                  fileName: {
                    contains: fileName
                  }
                }
              });
            }
          }
        }
      } else {
        // Try parsing as numeric ID
        const numericId = Number(progress.materialId);
        if (Number.isNaN(numericId)) {
          console.warn(`Invalid numeric resource id: ${progress.materialId}`);
        } else {
          resource = await prisma.trainingResource.findUnique({
            where: {
              id: numericId
            }
          });
        }
      }

      if (!resource) {

        // Try to find by name as last resort
        resource = await prisma.trainingResource.findFirst({
          where: {
            fileName: {
              contains: progress.materialName
            }
          }
        });

        if (!resource) {
          return false;
        }
      }

      resourceId = resource.id;
      console.log(`3.) Adjusting Redis Database - Found resource ID: ${resourceId}`);


      const isImageFile = ['jpg', 'jpeg', 'png', 'image'].includes(
        progress.materialType.toLowerCase()
      );

      const status = (progress.progress >= 100 || progress.isViewed || (isImageFile && progress.progress > 0))
        ? 'COMPLETED'
        : 'IN_PROGRESS';

      console.log(`4.) Caching progress for Material ID: ${progress.materialId} Type: ${progress.materialType} - Progress: ${progress.progress}%, isViewed: ${progress.isViewed}, Status: ${status}`);

      // For image files, add extra logging
      if (isImageFile) {
        console.log(`Special handling for image file in persistence: ${progress.materialName} [${progress.materialType}]`);
      }

      // Persist to database
      await prisma.userFileProgress.upsert({
        where: {
          userId_resourceId: {
            userId: parseInt(progress.userId),
            resourceId: resourceId
          }
        },
        update: {
          progress: progress.progress,
          lastPage: progress.lastPosition || 0,
          status: status,
          completedAt: status === 'COMPLETED' ? new Date() : null,
          updated_at: new Date(),
          lastAccessedAt: new Date()
        },
        create: {
          userId: parseInt(progress.userId),
          resourceId: resourceId,
          progress: progress.progress,
          lastPage: progress.lastPosition || 0,
          status: status,
          completedAt: status === 'COMPLETED' ? new Date() : null,
          lastAccessedAt: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        }
      });

      console.log(`5.) Persisting Data to db - Completed for Material ID: ${progress.materialId}`);

      // Only delete from Redis after successful DB write
      await redis.del(key);
      console.log(`Redis key deleted after successful DB write: ${key}`);
    return true;
    } catch (error) {
      return false;
    } finally {
      await releaseLock(lockKey);

    }
  } catch (error) {
    return false;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await req.json();
    const { type, userId, courseId } = requestSchema.parse(body);

    // Validate user permissions if userId is specified
    const targetUserId = userId || session.user.id;
    if (userId && userId !== session.user.id) {
      // Check if user has admin privileges or permissions to manage other users
      // For now, we'll just return an error
      return NextResponse.json(
        { error: "Cannot save another user's progress without proper permissions" },
        { status: 403 }
      );
    }


    let keys: string[] = [];

    // Get all keys matching the pattern
    if (type === 'all') {
      let pattern = `progress:*:${targetUserId}:*`;

      // If courseId is specified, narrow down the pattern
      if (courseId) {
        pattern = `progress:*:${targetUserId}:${courseId}:*`;
      }

      // Use SCAN to find all matching keys
      let cursor = '0';
      do {
        const [nextCursor, scanKeys] = await redis.scan(
          cursor,
          'MATCH',
          pattern,
          'COUNT',
          '100'
        );
        cursor = nextCursor;
        keys = keys.concat(scanKeys);
      } while (cursor !== '0');
    } else if (type === 'material') {
      // We only handle material type for now
      let pattern = `progress:material:${targetUserId}:*`;

      // If courseId is specified, narrow down the pattern
      if (courseId) {
        pattern = `progress:material:${targetUserId}:${courseId}:*`;
      }


      // Use SCAN to find all matching keys
      let cursor = '0';
      do {
        const [nextCursor, scanKeys] = await redis.scan(
          cursor,
          'MATCH',
          pattern,
          'COUNT',
          '100'
        );
        cursor = nextCursor;
        keys = keys.concat(scanKeys);
      } while (cursor !== '0');
    }


    // Process all keys
    const results = await Promise.all(
      keys.map(async (key) => {
        try {
          // For now we only handle material progress
          if (key.startsWith('progress:material:')) {
            return await persistMaterialProgress(key);
          }
          return false;
        } catch (error) {
          console.error(`Error processing key ${key}:`, error);
          return false;
        }
      })
    );

    const successCount = results.filter(Boolean).length;

    return NextResponse.json({
      success: true,
      total: keys.length,
      persisted: successCount,
      failed: keys.length - successCount
    });
  } catch (error) {
    console.error('Progress save error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.format() },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}