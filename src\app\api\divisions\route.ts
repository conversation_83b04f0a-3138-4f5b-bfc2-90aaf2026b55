import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Define Zod schemas for the response data
const UnitSchema = z.object({
  id: z.number(),
  unitName: z.string()
});

const DivisionSchema = z.object({
  id: z.number(),
  divisionName: z.string(),
  units: z.array(UnitSchema)
});

const DivisionsResponseSchema = z.object({
  divisions: z.array(DivisionSchema)
});

export async function GET(request: NextRequest) {
  try {
    console.log('Divisions API called');
    
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      console.log('Auth failed: No session');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch all divisions
    const divisions = await prisma.division.findMany({
      select: {
        id: true,
        divisionName: true,
        units: {
          select: {
            id: true,
            unitName: true
          }
        }
      },
      orderBy: {
        divisionName: 'asc'
      }
    });
    
    console.log(`Found ${divisions.length} divisions`);

    // Validate response data against schema
    const response = { divisions };
    try {
      DivisionsResponseSchema.parse(response);
    } catch (validationError) {
      console.error('Response validation failed:', validationError);
      return NextResponse.json(
        { error: 'Invalid response data structure' },
        { status: 500 }
      );
    }

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error fetching divisions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch divisions' },
      { status: 500 }
    );
  }
} 