'use client';

import { Clock, CheckCircle2, FileText, Archive } from 'lucide-react';
import { MaterialStatus } from '@prisma/client';
import { LucideIcon } from 'lucide-react';

export const statusConfig: Record<MaterialStatus, { 
  icon: LucideIcon; 
  text: string; 
  className: string;
  description?: string;
}> = {
  PENDING_APPROVAL: { 
    icon: Clock, 
    text: 'Pending Approval', 
    className: 'text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full',
    description: 'Awaiting review from HR'
  },
  PUBLISHED: { 
    icon: CheckCircle2, 
    text: 'Published', 
    className: 'text-green-600 bg-green-50 px-2 py-1 rounded-full',
    description: 'Available to all users'
  },
  DRAFT: { 
    icon: FileText, 
    text: 'Draft', 
    className: 'text-gray-600 bg-gray-50 px-2 py-1 rounded-full',
    description: 'Not yet submitted for review'
  },
  ARCHIVED: { 
    icon: Archive, 
    text: 'Archived', 
    className: 'text-gray-600 bg-gray-50 px-2 py-1 rounded-full',
    description: 'No longer active or available'
  }
};

export const timeFilterOptions = ['This Week', 'This Month', 'This Quarter', 'This Year'];

export const stats = [
  { 
    title: 'Pending Approvals', 
    value: '3', 
    icon: Clock, 
    subtitle: 'Avg. wait time: 1.8 days', 
    color: 'text-color3' 
  },
  { 
    title: 'Published', 
    value: '2', 
    icon: CheckCircle2, 
    trend: 3, 
    color: 'text-color3' 
  },
  { 
    title: 'Draft', 
    value: '5', 
    icon: FileText, 
    progress: 65, 
    color: 'text-color3' 
  },
  { 
    title: 'Archived', 
    value: '12', 
    icon: Archive, 
    trend: -2, 
    color: 'text-color3' 
  }
];