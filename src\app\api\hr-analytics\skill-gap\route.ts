import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for validating query parameters
const querySchema = z.object({
  unitId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  divisionId: z.string().optional().transform(val => val ? parseInt(val, 10) : null),
  timeframe: z.enum(['week', 'month', 'quarter', 'year']).optional().default('month')
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'HR_ADMIN') {
      return NextResponse.json({ error: 'Forbidden: Insufficient permissions' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    
    // Validate query parameters using Zod
    const validationResult = querySchema.safeParse(Object.fromEntries(searchParams.entries()));
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid query parameters',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    
    const { unitId, divisionId, timeframe } = validationResult.data;

    // Calculate date range based on timeframe
    const today = new Date();
    let currentPeriodStart = new Date();
    let previousPeriodStart = new Date();
    
    switch (timeframe) {
      case 'week':
        currentPeriodStart.setDate(today.getDate() - 7);
        previousPeriodStart.setDate(today.getDate() - 14);
        break;
      case 'month':
        currentPeriodStart.setMonth(today.getMonth() - 1);
        previousPeriodStart.setMonth(today.getMonth() - 2);
        break;
      case 'quarter':
        currentPeriodStart.setMonth(today.getMonth() - 3);
        previousPeriodStart.setMonth(today.getMonth() - 6);
        break;
      case 'year':
        currentPeriodStart.setFullYear(today.getFullYear() - 1);
        previousPeriodStart.setFullYear(today.getFullYear() - 2);
        break;
      default:
        currentPeriodStart.setMonth(today.getMonth() - 1); // default to month
        previousPeriodStart.setMonth(today.getMonth() - 2);
    }

    // Get new competencies introduced in the current period
    const newCompetencies = await prisma.competency.findMany({
      where: {
        createdAt: {
          gte: currentPeriodStart
        }
      }
    });

    const newCompetenciesCount = newCompetencies.length;

    // Get competencies introduced in the previous period for trend calculation
    const previousPeriodCompetencies = await prisma.competency.findMany({
      where: {
        createdAt: {
          gte: previousPeriodStart,
          lt: currentPeriodStart
        }
      }
    });

    const previousPeriodCount = previousPeriodCompetencies.length;

    // Calculate trend percentage
    let trend = 0;
    if (previousPeriodCount > 0) {
      trend = parseFloat((((newCompetenciesCount - previousPeriodCount) / previousPeriodCount) * 100).toFixed(1));
    }

    // Get total competencies
    const totalCompetencies = await prisma.competency.count();

    // Get top recent competencies
    const topRecentCompetencies = await prisma.competency.findMany({
      where: {
        createdAt: {
          gte: currentPeriodStart
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5,
      select: {
        competencyName: true,
        createdAt: true
      }
    });

    // Calculate the rate of new competencies per day
    const rate = parseFloat((newCompetenciesCount / (timeframeInDays(timeframe))).toFixed(2));
    
    // Calculate a skill gap index from the rate for backward compatibility
    // Scale it to a 0-5 range where higher is more new competencies (indicating more skills being tracked)
    // This is the opposite of the original skill gap index (where lower was better)
    // but will at least provide a meaningful value for the frontend
    const skillGapIndex = Math.min(5, parseFloat((rate * 5).toFixed(1)));

    return NextResponse.json({
      newCompetenciesCount,
      rate,
      skillGapIndex, // Add this for backward compatibility with the frontend
      totalCompetencies,
      topRecentCompetencies: topRecentCompetencies.map(comp => ({
        name: comp.competencyName,
        createdAt: comp.createdAt
      })),
      trend,
      timeframe
    });
    
  } catch (error) {
    console.error('Error calculating new competencies rate:', error);
    return NextResponse.json(
      { error: 'Failed to calculate new competencies rate' },
      { status: 500 }
    );
  }
}

// Helper function to get number of days in a timeframe
function timeframeInDays(timeframe: string): number {
  switch (timeframe) {
    case 'week': return 7;
    case 'month': return 30;
    case 'quarter': return 90;
    case 'year': return 365;
    default: return 30;
  }
} 