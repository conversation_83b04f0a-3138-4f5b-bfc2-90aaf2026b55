export interface Course {
  title: string;
  instructor: string;
  progress?: number;
  students?: number;
  lastAccessed: string;
  lastViewed: string;
  tag: string;
  action: string;
  category: string;
}

export const learningCourses: Course[] = [
  {
    title: "Introduction to React",
    instructor: "<PERSON>",
    progress: 75,
    lastAccessed: "2 days ago",
    lastViewed: "Yesterday at 2:30 PM",
    tag: "Programming",
    action: "Continue Learning",
    category: "Training"
  },
  {
    title: "Advanced TypeScript",
    instructor: "<PERSON>",
    progress: 30,
    lastAccessed: "1 week ago",
    lastViewed: "Last week",
    tag: "Programming",
    action: "Continue Learning",
    category: "Training"
  },
  {
    title: "Data Science Fundamentals",
    instructor: "<PERSON>",
    progress: 45,
    lastAccessed: "3 days ago",
    lastViewed: "Today at 9:00 AM",
    tag: "Data Science",
    action: "Continue Learning",
    category: "Continuing Professional Education (CPE)"
  },
  {
    title: "UI/UX Design Principles",
    instructor: "<PERSON>",
    progress: 60,
    lastAccessed: "1 day ago",
    lastViewed: "Today at 11:30 AM",
    tag: "Design",
    action: "Continue Learning",
    category: "Training"
  },
  {
    title: "Marketing Analytics",
    instructor: "<PERSON>",
    progress: 15,
    lastAccessed: "4 days ago",
    lastViewed: "Yesterday at 4:00 PM",
    tag: "Marketing",
    action: "Continue Learning",
    category: "Symposia"
  },
  {
    title: "Database Management",
    instructor: "Emily Chen",
    progress: 90,
    lastAccessed: "Today",
    lastViewed: "1 hour ago",
    tag: "Database",
    action: "Continue Learning",
    category: "Training"
  },
  {
    title: "Business Strategy",
    instructor: "Robert Taylor",
    progress: 40,
    lastAccessed: "2 days ago",
    lastViewed: "Yesterday at 1:00 PM",
    tag: "Business",
    action: "Continue Learning",
    category: "Continuing Professional Education (CPE)"
  }
];

export const teachingCourses: Course[] = [
  {
    title: "Web Development Basics",
    instructor: "You",
    students: 150,
    lastAccessed: "1 day ago",
    lastViewed: "Today at 10:00 AM",
    tag: "Programming",
    action: "Edit Course",
    category: "Training"
  },
  {
    title: "Advanced SQL Techniques",
    instructor: "You",
    students: 85,
    lastAccessed: "2 days ago",
    lastViewed: "Yesterday at 3:00 PM",
    tag: "Database",
    action: "Edit Course",
    category: "Training"
  },
  {
    title: "Digital Marketing Workshop",
    instructor: "You",
    students: 200,
    lastAccessed: "Today",
    lastViewed: "2 hours ago",
    tag: "Marketing",
    action: "Edit Course",
    category: "Symposia"
  },
  {
    title: "Data Visualization with D3.js",
    instructor: "You",
    students: 120,
    lastAccessed: "3 days ago",
    lastViewed: "Yesterday at 5:00 PM",
    tag: "Data Science",
    action: "Edit Course",
    category: "Training"
  },
  {
    title: "Product Design Workshop",
    instructor: "You",
    students: 75,
    lastAccessed: "1 week ago",
    lastViewed: "3 days ago",
    tag: "Design",
    action: "Edit Course",
    category: "Continuing Professional Education (CPE)"
  }
];

export const archivedCourses: Course[] = [
  {
    title: "Python Fundamentals",
    instructor: "Alex Johnson",
    progress: 100,
    lastAccessed: "3 months ago",
    lastViewed: "3 months ago",
    tag: "Programming",
    action: "Review Again",
    category: "Training"
  },
  {
    title: "Project Management Essentials",
    instructor: "Maria Garcia",
    progress: 100,
    lastAccessed: "2 months ago",
    lastViewed: "2 months ago",
    tag: "Business",
    action: "Review Again",
    category: "Continuing Professional Education (CPE)"
  },
  {
    title: "Mobile App Design",
    instructor: "David Lee",
    progress: 100,
    lastAccessed: "4 months ago",
    lastViewed: "4 months ago",
    tag: "Design",
    action: "Review Again",
    category: "Training"
  },
  {
    title: "Content Marketing Strategy",
    instructor: "Lisa Anderson",
    progress: 100,
    lastAccessed: "1 month ago",
    lastViewed: "1 month ago",
    tag: "Marketing",
    action: "Review Again",
    category: "Symposia"
  }
];

export const categories = ['Training', 'Symposia', 'Continuing Professional Education (CPE)'];
export const types = ['Video Course', 'Document Based', 'Interactive'];
export const tags = ['Programming', 'Data Science', 'Business', 'Design', 'Marketing', 'Database']; 