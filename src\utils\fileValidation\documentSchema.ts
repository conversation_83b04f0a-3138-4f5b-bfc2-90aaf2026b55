import { z } from 'zod';

export const documentSchema = z.object({
  type: z.enum(['certificate', 'assessmentForm'], {
    errorMap: () => ({ message: 'Invalid document type' })
  }),
  materialId: z.string().min(1, 'Material ID is required').transform((val) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed)) {
      throw new Error('Invalid Material ID format');
    }
    return parsed;
  }),
});

export type DocumentSchema = z.infer<typeof documentSchema>;

export const validateDocumentRequest = (data: unknown): { success: boolean; data?: DocumentSchema; error?: string } => {
  try {
    const validatedData = documentSchema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors.map(e => e.message).join(', ')
      };
    }
    return { success: false, error: 'Invalid request data' };
  }
}; 