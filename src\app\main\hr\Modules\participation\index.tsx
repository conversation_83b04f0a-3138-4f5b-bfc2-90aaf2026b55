'use client';

import React, { useRef } from 'react';
import { useSidebar } from '@/components/layout/HRsidebar';
import { 
  Users, 
  CircleCheck, 
  BookOpenText, 
  Calendar,
  Filter,
  Download,
  Clock,
  ChevronDown
} from 'lucide-react';
import { Bar } from 'react-chartjs-2';
import { StatCard, DepartmentProgress, ContributorCard, UnitDropdown } from './components';
import { useParticipationData } from './hooks/useParticipationData';
import styles from '../styles/customScrollbar.module.css';

const ParticipationModule: React.FC = () => {
  const { isCollapsed } = useSidebar();
  const {
    timeFilter,
    setTimeFilter,
    selectedUnit,
    setSelectedUnit,
    selectedDepartmentUnit,
    setSelectedDepartmentUnit,
    participationStats,
    departmentProgress,
    contributors,
    chartData,
    chartOptions
  } = useParticipationData();

  const filterRef = useRef<HTMLDivElement>(null);
  const unitFilterRef = useRef<HTMLDivElement>(null);
  const departmentUnitFilterRef = useRef<HTMLDivElement>(null);
  const [isFilterOpen, setIsFilterOpen] = React.useState<boolean>(false);
  const [isUnitFilterOpen, setIsUnitFilterOpen] = React.useState<boolean>(false);
  const [isDepartmentUnitFilterOpen, setIsDepartmentUnitFilterOpen] = React.useState<boolean>(false);

  const handleFilterChange = (filter: string): void => {
    setTimeFilter(filter);
    setIsFilterOpen(false);
  };

  const handleUnitChange = (unit: string): void => {
    setSelectedUnit(unit);
    setIsUnitFilterOpen(false);
  };

  const handleDepartmentUnitChange = (unit: string): void => {
    setSelectedDepartmentUnit(unit);
    setIsDepartmentUnitFilterOpen(false);
  };

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsFilterOpen(false);
      }
      if (unitFilterRef.current && !unitFilterRef.current.contains(event.target as Node)) {
        setIsUnitFilterOpen(false);
      }
      if (departmentUnitFilterRef.current && !departmentUnitFilterRef.current.contains(event.target as Node)) {
        setIsDepartmentUnitFilterOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className='w-full max-h-screen overflow-y-auto'>
      <div className='max-w-full mx-auto space-y-6'>
        <div className='flex justify-between items-center'>
          <div>
            <h1 className='text-2xl font-semibold text-gray-800'>Participation Tracking</h1>
            <p className='text-sm text-gray-500'>Track employee participation in training programs</p>
          </div>
          <div className='flex gap-2'>
            <div className='relative' ref={filterRef}>
              <button 
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className='flex gap-2 items-center text-sm rounded-md bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-3 py-2 cursor-pointer'
              >
                <Calendar size={16} />
                <span>{timeFilter}</span>
                <ChevronDown size={14} />
              </button>
              {isFilterOpen && (
                <div className='absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10'>
                  <ul className='py-1'>
                    {['This Week', 'This Month', 'This Quarter', 'This Year'].map((filter) => (
                      <li key={filter}>
                        <button
                          onClick={() => handleFilterChange(filter)}
                          className={`block w-full text-left px-4 py-2 text-sm cursor-pointer ${timeFilter === filter ? 'bg-gray-100 text-color3' : 'hover:bg-gray-50'}`}
                        >
                          {filter}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
            <button 
              onClick={() => {
  
              }}
              className='flex gap-2 items-center text-sm rounded-md bg-color3 hover:bg-color3/95 text-white px-4 py-2'
            >
              <Download size={16} />
              <span>Export Report</span>
            </button>
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          <StatCard
            title="Total Participants"
            value={participationStats.employees.total}
            icon={Users}
            trend={participationStats.employees.trend}
            subtitle={participationStats.employees.subtitle}
          />
          <StatCard
            title="Avg. Completion Rate"
            value={`${participationStats.average.completion}%`}
            icon={CircleCheck}
            trend={participationStats.average.trend}
            subtitle={participationStats.average.subtitle}
          />
          <StatCard
            title="Knowledge Sharing"
            value={participationStats.share.contributor}
            icon={BookOpenText}
            trend={participationStats.share.trend}
            subtitle={participationStats.share.subtitle}
          />
          <StatCard
            title="Active Time"
            value="4.2h"
            icon={Clock}
            trend={3.5}
            subtitle="Avg. daily learning time"
            color="text-purple-600"
          />
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          <div className='bg-white rounded-xl shadow-lg shadow-gray-200/50 p-6 flex flex-col h-full border border-gray-100'>
            <div className='mb-6'>
              <div className='flex justify-between items-start mb-4'>
                <div>
                  <h2 className='text-lg font-semibold text-gray-800 mb-1'>Department Participation</h2>
                  <p className='text-sm text-gray-500'>Training participation by department</p>
                </div>
                <div className="relative" ref={departmentUnitFilterRef}>
                  <button
                    onClick={() => setIsDepartmentUnitFilterOpen(!isDepartmentUnitFilterOpen)}
                    className="flex items-center gap-2 px-2.5 py-1.5 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer"
                  >
                    <Filter size={14} className="text-gray-500" />
                    <span className="text-gray-700 max-w-[180px] truncate">{selectedDepartmentUnit}</span>
                    <ChevronDown size={14} className="text-gray-500" />
                  </button>
                  {isDepartmentUnitFilterOpen && (
                    <UnitDropdown
                      isOpen={isDepartmentUnitFilterOpen}
                      selectedUnit={selectedDepartmentUnit}
                      onUnitChange={handleDepartmentUnitChange}
                      onClose={() => setIsDepartmentUnitFilterOpen(false)}
                    />
                  )}
                </div>
              </div>
              <div className='bg-gray-50 rounded-lg p-4 mb-4'>
                <Bar data={chartData} options={chartOptions} height={100} />
              </div>
            </div>
            <div className={`space-y-2 flex-1 max-h-[400px] overflow-y-auto ${styles.customScrollbar}`}>
              {departmentProgress
                .filter(dept => selectedDepartmentUnit === 'All Units' || dept.unit === selectedDepartmentUnit)
                .map((dept, index) => (
                  <DepartmentProgress
                    key={index}
                    {...dept}
                  />
                ))}
            </div>
          </div>
   
          <div className='bg-white rounded-xl shadow-lg shadow-gray-200/50 p-6 flex flex-col h-full border border-gray-100'>
            <div className='mb-6'>
              <div className='flex justify-between items-start mb-4'>
                <div>
                  <h2 className='text-lg font-semibold text-gray-800 mb-1'>Top Knowledge Contributors</h2>
                  <p className='text-sm text-gray-500'>Most active employees in sharing knowledge</p>
                </div>
                <div className="relative" ref={unitFilterRef}>
                  <button
                    onClick={() => setIsUnitFilterOpen(!isUnitFilterOpen)}
                    className="flex items-center gap-2 px-2.5 py-1.5 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer"
                  >
                    <Filter size={14} className="text-gray-500" />
                    <span className="text-gray-700 max-w-[180px] truncate">{selectedUnit}</span>
                    <ChevronDown size={14} className="text-gray-500" />
                  </button>
                  {isUnitFilterOpen && (
                    <UnitDropdown
                      isOpen={isUnitFilterOpen}
                      selectedUnit={selectedUnit}
                      onUnitChange={handleUnitChange}
                      onClose={() => setIsUnitFilterOpen(false)}
                    />
                  )}
                </div>
              </div>
              <div className='grid grid-cols-3 gap-4 mb-6'>
                <div className='bg-gray-50 rounded-lg p-4 text-center'>
                  <p className='text-sm text-gray-600 mb-1'>Total Resources</p>
                  <p className='text-2xl font-bold text-color3'>171</p>
                </div>
                <div className='bg-gray-50 rounded-lg p-4 text-center'>
                  <p className='text-sm text-gray-600 mb-1'>Avg. Rating</p>
                  <p className='text-2xl font-bold text-green-600'>4.8</p>
                </div>
                <div className='bg-gray-50 rounded-lg p-4 text-center'>
                  <p className='text-sm text-gray-600 mb-1'>Active Contributors</p>
                  <p className='text-2xl font-bold text-purple-600'>24</p>
                </div>
              </div>
            </div>
            <div className={`space-y-4 flex-1 max-h-[400px] overflow-y-auto ${styles.customScrollbar}`}>
              {contributors
                .filter(contributor => selectedUnit === 'All Units' || contributor.unit === selectedUnit)
                .map((contributor, index) => (
                  <ContributorCard
                    key={index}
                    {...contributor}
                  />
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParticipationModule; 