'use client';

import { useState, useMemo } from 'react';
import { Bell, Check, X, Search, AlertCircle, Funnel, Calendar, ChevronDown, Eye } from 'lucide-react';
import { ReviewData, TrainingParticipant } from '../types';
import Swal from 'sweetalert2';

interface ReviewParticipantsProps {
  data: ReviewData;
  nextStep: () => void;
  prevStep: () => void;
}

// Helper function to highlight text
const highlightText = (text: string, searchQuery: string) => {
  if (!searchQuery.trim()) return text;

  const regex = new RegExp(`(${searchQuery.trim()})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part, index) => {
    if (part.toLowerCase() === searchQuery.toLowerCase()) {
      return <span key={index} className="text-blue-600 font-semibold">{part}</span>;
    }
    return part;
  });
};

const ReviewParticipants = ({ data, nextStep, prevStep }: ReviewParticipantsProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState<'name' | 'email'>('name');
  const itemsPerPage = 5;
  const [isNotifying, setIsNotifying] = useState(false);

  // Filter participants based on search query and status
  const filteredParticipants = useMemo(() => {
    // Check if data and trainingParticipants exist before filtering
    if (!data || !data.trainingParticipants) {
      return [];
    }
    return data.trainingParticipants.filter((participant: TrainingParticipant) => {
      const searchTerms = searchQuery.toLowerCase().trim();
      const participantName = `${participant.user.firstName} ${participant.user.lastName}`;
      const matchesSearch = !searchTerms || 
        [participantName, participant.user.email]
          .some(field => field.toLowerCase().includes(searchTerms));
      
      // Assuming non-null key means the item exists
      const hasAssessment = !!participant.assessmentFormKey;
      const hasCertification = !!participant.certificateKey;

      const matchesStatus = selectedStatus === 'all' || 
        (selectedStatus === 'complete' && hasAssessment && hasCertification) ||
        (selectedStatus === 'incomplete' && (!hasAssessment || !hasCertification));
      
      return matchesSearch && matchesStatus;
    });
  }, [data?.trainingParticipants, searchQuery, selectedStatus]);

  // Sort participants
  const sortedParticipants = useMemo(() => {
    return [...filteredParticipants].sort((a: TrainingParticipant, b: TrainingParticipant) => {
      const nameA = `${a.user.firstName} ${a.user.lastName}`;
      const nameB = `${b.user.firstName} ${b.user.lastName}`;
      if (sortBy === 'name') {
        return nameA.localeCompare(nameB);
      } else {
        return a.user.email.localeCompare(b.user.email);
      }
    });
  }, [filteredParticipants, sortBy]);

  // Get participants with missing requirements
  const participantsWithMissing = useMemo(() => {
    return sortedParticipants.filter(
      (participant: TrainingParticipant) => !participant.assessmentFormKey || !participant.certificateKey
    );
  }, [sortedParticipants]);

  // Pagination calculations
  const totalItems = sortedParticipants.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentParticipants = sortedParticipants.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleNotify = async (participant: TrainingParticipant) => {
    const missingItems = [];
    // Use the derived boolean flags or check keys directly
    const hasAssessment = !!participant.assessmentFormKey;
    const hasCertification = !!participant.certificateKey;
    if (!hasAssessment) missingItems.push('Assessment Form');
    if (!hasCertification) missingItems.push('Certification');
    
    const participantName = `${participant.user.firstName} ${participant.user.lastName}`;

    if (missingItems.length === 0) {
      await Swal.fire({
        title: 'No Action Needed',
        text: `${participantName} has already submitted all required documents.`,
        icon: 'info',
        confirmButtonColor: '#0077CC',
      });
      return;
    }

    const result = await Swal.fire({
      title: 'Send Notification?',
      html: `
        Are you sure you want to notify <strong>${participantName}</strong> about missing:<br>
        <ul style="text-align: left; margin-top: 10px;">
          ${missingItems.map(item => `<li>• ${item}</li>`).join('')}
        </ul>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#0077CC',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, notify',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      setIsNotifying(true);
      const missingItemsText = missingItems.join(' and ');
      const notificationMessage = `Please submit the following requirements ${missingItemsText} for the Training Material entitled "${data.title}".`;
      try {
        const response = await fetch('/api/notifications/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recipientUserId: participant.user.id,
            message: notificationMessage,
            type: 'MISSING_REQUIREMENTS',
            relatedEntityType: 'TRAINING_MATERIAL',
            relatedEntityId: data.id
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: 'Failed to send notification' }));
          throw new Error(errorData.error || `Server error: ${response.status}`);
        }

        await Swal.fire({
          title: 'Notification Sent!',
          text: `${participantName} has been notified about the missing requirements.`,
          icon: 'success',
          confirmButtonColor: '#0077CC',
        });
      } catch (error: unknown) {
        console.error('Failed to send notification:', error);
        const message = error instanceof Error ? error.message : 'Could not send notification. Please try again.';
        await Swal.fire('Error!', message, 'error');
      } finally {
        setIsNotifying(false);
      }
    }
  };

  const handleNotifyAll = async () => {
    if (participantsWithMissing.length === 0) {
      await Swal.fire({
        title: 'No Action Needed',
        text: 'All participants have submitted their required documents.',
        icon: 'info',
        confirmButtonColor: '#0077CC',
      });
      return;
    }

    const result = await Swal.fire({
      title: 'Send Notifications?',
      html: `
        <div class="text-left mb-4">
          Are you sure you want to notify all participants with missing requirements?
        </div>
        <div class="max-h-[300px] overflow-y-auto border border-gray-200 rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-color1 sticky top-0">
              <tr>
                <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">
                  Participant
                </th>
                <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">
                  Missing Requirements
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              ${participantsWithMissing.map((p: TrainingParticipant) => {
                const participantName = `${p.user.firstName} ${p.user.lastName}`;
                const hasAssessment = !!p.assessmentFormKey;
                const hasCertification = !!p.certificateKey;
                return `
                <tr class="hover:bg-gray-50">
                  <td class="px-4 py-2 text-sm text-gray-900">
                    ${highlightText(participantName, searchQuery)} 
                  </td>
                  <td class="px-4 py-2 text-sm text-gray-500">
                    <ul class="list-inside">
                      ${!hasAssessment ? '<li>Assessment Form</li>' : ''}
                      ${!hasCertification ? '<li>Certification</li>' : ''}
                    </ul>
                  </td>
                </tr>
              `}).join('')}
            </tbody>
          </table>
        </div>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#0077CC',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, notify all',
      cancelButtonText: 'Cancel',
      customClass: {
        htmlContainer: 'swal-html-container',
      },
      didOpen: () => {
        const style = document.createElement('style');
        style.textContent = `
          .swal-html-container {
            margin: 1rem 0 !important;
          }
          .swal2-popup {
            width: 32em !important;
          }
        `;
        document.head.appendChild(style);
      }
    });

    if (result.isConfirmed) {
      setIsNotifying(true);
      let successCount = 0;
      let errorCount = 0;

      const notificationPromises = participantsWithMissing.map(p => {
        const missingItems = [];
        if (!p.assessmentFormKey) missingItems.push('Assessment Form');
        if (!p.certificateKey) missingItems.push('Certification');
        const missingItemsText = missingItems.join(' and ');
        const message = `Please submit the following requirements ${missingItemsText} for the Training Material entitled "${data.title}".`;
        
        return fetch('/api/notifications/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recipientUserId: p.user.id,
            message: message,
            type: 'MISSING_REQUIREMENTS',
            relatedEntityType: 'TRAINING_MATERIAL',
            relatedEntityId: data.id
          }),
        });
      });

      try {
        const results = await Promise.allSettled(notificationPromises);
        results.forEach((res, index) => {
          if (res.status === 'fulfilled' && res.value.ok) {
            successCount++;
          } else {
            errorCount++;
            const participantName = `${participantsWithMissing[index].user.firstName} ${participantsWithMissing[index].user.lastName}`;
            const errorMessage = `Failed to notify ${participantName}.`;
            if (res.status === 'fulfilled' && !res.value.ok) {
               // Try to get error from response body
               res.value.json().then(errData => console.error(errorMessage, errData.error || `Status: ${res.value.status}`)).catch(() => console.error(errorMessage, `Status: ${res.value.status}`));
            } else if (res.status === 'rejected') {
               console.error(errorMessage, res.reason);
            }
          }
        });

        let title = 'Notifications Sent!';
        let text = `${successCount} notification(s) sent successfully.`;
        let icon: 'success' | 'warning' | 'error' = 'success';

        if (errorCount > 0) {
          text += ` ${errorCount} failed. Check console for details.`;
          icon = successCount > 0 ? 'warning' : 'error';
          title = successCount > 0 ? 'Partial Success' : 'Sending Failed';
        }

        await Swal.fire({
          title: title,
          text: text,
          icon: icon,
          confirmButtonColor: '#0077CC',
        });

      } catch (error: unknown) {
          console.error('Unexpected error during notify all:', error);
          const message = error instanceof Error ? error.message : 'An unexpected error occurred while sending notifications.';
          await Swal.fire('Error!', message, 'error');
      } finally {
        setIsNotifying(false);
      }
    }
  };

  // Function to handle viewing a document
  const handleViewDocument = (fileKey: string | null) => {
    if (!fileKey) {
      console.error('No file key provided.');
      // Optionally show an error message to the user
      return;
    }
    // Construct the URL to the download endpoint
    // IMPORTANT: Replace with your actual download endpoint structure
    const downloadUrl = `/api/training-material/download?key=${encodeURIComponent(fileKey)}`;
    window.open(downloadUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className=''>
    <div className="space-y-8 shadow-sm p-4">
      {/* Header with Search, Filters, and Notify All */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="flex-1 w-full">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search participants..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-color3/20"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Funnel size={15} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="appearance-none cursor-pointer pl-9 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white text-sm text-gray-600"
            >
              <option value="all">All Status</option>
              <option value="complete">Complete</option>
              <option value="incomplete">Incomplete</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={15} />
          </div>

          <div className="relative">
            <Calendar size={15} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'email')}
              className="appearance-none cursor-pointer pl-9 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white text-sm text-gray-600"
            >
              <option value="name">Sort by Name</option>
              <option value="email">Sort by Email</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={15} />
          </div>

          <button
            onClick={handleNotifyAll}
            disabled={isNotifying || participantsWithMissing.length === 0}
            className={`flex items-center cursor-pointer gap-2 px-4 py-2 bg-color3 text-white rounded-lg hover:bg-color3/90 transition-colors ${isNotifying || participantsWithMissing.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isNotifying ? 
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div> : 
              <AlertCircle className="w-4 h-4" />
            }
            <span>{isNotifying ? 'Sending...' : 'Notify Missing'}</span>
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-lg border border-gray-200">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr className="bg-color1">
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                Participant
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                Assessment Form
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                Certification
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                Notify
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentParticipants.map((participant: TrainingParticipant) => {
              const participantName = `${participant.user.firstName} ${participant.user.lastName}`;
              const hasAssessment = !!participant.assessmentFormKey;
              const hasCertification = !!participant.certificateKey;

              return (
                <tr key={participant.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {highlightText(participantName, searchQuery)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    <div className="flex items-center justify-center gap-2">
                      {hasAssessment ? 
                        <Check className="h-5 w-5 text-green-500" /> : 
                        <X className="h-5 w-5 text-red-500" />
                      }
                      {hasAssessment && (
                        <button 
                          onClick={() => handleViewDocument(participant.assessmentFormKey)}
                          title="View Assessment Form"
                          className="text-gray-400 hover:text-color3 focus:outline-none p-1 rounded-md hover:bg-gray-100"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    <div className="flex items-center justify-center gap-2">
                      {hasCertification ? 
                        <Check className="h-5 w-5 text-green-500" /> : 
                        <X className="h-5 w-5 text-red-500" />
                      }
                      {hasCertification && (
                        <button 
                          onClick={() => handleViewDocument(participant.certificateKey)}
                          title="View Certificate"
                          className="text-gray-400 hover:text-color3 focus:outline-none p-1 rounded-md hover:bg-gray-100"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    <button
                      onClick={() => handleNotify(participant)}
                      disabled={(hasAssessment && hasCertification) || isNotifying}
                      className={`p-1 rounded-md ${hasAssessment && hasCertification ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-100'} ${isNotifying ? 'opacity-50 cursor-wait' : ''}`}
                      title={isNotifying ? "Sending..." : "Notify participant"}
                    >
                      <Bell className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              );
            })}
            {currentParticipants.length === 0 && (
              <tr>
                <td colSpan={4} className="px-6 py-8 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <Search className="w-10 h-10 text-gray-300 mb-2" />
                    <p className="text-gray-500 text-sm">
                      {searchQuery.trim() 
                        ? `No participants found matching "${searchQuery}"`
                        : 'No participants found'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalItems > 0 && (
        <div className="flex items-center justify-between    px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`relative inline-flex items-center rounded-md px-4 py-2 text-sm font-medium ${
                currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } border border-gray-300`}
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`relative ml-3 inline-flex items-center rounded-md px-4 py-2 text-sm font-medium ${
                currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } border border-gray-300`}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastItem, totalItems)}
                </span>{' '}
                of <span className="font-medium">{totalItems}</span> results
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 ${
                    currentPage === 1 ? 'cursor-not-allowed' : ''
                  }`}
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                  </svg>
                </button>
                {[...Array(totalPages)].map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handlePageChange(index + 1)}
                    className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                      currentPage === index + 1
                        ? 'z-10 bg-color3 text-white  focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-color3'
                        : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 ${
                    currentPage === totalPages ? 'cursor-not-allowed' : ''
                  }`}
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
      </div>
         
             {/* Navigation */}
      <div className="flex justify-between pt-6">
        <button
          onClick={prevStep}
          className="px-6 py-2 cursor-pointer border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Previous 
        </button>
        <button
          onClick={nextStep}
          className="px-6 py-2 cursor-pointer bg-color3 text-white rounded-lg hover:bg-color3/90 transition-colors"
        >
          Next 
        </button>
      </div>
  
    </div>
    
  );
};

export default ReviewParticipants; 