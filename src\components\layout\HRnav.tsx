'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Megaphone, LogOut, Search, X, Menu, Users, BookOpen, FileText, Building2 } from 'lucide-react';
import { useSidebar } from '@/components/layout/HRsidebar';
import { signOut, useSession } from 'next-auth/react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  read: boolean;
  path: string;
  relatedEntityType?: string;
  relatedEntityId?: number | string;
}

// Search result types
type ResultType = 'course' | 'publication' | 'department' | 'contributor';

interface SearchResult {
  id: string;
  title: string;
  subtitle: string;
  type: ResultType;
  url: string;
}

// Mock data for search results
const mockSearchData: SearchResult[] = [
  // Publications
  { id: '1', title: 'Project Management Fundamentals', subtitle: 'Operations • By <PERSON>', type: 'publication', url: '/main/hr?module=approvals' },
  { id: '2', title: 'Leadership Fundamentals', subtitle: 'Training • By <PERSON>', type: 'publication', url: '/main/hr?module=approvals' },
  { id: '3', title: 'Cybersecurity Basics', subtitle: 'IT • By Mike <PERSON>', type: 'publication', url: '/main/hr?module=approvals' },
  
  // Departments
  { id: 'd1', title: 'IT Department', subtitle: '28 employees • 83% participation', type: 'department', url: '/main/hr?module=participation' },
  { id: 'd2', title: 'Training Department', subtitle: '15 employees • 92% participation', type: 'department', url: '/main/hr?module=participation' },
  { id: 'd3', title: 'Operations Department', subtitle: '42 employees • 76% participation', type: 'department', url: '/main/hr?module=participation' },
  { id: 'd4', title: 'Marketing Department', subtitle: '18 employees • 65% participation', type: 'department', url: '/main/hr?module=participation' },
  
  // Contributors
  { id: 'c1', title: 'Sarah Williams', subtitle: 'Training • 42 points', type: 'contributor', url: '/main/hr?module=participation' },
  { id: 'c2', title: 'Michael Brown', subtitle: 'IT • 38 points', type: 'contributor', url: '/main/hr?module=participation' },
  { id: 'c3', title: 'Emily Davis', subtitle: 'Marketing • 35 points', type: 'contributor', url: '/main/hr?module=participation' },
];

const getIconForResultType = (type: ResultType) => {
  switch (type) {
    case 'course':
    case 'publication':
      return <BookOpen className="w-4 h-4" />;
    case 'department':
      return <Building2 className="w-4 h-4" />;
    case 'contributor':
      return <Users className="w-4 h-4" />;
    default:
      return <FileText className="w-4 h-4" />;
  }
};

// Function to highlight search matches
const highlightSearchMatches = (text: string, searchQuery: string): React.ReactNode => {
  if (!searchQuery.trim()) return text;
  
  const parts = text.split(new RegExp(`(${searchQuery.trim()})`, 'gi'));
  return (
    <>
      {parts.map((part, i) => 
        part.toLowerCase() === searchQuery.toLowerCase() ? 
          <span key={i} className="text-blue-600 font-semibold">{part}</span> : part
      )}
    </>
  );
};

const HRnav: React.FC = () => {
  const [showNotifications, setShowNotifications] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showSearchResults, setShowSearchResults] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const { isCollapsed, toggleSidebar } = useSidebar();
  const notificationsRef = useRef<HTMLDivElement | null>(null);
  const searchRef = useRef<HTMLDivElement | null>(null);

  // Notification State
  const { data: session } = useSession();
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState<boolean>(false);
  const [notificationError, setNotificationError] = useState<string | null>(null);

  const fetchNotifications = async () => {
    if (!session?.user) return;
    
    setIsLoadingNotifications(true);
    setNotificationError(null);
    try {
      const response = await fetch('/api/notifications?limit=10');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch notifications');
      }
      const data = await response.json();
      setNotifications(data.notifications || []);
      setUnreadCount(data.unreadCount || 0);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setNotificationError(error instanceof Error ? error.message : 'Failed to load notifications');
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setIsLoadingNotifications(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      fetchNotifications();
      const interval = setInterval(fetchNotifications, 30000);
      return () => clearInterval(interval);
    }
  }, [session]);

  useEffect(() => {
    if (showNotifications && session?.user) {
      fetchNotifications();
    }
  }, [showNotifications, session]);

  const handleNotificationClick = async (notification: Notification) => {
    // Optimistically update UI
    if (!notification.read) {
      setNotifications(prevNotifications =>
        prevNotifications.map(n => 
          n.id === notification.id ? { ...n, read: true } : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    }

    setShowNotifications(false);
    router.push(notification.path);

    // Mark as read in DB
    try {
      await fetch('/api/notifications/mark-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationIds: [notification.id] }),
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Optionally revert optimistic update or re-fetch if API call fails badly
      // For now, we'll rely on the next poll or panel open to correct state if needed.
    }
  };

  // Search logic
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    // Filter mock data based on search query
    const filteredResults = mockSearchData.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setSearchResults(filteredResults);
    setShowSearchResults(true);
  }, [searchQuery]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleResultClick = (result: SearchResult) => {
    setShowSearchResults(false);
    setSearchQuery('');
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
      setShowNotifications(false);
    }
    if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
      setShowSearchResults(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`fixed top-0 right-0 ${isCollapsed ? 'w-[calc(100%-64px)]' : 'w-[calc(100%-250px)]'} transition-all duration-300 ease-in-out z-50`}>
      <div className='flex justify-between h-16 px-6 shadow-md bg-white items-center'>
        <div className='flex items-center gap-4'>
          <button
            onClick={toggleSidebar}
            className='p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors'
            aria-label="Toggle sidebar"
          >
            <Menu size={20} className='text-gray-600' />
          </button>
          <div className='relative flex items-center' ref={searchRef}>
            <div className='absolute left-3 flex items-center pointer-events-none'>
              <Search className='h-5 w-5 text-gray-400' />
            </div>
            <input 
              type="text"
              placeholder="Search for courses, departments, people..."
              className='w-[350px] h-10 pl-10 pr-4 rounded-lg bg-gray-50 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-100 text-sm'
              value={searchQuery}
              onChange={handleSearch}
            />

            {/* Search Results Dropdown */}
            {showSearchResults && searchResults.length > 0 && (
              <div className='absolute z-50 top-full left-0 mt-1 w-[350px] bg-white rounded-lg shadow-lg border border-gray-200 max-h-[400px] overflow-y-auto'>
                <div className='p-3 border-b border-gray-100'>
                  <p className='text-xs text-gray-500'>
                    {searchResults.length} results for "{searchQuery}"
                  </p>
                </div>
                <div className='divide-y divide-gray-100'>
                  {searchResults.map((result) => (
                    <div 
                      key={result.id} 
                      className='p-3 hover:bg-gray-50 cursor-pointer'
                      onClick={() => handleResultClick(result)}
                    >
                      <div className='flex items-start gap-3'>
                        <div className='mt-0.5 p-1.5 bg-blue-50 rounded-md text-color3'>
                          {getIconForResultType(result.type)}
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900'>
                            {highlightSearchMatches(result.title, searchQuery)}
                          </p>
                          <p className='text-xs text-gray-500 mt-0.5'>
                            {highlightSearchMatches(result.subtitle, searchQuery)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* No Results Message */}
            {showSearchResults && searchQuery.trim() !== '' && searchResults.length === 0 && (
              <div className='absolute z-50 top-full left-0 mt-1 w-[350px] bg-white rounded-lg shadow-lg border border-gray-200'>
                <div className='p-6 flex flex-col items-center justify-center'>
                  <Search className='text-gray-300 mb-2' size={24} />
                  <p className='text-gray-500 text-sm'>No results found for "{searchQuery}"</p>
                  <p className='text-gray-400 text-xs mt-1'>Try different keywords or check spelling</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className='flex items-center gap-3'>
          <h1 className='text-sm font-semibold'>HR Administration</h1>
          <div className='relative flex items-center' ref={notificationsRef}>
            <button 
              className='relative p-2 cursor-pointer hover:bg-gray-50 rounded-full transition-colors'
              onClick={() => setShowNotifications(!showNotifications)}
              aria-label="Toggle notifications"
            >
              <Megaphone className='text-color3' size={20} />
              {unreadCount > 0 && (
                <span className='absolute top-1 right-1 bg-red-500 rounded-full w-2 h-2'></span>
              )}
            </button>

            {showNotifications && (
              <div className='absolute z-[9999] right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-100 py-2'>
                <div className='flex items-center justify-between px-4 py-2 border-b border-gray-100'>
                  <h3 className='font-semibold text-sm'>Notifications</h3>
                  <button 
                    onClick={() => setShowNotifications(false)}
                    className='p-1 hover:bg-gray-50 cursor-pointer rounded-full text-gray-400 hover:text-gray-600'
                    aria-label="Close notifications"
                  >
                    <X size={16} />
                  </button>
                </div>
                <div className='max-h-[300px] overflow-y-auto'>
                  {isLoadingNotifications && notifications.length === 0 ? (
                    <div className="flex justify-center items-center py-6">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-color3"></div>
                    </div>
                  ) : notificationError ? (
                    <div className="p-4 text-center text-red-500 text-sm">
                      {notificationError}
                    </div>
                  ) : notifications.length === 0 ? (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      No notifications
                    </div>
                  ) : (
                    notifications.map((notif) => (
                      <div 
                        key={notif.id}
                        className={`px-4 py-3 hover:bg-gray-50 border-b border-gray-50 last:border-0 cursor-pointer ${
                          !notif.read ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => handleNotificationClick(notif)}
                      >
                        <div className='flex items-start gap-2'>
                          {!notif.read && (
                            <span className='w-2 h-2 rounded-full bg-blue-500 mt-[6px] flex-shrink-0'></span>
                          )}
                          <div className={notif.read ? 'opacity-70' : ''}>
                            <p className={`text-sm font-medium ${!notif.read ? 'text-gray-800' : 'text-gray-600'}`}>{notif.title}</p>
                            <p className={`text-sm ${!notif.read ? 'text-gray-700' : 'text-gray-500'} mt-0.5`}>{notif.message}</p>
                            <span className='text-xs text-gray-400 block mt-1'>{notif.time}</span>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
          <button 
            className='p-2 cursor-pointer hover:bg-gray-50 rounded-full transition-colors'
            aria-label="Logout"
            onClick={() => signOut({ callbackUrl: '/' })}
          >
            <LogOut className='text-red-500' size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default HRnav;
