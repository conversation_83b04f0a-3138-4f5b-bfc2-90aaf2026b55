import React from 'react';

const events = [
  { type: 'critical', message: 'Storage nearing capacity', time: '2m ago' },
  { type: 'warning', message: 'Failed login attempt', time: '15m ago' },
  { type: 'success', message: 'Database backup completed', time: '1h ago' },
  { type: 'info', message: 'System update available', time: '3h ago' },
  { type: 'success', message: 'Security scan completed', time: '6h ago' },
  // Add more events to simulate lots of data
  { type: 'info', message: 'User added to system', time: '7h ago' },
  { type: 'warning', message: 'Unusual network activity', time: '8h ago' },
  { type: 'critical', message: 'Firewall breach detected', time: '9h ago' },
  { type: 'success', message: 'Patch applied successfully', time: '10h ago' },
  { type: 'info', message: 'Scheduled maintenance', time: '12h ago' },
];

const typeColor: { [key: string]: string } = {
  critical: 'bg-red-500',
  warning: 'bg-yellow-400',
  success: 'bg-green-500',
  info: 'bg-blue-500',
};

const RecentEvents: React.FC = () => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 flex-1 flex flex-col h-full min-h-0">
    <div className="mb-3">
      <h2 className="text-lg font-semibold text-gray-800">Recent Events</h2>
      <p className="text-gray-500 text-xs sm:text-sm">Latest system events and alerts</p>
    </div>
    <div className="flex-1 min-h-0 overflow-auto max-h-[60vh]">
      <ul className="space-y-2">
        {events.map((event, idx) => (
          <li key={idx} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
            <div className="flex items-center gap-2 min-w-0">
              <span className={`inline-block w-2 h-2 rounded-full ${typeColor[event.type]}`}></span>
              <span className="text-sm text-gray-700 truncate" title={event.message}>{event.message}</span>
            </div>
            <span className="text-xs text-gray-400 whitespace-nowrap">{event.time}</span>
          </li>
        ))}
      </ul>
    </div>
  </div>
);

export default RecentEvents; 