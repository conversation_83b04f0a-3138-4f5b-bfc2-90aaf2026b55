import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route'; // Adjust if your authOptions path is different

export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const params = await context.params;

    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = parseInt(session.user.id, 10);
    if (isNaN(userId)) {
        return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 });
    }

    const materialId = parseInt(params.id, 10);
    if (isNaN(materialId)) {
      return NextResponse.json({ error: 'Invalid material ID format. ID must be a number.' }, { status: 400 });
    }

    const material = await prisma.trainingMaterial.findUnique({
      where: { id: materialId },
      select: {
        id: true,
        title: true,
      },
    });

    if (!material) {
      return NextResponse.json({ error: 'Training material not found' }, { status: 404 });
    }

    const participantRecord = await prisma.trainingParticipant.findUnique({
      where: {
        userId_trainingMaterialId: {
          userId: userId,
          trainingMaterialId: materialId,
        },
      },
      select: {
        certificateKey: true, 
        assessmentFormKey: true,
      },
    });

    // Based on user clarification: all participants for any material require both documents.
    const requiresCertificate = true;
    const requiresAssessmentForm = true;

    return NextResponse.json({
      id: material.id.toString(), 
      title: material.title,
      requiresCertificate: requiresCertificate,
      requiresAssessmentForm: requiresAssessmentForm,
      hasCertificate: !!participantRecord?.certificateKey,
      hasAssessmentForm: !!participantRecord?.assessmentFormKey,
    });

  } catch (error) {
    console.error('[TRAINING_MATERIAL_USER_STATUS_GET] Error:', error);
    let errorMessage = 'Failed to fetch user submission status for the training material.';
    if (error instanceof Error && process.env.NODE_ENV === 'development') {
        errorMessage = error.message;
    }
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
} 