// Training Analytics Types
export interface ChartData {
  labels: string[];
  data: number[];
}

export interface Course {
  name: string;
  enrollments: number;
  rating: number;
}

export interface SkillGap {
  skill: string;
  current: number;
  target: number;
}

export interface TrainingMetrics {
  completionRate: number;
  totalEnrollments: number;
  budgetUtilization: ChartData;
  popularCourses: Course[];
  certifications: ChartData;
  skillsGap: SkillGap[];
  monthlyProgress: ChartData;
}

// Performance Metrics Types
export interface ReviewStatus {
  completed: number;
  pending: number;
  overdue: number;
}

export interface PerformanceMetrics {
  totalReviews: number;
  averageScore: number;
  kpiAchievement: ChartData;
  reviewStatus: ReviewStatus;
  goalCompletion: ChartData;
  skillGapTrends: ChartData;
  departmentScores: ChartData;
}

// Department Distribution Types
export interface ResourceAllocation {
  department: string;
  allocated: number;
  utilized: number;
}

export interface DepartmentDistribution extends ChartData {
  colors: string[];
}

export interface DepartmentMetrics {
  headcount: ChartData;
  turnover: ChartData;
  distribution: DepartmentDistribution;
  resourceAllocation: ResourceAllocation[];
}

// Compliance Types
export interface RiskLevel {
  high: number;
  medium: number;
  low: number;
}

export interface DepartmentTrainingStatus {
  department: string;
  required: number;
  completed: number;
  pending: number;
  overdue: number;
}

export interface ComplianceMetrics {
  overallCompliance: number;
  riskLevel: RiskLevel;
  trainingStatus: DepartmentTrainingStatus[];
  complianceTrends: ChartData;
}

export interface DashboardData {
  trainingMetrics: TrainingMetrics;
  performanceMetrics: PerformanceMetrics;
  departmentMetrics: DepartmentMetrics;
  complianceMetrics: ComplianceMetrics;
} 