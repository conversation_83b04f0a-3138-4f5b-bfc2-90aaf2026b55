"use client"

import { useState } from 'react';
import StepIndicator from '@/components/ui/StepIndicator';
import OverviewStep from './Steps/overviewStep';
import ResourcesUpload from './Steps/resourcesUpload';  
import ParticipantsStep from './Steps/addParticipants';
import CertificationsStep from './Steps/certsUpload';
import SubmitStep from './Steps/submitStep';
import { FormData } from './types'; 

// Default Form Data
const initialFormData: FormData = {
  title: '',
  description: '',
  category: '',
  categoryId: undefined,
  existingCompetencyIds: [],
  existingCompetencies: [],
  newCompetencyNames: [],
  learningObjectives: [],
  modules: [],
  participants: [],
  certifications: [],
  complianceForms: []
}

export default function CreateCourse() {

  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<FormData>(initialFormData)

  // Tabs
  const steps = ["Overview", "Modules & Resources", "Participants", "Certifications & Forms", "Submit"]

  const updateFormData = (data: Partial<FormData>) => {
    setFormData((prev) => ({...prev, ...data}))
  }

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if(currentStep > 1){
      setCurrentStep(currentStep - 1)
    }
  }

  return (
      <div className="p-2">
        <div className="mb-5">
          <h1 className="text-2xl font-bold">Create Course</h1>
          <p className="text-gray-600 mt-1">Design and configure your new training course</p>
        </div>
        <div className="max-w-full mx-auto bg-white rounded-2xl shadow-sm overflow-hidden">
          <StepIndicator steps={steps} currentStep={currentStep} />
          <div className="p-8">
            {currentStep === 1 && (
              <OverviewStep 
                formData={formData} 
                updateFormData={updateFormData} 
                nextStep={nextStep}
              />
            )}

            {currentStep === 2 && (
              <ResourcesUpload
                formData={formData as any} // Type assertion to bypass type error temporarily
                updateFormData={updateFormData}
                nextStep={nextStep}
                prevStep={prevStep}
              />
            )}

            {currentStep === 3 && (
              <ParticipantsStep
                formData={formData}
                updateFormData={updateFormData}
                nextStep={nextStep}
                prevStep={prevStep}
              />
            )}

            {currentStep === 4 && (
              <CertificationsStep
                formData={formData}
                updateFormData={updateFormData}
                nextStep={nextStep}
                prevStep={prevStep}
              />
            )}

            {currentStep === 5 && (
              <SubmitStep 
                formData={formData as any} // Type assertion to bypass type error temporarily
                prevStep={prevStep}
              />
            )}
          </div>
        </div>
      </div>
  )
}
