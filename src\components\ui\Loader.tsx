import React, { useEffect, useState } from 'react';

const Loader = ({ className = '', style = {}, small = false, show = true, minVisible = 300 }) => {
  const [visible, setVisible] = useState(show);
  const [opacity, setOpacity] = useState(show ? 1 : 0);

  useEffect(() => {
    let fadeTimeout: NodeJS.Timeout | undefined;
    let minTimeout: NodeJS.Timeout | undefined;
    if (show) {
      setVisible(true);
      // Fade in
      setTimeout(() => setOpacity(1), 10);
    } else {
      // Ensure loader stays visible for at least minVisible ms
      minTimeout = setTimeout(() => {
        setOpacity(0);
        fadeTimeout = setTimeout(() => setVisible(false), 200); // match fade duration
      }, minVisible);
    }
    return () => {
      if (fadeTimeout) clearTimeout(fadeTimeout);
      if (minTimeout) clearTimeout(minTimeout);
    };
  }, [show, minVisible]);

  if (!visible) return null;

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center bg-white transition-opacity duration-200 pointer-events-auto ${className}`}
      style={{
        ...style,
        opacity,
        willChange: 'opacity',
        transition: 'opacity 0.2s cubic-bezier(0.4,0,0.2,1)',
        backfaceVisibility: 'hidden',
      }}
    >
      <svg
        className={`animate-spin ${small ? 'w-5 h-5' : 'w-12 h-12'}`}
        width={small ? 20 : 48}
        height={small ? 20 : 48}
        viewBox="0 0 48 48"
        style={{ willChange: 'transform', backfaceVisibility: 'hidden' }}
      >
        <defs>
          <linearGradient id="blue-fade" x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stopColor="#2196f3" stopOpacity="0" />
            <stop offset="20%" stopColor="#2196f3" stopOpacity="0.6" />
            <stop offset="60%" stopColor="#2196f3" stopOpacity="1" />
            <stop offset="100%" stopColor="#2196f3" stopOpacity="0" />
          </linearGradient>
        </defs>
        <circle
          cx="24"
          cy="24"
          r="20"
          fill="none"
          stroke="url(#blue-fade)"
          strokeWidth={small ? 3 : 4}
          strokeLinecap="round"
          strokeDasharray="80 60"
        />
      </svg>
    </div>
  );
};

export default Loader; 