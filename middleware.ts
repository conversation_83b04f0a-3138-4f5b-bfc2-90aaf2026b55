import { NextResponse } from 'next/server';
import { withAuth, NextRequestWithAuth } from 'next-auth/middleware';

export default withAuth(
    function middleware(request: NextRequestWithAuth) {
        const token = request.nextauth.token;
        const pathname = request.nextUrl.pathname;

        if (token) {
            if (pathname === '/main') {
                if (!token.role) {
                    return NextResponse.redirect(new URL('/unauthorized', request.url)); 
                }
                
                if (token.role === 'EMPLOYEE') {
                    return NextResponse.redirect(new URL('/main/employee/Dashboard', request.url));
                }
                if (token.role === 'HR_ADMIN') {
                    return NextResponse.redirect(new URL('/main/hr/Dashboard', request.url));
                }
                
                return NextResponse.redirect(new URL('/unauthorized', request.url)); 
            }

            if (pathname.startsWith('/main/employee/Dashboard')) {
                if (token.role === 'EMPLOYEE') {
                    return NextResponse.next(); 
                }
                if (token.role === 'HR_ADMIN') {
                    return NextResponse.redirect(new URL('/main/hr/Dashboard', request.url));
                }
                return NextResponse.redirect(new URL('/unauthorized', request.url));
            }

            if (pathname.startsWith('/main/hr/Dashboard')) {
                if (token.role === 'HR_ADMIN') {
                    return NextResponse.next(); 
                }
                return NextResponse.redirect(new URL('/unauthorized', request.url));
            }
            
            return NextResponse.next(); 
        }

        return NextResponse.next(); 
    },
    {
        callbacks: {
            authorized: ({ token, req }) => {
                const pathname = req.nextUrl.pathname;
                if (pathname.startsWith('/main')) {
                    return !!token; 
                }
                return true; 
            },
        },
        pages: {
            signIn: '/', 
        },
    }
);

export const config = {
    matcher: [
        '/main/:path*', 
    ],
};