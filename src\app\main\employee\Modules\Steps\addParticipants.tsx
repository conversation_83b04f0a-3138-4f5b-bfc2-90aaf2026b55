"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { X, UserPlus, Search, User } from "lucide-react"
import { Button } from "@/components/ui/Button"
import { FormData, Participant } from '../types';
import { useSession } from 'next-auth/react';
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from "@/components/ui/command"
import Loader from '@/components/ui/Loader';

// Define the user type returned from the API
interface UserType {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  unit?: string;
}

interface ParticipantsStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
}

export default function AddParticipants({ formData, updateFormData, nextStep, prevStep }: ParticipantsStepProps) {
  const [participants, setParticipants] = useState<Participant[]>(formData.participants || [])
  const [inputValue, setInputValue] = useState("")
  const [suggestions, setSuggestions] = useState<UserType[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const { data: session } = useSession();
  
  // Fetch users from API with debouncing
  const fetchUsers = async (searchTerm: string) => {
    if (searchTerm.length < 2) {
      setSuggestions([])
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      
      const response = await fetch(`/api/users/search?searchTerm=${encodeURIComponent(searchTerm)}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      
      const data = await response.json()
      
      // Filter out already selected participants
      const filteredUsers = data.users.filter((user: UserType) => 
        !participants.some(p => p.id === user.id)
      );
      
      setSuggestions(filteredUsers)
    } catch (err) {
      console.error('Error fetching users:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Debounce search
  useEffect(() => {
    const handler = setTimeout(() => {
      fetchUsers(inputValue)
    }, 300)

    return () => {
      clearTimeout(handler)
    }
  }, [inputValue])

  const addParticipant = (user: UserType) => {
    // Check if already added
    if (participants.some(p => p.id === user.id)) {
      return
    }

    const newParticipant: Participant = {
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
    }

    const newParticipants = [...participants, newParticipant]
    setParticipants(newParticipants)
    updateFormData({ participants: newParticipants })

    // Reset input and close dropdown
    setInputValue("")
    setOpen(false)
  }

  const removeParticipant = (idToRemove: string) => {
    const newParticipants = participants.filter((p) => p.id !== idToRemove)
    setParticipants(newParticipants)
    updateFormData({ participants: newParticipants })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    nextStep()
  }

  return (
    <form onSubmit={handleSubmit} autoComplete="off">
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-100 text-sm">3</span>
            Tag Participants
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Tag specific participants for the training session. Tagged users will receive a notification.
          </p>
        </div>
        
        <div className="bg-gray-50 p-6 rounded-lg">
          <div>
            <Label htmlFor="participant-search">Search Participants</Label>
            <div className="relative mt-1">
              <div className="relative rounded-lg border border-gray-300">
                <div className="flex items-center pl-3">
                  <Search className="h-4 w-4 text-gray-400" />
                  <input
                    id="participant-search"
                    value={inputValue}
                    onChange={(e) => {
                      setInputValue(e.target.value);
                      setOpen(e.target.value.length > 0);
                      if (e.target.value.length >= 2) {
                        fetchUsers(e.target.value);
                      }
                    }}
                    placeholder="Search by name or email..."
                    className="w-full py-2 px-2 outline-none border-none text-base"
                  />
                </div>
                
                {open && (
                  <div className="absolute top-full left-0 w-full z-10 mt-1 bg-white rounded-lg border border-gray-300 shadow-lg max-h-72 overflow-y-auto">
                    {isLoading ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader className="h-4 w-4" />
                      </div>
                    ) : inputValue.length > 0 ? (
                      suggestions.length > 0 ? (
                        <div className="py-1">
                          {suggestions.map((user) => (
                            <div
                              key={user.id}
                              onClick={() => addParticipant(user)}
                              className="flex items-center gap-3 px-4 py-2 cursor-pointer hover:bg-gray-50"
                            >
                              <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                                <User className="h-5 w-5 text-gray-500" />
                              </div>
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">{user.firstName} {user.lastName}</p>
                                <div className="flex text-xs text-gray-500">
                                  <span>{user.email}</span>
                                  {user.unit && (
                                    <>
                                      <span className="mx-1">•</span>
                                      <span>{user.unit}</span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="py-6 text-center text-sm text-gray-500">
                          No users found
                        </div>
                      )
                    ) : null}
                  </div>
                )}
              </div>
            </div>
            <p className="mt-2 text-xs text-gray-500">
              Search by name or email. Select a user to add them to the list of participants.
            </p>
          </div>
        </div>

        {participants.length > 0 ? (
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-4">Tagged Participants</h3>
            <ul className="space-y-2">
              {participants.map((participant) => (
                <li key={participant.id} className="flex items-center justify-between p-4 bg-white rounded-md">
                  <div>
                    <p className="font-medium">{participant.name}</p>
                    <p className="text-sm text-gray-500">{participant.email}</p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeParticipant(participant.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="bg-gray-50 p-6 rounded-lg flex flex-col items-center justify-center text-center">
            <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
              <UserPlus className="w-6 h-6 text-indigo-500" />
            </div>
            <h3 className="text-gray-700 font-medium mb-1">No participants tagged yet</h3>
            <p className="text-sm text-gray-500">
              Search and select users to tag them as participants for this training.
            </p>
          </div>
        )}

        <div className="flex justify-between">
          <Button className="border cursor-pointer border-gray-300 px-6 py-2 rounded-md hover:bg-gray-50" variant="outline" onClick={prevStep}>
            Back
          </Button>
          <Button className="bg-color3 cursor-pointer text-white px-6 py-2 rounded-md hover:bg-color3/95" type="submit">
            Continue
          </Button>
        </div>
      </div>
    </form>
  )
}
