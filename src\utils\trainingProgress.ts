/**
 * Utility functions for tracking overall training progress
 */

/**
 * Start tracking a user's progress on a training material
 */
export const startTrainingProgress = async (courseId: string | number): Promise<boolean> => {
  try {
    const response = await fetch('/api/progress/training', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'start',
        trainingMaterialId: courseId,
        progressPercentage: 0
      }),
    });

    if (!response.ok) {
      console.error('Failed to start training progress:', await response.json());
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error starting training progress:', error);
    return false;
  }
};

/**
 * Update a user's progress on a training material
 */
export const updateTrainingProgress = async (
  courseId: string | number, 
  progressPercentage: number
): Promise<boolean> => {
  try {
    const response = await fetch('/api/progress/training', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'update',
        trainingMaterialId: courseId,
        progressPercentage
      }),
    });

    if (!response.ok) {
      console.error('Failed to update training progress:', await response.json());
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating training progress:', error);
    return false;
  }
};

/**
 * Mark a training material as completed
 */
export const completeTrainingProgress = async (courseId: string | number): Promise<boolean> => {
  try {
    const response = await fetch('/api/progress/training', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'complete',
        trainingMaterialId: courseId
      }),
    });

    if (!response.ok) {
      console.error('Failed to complete training progress:', await response.json());
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error completing training progress:', error);
    return false;
  }
}; 