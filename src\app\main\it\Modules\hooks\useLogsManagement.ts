import { useEffect, useState } from 'react';
import { Log, LogsFilter, logsService } from '../services/logsService';

interface UseLogsManagementProps {
  itemsPerPage: number;
  searchQuery: string;
  eventTypeFilter: string;
  startDate: string | null | undefined;
  endDate: string | null | undefined;
  statusFilter?: string;
}

interface UseLogsManagementReturn {
  logs: Log[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  isLoading: boolean;
  error: Error | null;
  refreshLogs: () => Promise<void>;
  exportLogs: () => Promise<void>;
}

export const useLogsManagement = ({
  itemsPerPage,
  searchQuery,
  eventTypeFilter,
  startDate,
  endDate,
  statusFilter,
}: UseLogsManagementProps): UseLogsManagementReturn => {
  const [currentPage, setCurrentPage] = useState(1);
  const [logs, setLogs] = useState<Log[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchLogs = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const filters: LogsFilter = {
        page: currentPage,
        itemsPerPage,
        searchQuery,
        eventType: eventTypeFilter !== 'All Events' ? eventTypeFilter : undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        status: statusFilter,
      };

      const { logs: fetchedLogs, total } = await logsService.getLogs(filters);
      
      setLogs(fetchedLogs);
      setTotalItems(total);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch logs'));
    } finally {
      setIsLoading(false);
    }
  };

  const refreshLogs = async () => {
    await fetchLogs();
  };

  const exportLogs = async () => {
    try {
      setIsLoading(true);
      const filters: LogsFilter = {
        searchQuery,
        eventType: eventTypeFilter !== 'All Events' ? eventTypeFilter : undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        status: statusFilter,
      };

      const blob = await logsService.exportLogs(filters);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to export logs'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [currentPage, itemsPerPage, searchQuery, eventTypeFilter, startDate, endDate, statusFilter]);

  const totalPages = Math.ceil(totalItems / itemsPerPage);

  return {
    logs,
    totalItems,
    totalPages,
    currentPage,
    setCurrentPage,
    isLoading,
    error,
    refreshLogs,
    exportLogs,
  };
}; 