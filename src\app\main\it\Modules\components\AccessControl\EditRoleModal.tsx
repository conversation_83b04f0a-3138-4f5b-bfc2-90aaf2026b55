import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/modal';
import { Role } from './RoleItem';

interface EditRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  role: Role | null;
  onSave: (updatedRole: Role) => void;
}

interface PermissionSection {
  title: string;
  permissions: string[];
}

const EditRoleModal: React.FC<EditRoleModalProps> = ({
  isOpen,
  onClose,
  role,
  onSave,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    selectedPermissions: new Set<string>(),
  });

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description,
        selectedPermissions: new Set<string>(),
      });
    } else {
      setFormData({
        name: '',
        description: '',
        selectedPermissions: new Set<string>(),
      });
    }
  }, [role]);

  const permissionSections: PermissionSection[] = [
    {
      title: 'Content Management',
      permissions: [
        'View Content',
        'Create Content',
        'Edit Content',
        'Delete Content',
        'Approve Content',
      ],
    },
    {
      title: 'User Management',
      permissions: [
        'View Users',
        'Create Users',
        'Edit Users',
        'Delete Users',
        'Manage Roles',
      ],
    },
    {
      title: 'System Administration',
      permissions: [
        'View System Settings',
        'Edit System Settings',
        'View Logs',
        'Manage Backups',
        'Manage Security',
      ],
    },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePermissionToggle = (permission: string) => {
    setFormData(prev => {
      const newPermissions = new Set(prev.selectedPermissions);
      if (newPermissions.has(permission)) {
        newPermissions.delete(permission);
      } else {
        newPermissions.add(permission);
      }
      return {
        ...prev,
        selectedPermissions: newPermissions,
      };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (role) {
      onSave({
        ...role,
        name: formData.name,
        description: formData.description,
      });
    }
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={role ? "Edit Role" : "Add New Role"} size="large">
      <form onSubmit={handleSubmit} className="flex flex-col h-[calc(100vh-200px)] md:h-auto md:max-h-[calc(100vh-200px)]">
        <div className="flex-1 overflow-y-auto px-1">
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-color3 focus:border-color3"
                  required
                  placeholder="Enter role name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-color3 focus:border-color3"
                  placeholder="Enter role description"
                />
              </div>
            </div>

            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">
                Permissions <span className="text-red-500">*</span>
              </label>
              <div className="space-y-6">
                {permissionSections.map((section) => (
                  <div 
                    key={section.title}
                    className="bg-gray-50 rounded-lg p-4 space-y-3"
                  >
                    <h3 className="text-sm font-medium text-gray-900">
                      {section.title}
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {section.permissions.map((permission) => (
                        <label
                          key={permission}
                          className="relative flex items-start py-2 px-3 rounded-md hover:bg-white cursor-pointer group"
                        >
                          <div className="min-w-0 flex-1 text-sm">
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={formData.selectedPermissions.has(permission)}
                                onChange={() => handlePermissionToggle(permission)}
                                className="h-4 w-4 rounded border-gray-300 text-color3 focus:ring-color3"
                              />
                              <span className="font-medium text-gray-700 group-hover:text-gray-900">
                                {permission}
                              </span>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="flex-shrink-0 px-1">
          <div className="border-t border-gray-200 mt-6 pt-4">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-color3 border border-transparent rounded-md hover:bg-color3/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3"
              >
                {role ? 'Save Changes' : 'Create Role'}
              </button>
            </div>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export default EditRoleModal; 