'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, Funnel, Calendar, ChevronDown, FileText, Clock, CheckCircle2, Archive } from 'lucide-react';
import { stats as staticStats } from './data/config';
import { PendingApproval } from './utils/types';
import { StatCard } from '@/components/shared/StatCard';
import TimeFilter from './components/TimeFilter';
import ApprovalsTable from './components/ApprovalsTable';
import CertificationsTable from './components/CertificationsTable';

const ApprovalsModule = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'publications' | 'certifications'>('publications');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest'>('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const [timeFilter, setTimeFilter] = useState('This Month');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [materials, setMaterials] = useState<PendingApproval[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    hasNextPage: false,
    hasPrevPage: false
  });

  const [pendingCount, setPendingCount] = useState<number | null>(null);
  const [publishedCount, setPublishedCount] = useState<number | null>(null);
  const [draftCount, setDraftCount] = useState<number | null>(null);
  const [archivedCount, setArchivedCount] = useState<number | null>(null);
  const [loadingStats, setLoadingStats] = useState(true);

  const filterRef = useRef<HTMLDivElement>(null);
  const itemsPerPage = 5;

  // Fetch materials when filters change
  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: itemsPerPage.toString(),
          search: searchQuery,
          status: selectedStatus,
          sortBy: sortBy
        });

        // Use correct endpoint per tab
        const endpoint = activeTab === 'certifications'
          ? `/api/certifications/list?${params}`
          : `/api/training-material/list?${params}`;
        const response = await fetch(endpoint);
        if (!response.ok) {
          throw new Error('Failed to fetch training materials');
        }

        const data = await response.json();
        setMaterials(data.materials);
        setPagination(data.pagination);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Something went wrong');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMaterials();
  }, [currentPage, searchQuery, selectedStatus, sortBy]);

  useEffect(() => {
    const fetchStatCounts = async () => {
      setLoadingStats(true);
      try {
        const statusesToFetch: { status: string, setter: React.Dispatch<React.SetStateAction<number | null>> }[] = [
          { status: 'PENDING_APPROVAL', setter: setPendingCount },
          { status: 'PUBLISHED', setter: setPublishedCount },
          { status: 'DRAFT', setter: setDraftCount },
          { status: 'ARCHIVED', setter: setArchivedCount },
        ];

        const countsPromises = statusesToFetch.map(async (item) => {
          const params = new URLSearchParams({
            limit: '1', // We only need totalItems
            status: item.status,
          });
          const response = await fetch(`/api/training-material/list?${params}`);
          if (!response.ok) {
            console.error(`Failed to fetch count for ${item.status}`);
            return { status: item.status, count: 0 }; // Default to 0 on error
          }
          const data = await response.json();
          return { status: item.status, count: data.pagination.totalItems };
        });

        const results = await Promise.all(countsPromises);

        results.forEach(result => {
          if (result.status === 'PENDING_APPROVAL') setPendingCount(result.count);
          else if (result.status === 'PUBLISHED') setPublishedCount(result.count);
          else if (result.status === 'DRAFT') setDraftCount(result.count);
          else if (result.status === 'ARCHIVED') setArchivedCount(result.count);
        });

      } catch (err) {
        console.error("Error fetching stat counts:", err);
        // Set all counts to 0 or some error indicator if preferred
        setPendingCount(0);
        setPublishedCount(0);
        setDraftCount(0);
        setArchivedCount(0);
      } finally {
        setLoadingStats(false);
      }
    };

    fetchStatCounts();
  }, []); // Runs once on mount

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedStatus, sortBy, activeTab]);

  const handleFilterChange = (filter: string) => {
    setTimeFilter(filter);
    setIsFilterOpen(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
      setIsFilterOpen(false);
    }
  };

  const handleReviewClick = (item: PendingApproval) => {
    router.push(`/main/hr/review/${item.id}`);
  };

  // Event listeners
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter materials based on active tab
  // Reverted: original filter logic
  const filteredMaterials = activeTab === 'publications'
    ? materials.filter(item => !['Quality Assurance', 'Safety', 'HR', 'Finance'].includes(item.category))
    : materials;

  const displayStats = staticStats.map(stat => {
    let liveValue = '...'; // Default loading value
    if (!loadingStats) {
      switch (stat.title) {
        case 'Pending Approvals':
          liveValue = pendingCount !== null ? pendingCount.toString() : '0';
          break;
        case 'Published':
          liveValue = publishedCount !== null ? publishedCount.toString() : '0';
          break;
        case 'Draft':
          liveValue = draftCount !== null ? draftCount.toString() : '0';
          break;
        case 'Archived':
          liveValue = archivedCount !== null ? archivedCount.toString() : '0';
          break;
        default:
          liveValue = '0';
      }
    }
    return {
      ...stat, // Spread the static properties (icon, color, subtitle, trend, progress)
      value: liveValue, // Override with the live count
    };
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-color4">Publication Approvals</h1>
          <p className='text-sm text-gray-500'>Review and approve training content before publication</p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {displayStats.map((stat, index) => (
          <StatCard 
            key={index} 
            title={stat.title} 
            value={stat.value.toString()} 
            icon={stat.icon} 
            color={stat.color} 
            subtitle={stat.subtitle} 
            trend={stat.trend}
            progress={stat.progress}
          />
        ))}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('publications')}
            className={`${
              activeTab === 'publications'
                ? 'border-color3 text-color3'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 cursor-pointer px-1 border-b-2 font-medium text-sm`}
          >
            Publications
          </button>
          <button
            onClick={() => setActiveTab('certifications')}
            className={`${
              activeTab === 'certifications'
                ? 'border-color3 text-color3'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 cursor-pointer px-1 border-b-2 font-medium text-sm`}
          >
            Certifications & Forms
          </button>
        </nav>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="flex-1 w-full">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder={`Search ${activeTab === 'publications' ? 'publications' : 'certifications and forms'}...`}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex gap-2">
          <div className="relative">
            <Funnel size={15} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="appearance-none cursor-pointer pl-9 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white text-sm text-gray-600"
            >
              <option value="all" className="cursor-pointer">All Status</option>
              <option value="PENDING_APPROVAL" className="cursor-pointer">Pending</option>
              <option value="PUBLISHED" className="cursor-pointer">Published</option>
              <option value="DRAFT" className="cursor-pointer">Draft</option>
              <option value="ARCHIVED" className="cursor-pointer">Archived</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={15} />
          </div>

          <div className="relative">
            <Calendar size={15} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest')}
              className="appearance-none cursor-pointer pl-9 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white text-sm text-gray-600"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={15} />
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-color3"></div>
        </div>
      ) : (
        /* Table */
        activeTab === 'publications' ? (
          <ApprovalsTable 
            currentItems={filteredMaterials}
            searchQuery={searchQuery}
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            indexOfFirstItem={(pagination.currentPage - 1) * itemsPerPage + 1}
            indexOfLastItem={Math.min(pagination.currentPage * itemsPerPage, pagination.totalItems)}
            handlePageChange={setCurrentPage}
            onReview={handleReviewClick}
          />
        ) : (
          <CertificationsTable 
            currentItems={filteredMaterials}
            searchQuery={searchQuery}
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            indexOfFirstItem={(pagination.currentPage - 1) * itemsPerPage + 1}
            indexOfLastItem={Math.min(pagination.currentPage * itemsPerPage, pagination.totalItems)}
            handlePageChange={setCurrentPage}
            onReview={handleReviewClick}
          />
        )
      )}
    </div>
  );
};

export default ApprovalsModule;