import React, { useState, useRef, useEffect } from 'react';
import { Edit2, UserX } from 'lucide-react';

interface UserDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDeactivate: () => void;
  buttonRef: React.RefObject<HTMLButtonElement | null>;
}

const UserDropdown: React.FC<UserDropdownProps> = ({
  isOpen,
  onClose,
  onEdit,
  onDeactivate,
  buttonRef
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    const updatePosition = () => {
      if (buttonRef.current && isOpen) {
        const rect = buttonRef.current.getBoundingClientRect();
        setPosition({
          top: rect.bottom,
          left: Math.max(0, rect.left - 200 + rect.width), // Prevent going off-screen left
        });
      }
    };

    updatePosition();
    window.addEventListener('scroll', updatePosition);
    window.addEventListener('resize', updatePosition);

    return () => {
      window.removeEventListener('scroll', updatePosition);
      window.removeEventListener('resize', updatePosition);
    };
  }, [isOpen, buttonRef]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, buttonRef]);

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      style={{
        position: 'fixed',
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
      className="z-[9999]"
    >
      <div className="w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-1">
        <button
          onClick={onEdit}
          className="w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2 cursor-pointer"
        >
          <Edit2 size={16} />
          <span>Edit User</span>
        </button>
        <button
          onClick={onDeactivate}
          className="w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-50 flex items-center gap-2 cursor-pointer"
        >
          <UserX size={16} />
          <span>Deactivate User</span>
        </button>
      </div>
    </div>
  );
};

export default UserDropdown; 