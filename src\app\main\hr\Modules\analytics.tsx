'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ClipboardMinus, Calendar, ChevronDown } from 'lucide-react';
import TabNavigation from './components/TabNavigation';
import CourseAnalytics from './components/CourseAnalytics';
import CompetencyRadar from './components/CompetencyRadar';
import TrainingAnalytics from './components/TrainingAnalytics';
import { convertToCSV, downloadCSV } from '@/lib/csvUtils';

const AnalyticsModule = () => {
  const router = useRouter();
  const [timeFilter, setTimeFilter] = useState('This Week');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const filterRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState('competencies');
  const [selectedDivision, setSelectedDivision] = useState('All Divisions');

  // Refs for analytics components
  const competencyRef = useRef<any>(null);
  const courseRef = useRef<any>(null);
  const trainingRef = useRef<any>(null);

  const divisions = [
    'All Divisions',
    'Office of the Regional Director',
    'Financial and Administrative Services',
    'Technical Operations'
  ];

  const tabs = [
    { id: 'competencies', label: 'Competencies' },
    { id: 'course', label: 'Course Analytics' },
    { id: 'training', label: 'Training Analytics' }
  ];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsFilterOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleFilterChange = (filter: string) => {
    setTimeFilter(filter);
    setIsFilterOpen(false);
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      let exportData;
      let filename;
      const timestamp = new Date().toISOString().split('T')[0];

      switch (activeTab) {
        case 'competencies':
          if (competencyRef.current?.getExportData) {
            exportData = await competencyRef.current.getExportData();
            filename = `competency-report-${timestamp}.csv`;
          }
          break;
        case 'course':
          if (courseRef.current?.getExportData) {
            exportData = await courseRef.current.getExportData();
            filename = `course-analytics-${timestamp}.csv`;
          }
          break;
        case 'training':
          if (trainingRef.current?.getExportData) {
            exportData = await trainingRef.current.getExportData();
            filename = `training-analytics-${timestamp}.csv`;
          }
          break;
      }

      if (exportData && filename) {
        const csvString = convertToCSV(exportData);
        downloadCSV(csvString, filename);
      }
    } catch (error) {
      console.error('Export failed:', error);
      // You might want to show a toast or alert here
    } finally {
      setIsExporting(false);
    }
  };
  
  const renderContent = () => {
    switch (activeTab) {
      case 'competencies':
        return (
          <div className="w-full">
            <CompetencyRadar ref={competencyRef} timeframe={timeFilter} />
          </div>
        );
      case 'course':
        return <CourseAnalytics ref={courseRef} timeframe={timeFilter} />;
      case 'training':
        return <TrainingAnalytics ref={trainingRef} timeframe={timeFilter} />;
      default:
        return null;
    }
  };

  return (
    <div className="max-h-screen">
      <div className='flex flex-col md:flex-row justify-between items-start md:items-center mb-4'>
        <div>
          <h1 className='text-xl md:text-2xl font-semibold text-gray-800'>Reports & Analytics</h1>
          <p className='text-xs md:text-sm text-gray-500 mt-1'>View training metrics and performance analytics</p>
        </div>
        <div className='flex gap-2 mt-2 md:mt-0'>
          <div className='relative' ref={filterRef}>
            <button 
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className='flex gap-2 items-center text-sm rounded-md bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-3 py-2'
            >
              <Calendar size={16} />
              <span>{timeFilter}</span>
              <ChevronDown size={14} />
            </button>
            {isFilterOpen && (
              <div className='absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10'>
                <ul className='py-1'>
                  {['This Week', 'This Month', 'This Quarter', 'This Year'].map((filter) => (
                    <li key={filter}>
                      <button
                        onClick={() => handleFilterChange(filter)}
                        className={`block w-full text-left px-4 py-2 text-sm ${timeFilter === filter ? 'bg-gray-100 text-color3' : 'hover:bg-gray-50'}`}
                      >
                        {filter}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          <button 
            onClick={handleExport}
            disabled={isExporting}
            className='flex gap-2 items-center text-xs md:text-sm rounded-md bg-color3 cursor-pointer duration-200 hover:bg-color3/90 p-2 text-white disabled:opacity-50 disabled:cursor-not-allowed'
          >
            <ClipboardMinus size={16} />
            <span>{isExporting ? 'Exporting...' : 'Export Report'}</span>
          </button>
        </div>
      </div>

      <TabNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
        tabs={tabs}
      />
    
      {renderContent()}
    </div>
  );
};

export default AnalyticsModule; 