"use client"

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PlusCircle, CheckCircle, Search } from "lucide-react";
import { toast } from "sonner";
import { debounce, DebouncedFunc } from "lodash";
import { CompetencySuggestion } from '../types';
import { FiX } from "react-icons/fi";

interface CompetencyInputProps {
  selectedTags: CompetencySuggestion[];
  onTagAdd: (tag: CompetencySuggestion) => void;
  onTagRemove: (tagToRemove: string) => void;
  label?: string;
  placeholder?: string;
  description?: string;
  error?: string;
}

const sanitizeCompetencyName = (name: string): string => {
  return name.toLowerCase().replace(/[^\w\s]/g, '').trim();
};

const CompetencyInput: React.FC<CompetencyInputProps> = React.memo(({
  selectedTags,
  onTagAdd,
  onTagRemove,
  label = "Competencies",
  placeholder = "Type to search or add competencies...",
  description = "Select existing or type a new competency.",
  error
}: CompetencyInputProps) => {
  const [tagInput, setTagInput] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [suggestedTags, setSuggestedTags] = useState<CompetencySuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const tagInputRef = useRef<HTMLDivElement>(null);
  const searchCompetenciesRef = useRef<DebouncedFunc<(query: string) => Promise<void>> | null>(null);

  // Effect for handling clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tagInputRef.current && !tagInputRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Initialize the debounced search function
  useEffect(() => {
    searchCompetenciesRef.current = debounce(async (query: string) => {
      if (query.length < 2) {
        setSuggestedTags([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const sanitizedQuery = sanitizeCompetencyName(query);
        // Replace with your actual API endpoint
        const response = await fetch(`/api/competencies/search?query=${encodeURIComponent(sanitizedQuery)}`); 
        if (!response.ok) throw new Error('Failed to fetch competencies');

        const data = await response.json();

        // Use the FetchedCompetency interface here
        const suggestions: CompetencySuggestion[] = data.competencies.map((comp: CompetencySuggestion) => ({ 
          id: comp.id,
          name: comp.name,
          sanitizedName: comp.sanitizedName || sanitizeCompetencyName(comp.name), // Ensure sanitizedName exists
          similarity: comp.similarity,
          isExisting: true
        }));

        // Add 'new' suggestion if query is not an exact match
        if (query.trim() && !data.exactMatch) {
          suggestions.push({
            id: 'new',
            name: query.trim(),
            sanitizedName: sanitizeCompetencyName(query.trim()),
            isExisting: false,
            isNew: true
          });
        }

        setSuggestedTags(suggestions);
      } catch (error) {
        console.error('Error searching competencies:', error);
        toast.error('Failed to search competencies');
        setSuggestedTags([]); 
      } finally {
        setIsLoading(false);
      }
    }, 300);

    // Cleanup debounced function
    return () => {
      searchCompetenciesRef.current?.cancel();
    };
  }, []); // Empty dependency array ensures this runs only once

  const handleInputFocus = () => {
    setIsDropdownOpen(true);
    // Trigger search immediately if there's already text
    if (tagInput.length >= 2) {
        searchCompetenciesRef.current?.(tagInput);
    }
  };

  // Only call onTagAdd when a tag is selected or added, not on every keystroke
  const handleTagInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTagInput(value);
    setIsDropdownOpen(true);
    searchCompetenciesRef.current?.(value);
  }, []);

  const handleTagSelect = useCallback((tag: CompetencySuggestion) => {
      const competencyName = tag.name;
      const sanitizedSelectedName = sanitizeCompetencyName(competencyName);

      // Check against names in the selectedTags array of objects
      if (selectedTags.some(t => sanitizeCompetencyName(t.name) === sanitizedSelectedName)) { 
          toast.warning(`Competency "${competencyName}" is already selected`);
          setTagInput('');
          setSuggestedTags([]);
          setIsDropdownOpen(false);
          return;
      }

      onTagAdd(tag); // Pass the full object to the parent

      // Optionally provide feedback if it's a new tag
      if (tag.isNew) {
          toast.info(`New competency "${competencyName}" added. It will be submitted for review later.`);
      }

      // Reset input and close dropdown
      setTagInput('');
      setSuggestedTags([]);
      setIsDropdownOpen(false);
  }, [selectedTags, onTagAdd]); // Add onTagAdd to dependencies

  // Add a handler for Enter key to add the tag
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsDropdownOpen(false);
    }
    if (e.key === 'Enter' && tagInput.trim() && !isLoading) {
      e.preventDefault();
      // Create a 'new' tag object and select it
      onTagAdd({
        id: 'new',
        name: tagInput.trim(),
        sanitizedName: sanitizeCompetencyName(tagInput.trim()),
        isExisting: false,
        isNew: true
      });
      setTagInput('');
      setSuggestedTags([]);
      setIsDropdownOpen(false);
    }
  };

  return (
    <div className="space-y-3 relative" ref={tagInputRef}>
      <Label className="block">{label}</Label>
      <div className="flex flex-wrap gap-2">
        <Input
          type="text"
          value={tagInput}
          onChange={handleTagInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`w-full bg-white py-2 ${error ? 'border-red-500' : ''}`}
          aria-autocomplete="list"
          aria-controls="competency-suggestions"
        />
        <div>
          <p className="ml-2 text-xs text-gray-500">{description}</p>
        </div>
      </div>
      <div className={`flex flex-wrap gap-2 min-h-[40px] border ${error ? 'border-red-500' : 'border-gray-200'} rounded-md p-2 bg-gray-50`}>
        {selectedTags.length === 0 && <span className="text-sm text-gray-400">No competencies added yet.</span>}
        {selectedTags.map((tag) => (
          <div key={tag.name} className="flex items-center bg-color3 rounded-full text-sm text-white px-3 py-1">
            <span>{tag.name}</span>
            <button
              type="button"
              onClick={() => onTagRemove(tag.name)}
              className="ml-2 text-white hover:text-gray-300"
            >
              <FiX size={16} />
            </button>
          </div>
        ))}
      </div>
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}

      {/* Suggestions Dropdown */}
      {isDropdownOpen && (isLoading || suggestedTags.length > 0) && (
        <div id="competency-suggestions" className="absolute z-20 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto" role="listbox">
          {suggestedTags.map((tag, index) => (
            <div
              key={tag.id || index}
              role="option"
              aria-selected="false"
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
              onClick={() => handleTagSelect(tag)}
            >
              <div className="flex items-center">
                {tag.isNew ? (
                  <PlusCircle className="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" />
                ) : tag.similarity === 100 ? (
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                ) : (
                  <Search className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                )}
                <span className="truncate">
                  {tag.isNew ? (
                    <>Add new: <span className="font-semibold">&ldquo;{tag.name}&rdquo;</span></>
                  ) : (
                    tag.name
                  )}
                </span>
              </div>
              {typeof tag.similarity === 'number' && tag.similarity < 100 && !tag.isNew && (
                <span className="text-xs text-gray-400 ml-2 flex-shrink-0">{tag.similarity}% match</span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
});

export default CompetencyInput; 