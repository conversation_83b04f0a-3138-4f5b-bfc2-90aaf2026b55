'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/Button';
import FileUploader from '@/components/ui/FileUploader';
import FileList from '@/components/ui/FileList';
import { toast, Toaster } from 'sonner';
import { AlertCircle, UploadCloud } from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/Alert";
import { Label } from "@/components/ui/label";



interface TrainingMaterialDetails {
  id: string;
  title: string;
  requiresCertificate: boolean;
  requiresAssessmentForm: boolean;
  // Fields to indicate what the user has already submitted for this material
  hasCertificate?: boolean;
  hasAssessmentForm?: boolean;
}

// TODO: Define based on CertificationsStep or FileUploader needs
interface UploadedFile {
  file: File;
  // any other relevant properties
}

export default function SubmitMissingDocumentsPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const materialId = params.materialId as string;

  // Function to get the appropriate dashboard URL based on user role
  const getDashboardUrl = () => {
    if (session?.user?.role === 'HR_ADMIN') {
      return '/main/hr/Dashboard?module=approvals';
    } else if (session?.user?.role === 'EMPLOYEE') {
      return '/main/employee/Dashboard?module=knowledge';
    } else {
      // Default fallback
      return '/main';
    }
  };

  const [materialDetails, setMaterialDetails] = useState<TrainingMaterialDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [certificateFile, setCertificateFile] = useState<File[]>([]);
  const [assessmentFormFile, setAssessmentFormFile] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // --- TODO: Constants from CertificationsStep ---
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_CERT_TYPES = ['.pdf', '.jpg', '.jpeg', '.png'];
  const ALLOWED_FORM_TYPES = ['.pdf', '.docx'];
  // --- End Constants ---

  const fetchDetails = useCallback(async () => {
    if (!materialId) return;
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/training-material/${materialId}/user-submission-status`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to fetch material details' }));
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }
      const data = await response.json();
      setMaterialDetails(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred while fetching details.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [materialId]);

  useEffect(() => {
    fetchDetails();
  }, [fetchDetails]);

  const isCertificateMissing = !!(materialDetails && materialDetails.requiresCertificate && !materialDetails.hasCertificate);
  const isAssessmentFormMissing = !!(materialDetails && materialDetails.requiresAssessmentForm && !materialDetails.hasAssessmentForm);

  const handleFileUpload = (files: File[], type: 'certificate' | 'assessmentForm') => {
    if (files.length > 1) {
        toast.error(`Please upload only one ${type === 'certificate' ? 'certificate' : 'assessment form'} file.`);
        return false;
    }
    const file = files[0];
    if (file.size > MAX_FILE_SIZE) {
        toast.error(`File "${file.name}" exceeds the maximum size of 10MB`);
        return false;
    }
    const allowedExt = type === 'certificate' ? ALLOWED_CERT_TYPES : ALLOWED_FORM_TYPES;
    const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!fileExt || !allowedExt.includes(fileExt)) {
         toast.error(`File "${file.name}" is not an allowed file type. Allowed: ${allowedExt.join(', ')}`);
        return false;
    }

    if (type === 'certificate') {
      setCertificateFile([file]);
      toast.success("Certificate selected: " + file.name);
    } else {
      setAssessmentFormFile([file]);
      toast.success("Assessment form selected: " + file.name);
    }
    return true;
  };

  const removeFile = (type: 'certificate' | 'assessmentForm') => {
    if (type === 'certificate') {
      setCertificateFile([]);
      toast.info("Certificate selection removed");
    } else {
      setAssessmentFormFile([]);
      toast.info("Assessment form selection removed");
    }
  };

  const handleSubmit = async () => {
    let submittedSomething = false;

    if (!isCertificateMissing && !isAssessmentFormMissing) {
      toast.info("No documents are currently marked as missing.");
      return;
    }

    if ((isCertificateMissing && certificateFile.length === 0) && (isAssessmentFormMissing && assessmentFormFile.length === 0)) {
      toast.error('Please select file(s) for the missing document(s) to upload.');
      return;
    }

    setIsSubmitting(true);

    try {
      if (isCertificateMissing && certificateFile.length > 0) {
        const certFormData = new FormData();
        certFormData.append('materialId', materialId);
        certFormData.append('file', certificateFile[0]);
        certFormData.append('type', 'certificate');

        const response = await fetch('/api/training-material/submit-document', {
          method: 'POST',
          body: certFormData,
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Certificate upload failed: ${response.statusText}`);
        }
        toast.success('Certificate uploaded successfully!');
        setCertificateFile([]);
        submittedSomething = true;
      }

      if (isAssessmentFormMissing && assessmentFormFile.length > 0) {
        const assessFormData = new FormData();
        assessFormData.append('materialId', materialId);
        assessFormData.append('file', assessmentFormFile[0]);
        assessFormData.append('type', 'assessmentForm');

        const response = await fetch('/api/training-material/submit-document', {
          method: 'POST',
          body: assessFormData,
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Assessment form upload failed: ${response.statusText}`);
        }
        toast.success('Assessment form uploaded successfully!');
        setAssessmentFormFile([]);
        submittedSomething = true;
      }

      if (submittedSomething) {
        await fetchDetails();
        toast.success("Redirecting to dashboard...");
        router.push(getDashboardUrl());
      } else {
        toast.info("No new files were selected for upload.");
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Submission failed.';
      toast.error(errorMessage);
      console.error("Submission error: ", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-8 flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        <p className="ml-4 text-lg">Loading material details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
         <Button onClick={fetchDetails} className="mt-4">Try Again</Button>
      </div>
    );
  }

  if (!materialDetails) {
    return <div className="p-8 text-center text-gray-500">Material details not found. Please try again.</div>;
  }

  const noMissingDocuments = !isCertificateMissing && !isAssessmentFormMissing;

  return (
    <div className="p-4 md:p-8 max-w-4xl mx-auto">
      <Toaster position="top-right" richColors />
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Submit Missing Documents</h1>
        <p className="text-gray-600 mt-1">
          For Training Material: <span className="font-semibold">{materialDetails.title}</span>
        </p>
      </div>

      {noMissingDocuments && (
         <Alert className="bg-green-50 border-green-200 text-green-800">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              All required documents for this training material appear to be submitted according to our records.
            </AlertDescription>
            <Button
              onClick={() => router.push(getDashboardUrl())}
              className="mt-3 bg-color3 hover:bg-color3/90 text-white text-sm py-1 px-3"
            >
              Go to Dashboard
            </Button>
          </Alert>
      )}

      <div className="space-y-8 mt-6">
        {isCertificateMissing && materialDetails.requiresCertificate && (
          <div className="p-6 border border-gray-200 rounded-lg shadow-sm bg-white">
            <Label htmlFor="certificate-upload" className="text-lg font-semibold text-gray-700">
              Upload Certificate (Required)
            </Label>
            <p className="text-sm text-gray-500 mb-1">
              Status: <span className="font-medium text-red-600">Missing</span>
            </p>
            <p className="text-sm text-gray-500 mb-3">
              Allowed formats: {ALLOWED_CERT_TYPES.join(', ')}. Max size: 10MB.
            </p>
            <FileUploader
              id="certificate-upload"
              onFilesSelected={(files) => handleFileUpload(files, 'certificate')}
              accept={ALLOWED_CERT_TYPES.join(',')}
            />
            {certificateFile.length > 0 && (
              <div className="mt-4">
                <FileList files={certificateFile} onRemove={() => removeFile('certificate')} />
              </div>
            )}
          </div>
        )}
        {!isCertificateMissing && materialDetails.requiresCertificate && (
           <div className="p-6 border border-gray-200 rounded-lg shadow-sm bg-white">
            <Label className="text-lg font-semibold text-gray-700">Certificate</Label>
             <p className="text-sm text-gray-500 mb-1">
              Status: <span className="font-medium text-green-600">Submitted</span>
            </p>
          </div>
        )}

        {isAssessmentFormMissing && materialDetails.requiresAssessmentForm && (
          <div className="p-6 border border-gray-200 rounded-lg shadow-sm bg-white">
            <Label htmlFor="assessment-form-upload" className="text-lg font-semibold text-gray-700">
              Upload Assessment Form (Required)
            </Label>
             <p className="text-sm text-gray-500 mb-1">
              Status: <span className="font-medium text-red-600">Missing</span>
            </p>
            <p className="text-sm text-gray-500 mb-3">
              An assessment form is required for this training material. Allowed formats: {ALLOWED_FORM_TYPES.join(', ')}. Max size: 10MB.
            </p>
            <FileUploader
              id="assessment-form-upload"
              onFilesSelected={(files) => handleFileUpload(files, 'assessmentForm')}
              accept={ALLOWED_FORM_TYPES.join(',')}
            />
            {assessmentFormFile.length > 0 && (
              <div className="mt-4">
                <FileList files={assessmentFormFile} onRemove={() => removeFile('assessmentForm')} />
              </div>
            )}
          </div>
        )}
        {!isAssessmentFormMissing && materialDetails.requiresAssessmentForm && (
           <div className="p-6 border border-gray-200 rounded-lg shadow-sm bg-white">
            <Label className="text-lg font-semibold text-gray-700">Assessment Form</Label>
            <p className="text-sm text-gray-500 mb-1">
              Status: <span className="font-medium text-green-600">Submitted</span>
            </p>
          </div>
        )}

        {(isCertificateMissing || isAssessmentFormMissing) && (
           <div className="mt-8 flex justify-end gap-3">
            <Button
              onClick={() => router.push(getDashboardUrl())}
              variant="outline"
              className="border border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Back
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || ((isCertificateMissing && certificateFile.length === 0) && (isAssessmentFormMissing && assessmentFormFile.length === 0))}
              className="bg-color3 hover:bg-color3/90 text-white min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <><UploadCloud size={18} className="mr-2" /> Submit Document(s)</>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}