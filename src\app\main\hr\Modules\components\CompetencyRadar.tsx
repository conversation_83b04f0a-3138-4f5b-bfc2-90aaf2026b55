import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Radar } from 'react-chartjs-2';
import { Info, RefreshCw } from 'lucide-react';

interface CompetencyRadarProps {
  className?: string;
  timeframe?: string; // Make optional to maintain backward compatibility
}

interface CompetencyData {
  id: number;
  name: string;
  description: string | null;
  uniqueUsersEngaged: number;
  totalSystemUsersForMetric: number;
  metrics: {
    trainedPercentage: number;
    inSystemPercentage: number;
    notEngagedPercentage: number;
  };
}

interface CompetencyStats {
  totalCompetencies: number;
  mostEngagedCompetency: string;
  highestEngagementRate: string;
  lowestEngagementRate: string;
}

const CompetencyRadar = forwardRef<any, CompetencyRadarProps>(({ 
  className,
  timeframe = 'This Week'
}, ref) => {
  const [selectedTab, setSelectedTab] = useState<'chart' | 'table'>('chart');
  const [competencyData, setCompetencyData] = useState<CompetencyData[]>([]);
  const [stats, setStats] = useState<CompetencyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useImperativeHandle(ref, () => ({
    getExportData: () => {
      if (!competencyData.length) return null;

      const headers = [
        'Competency Name',
        'Description',
        'Engaged Users',
        'Total Users',
        'Trained (%)',
        'Using System (%)',
        'Not Engaged (%)',
        'Status'
      ];

      const data = competencyData.map(comp => {
        const status = getStatusText(comp.metrics.trainedPercentage);
        return [
          comp.name,
          comp.description || 'N/A',
          comp.uniqueUsersEngaged,
          comp.totalSystemUsersForMetric,
          comp.metrics.trainedPercentage,
          comp.metrics.inSystemPercentage,
          comp.metrics.notEngagedPercentage,
          status
        ];
      });

      // Add summary statistics if available
      if (stats) {
        data.push(
          [], // Empty row as separator
          ['Summary Statistics'],
          ['Most Engaged Competency', stats.mostEngagedCompetency],
          ['Highest Engagement Rate', stats.highestEngagementRate],
          ['Lowest Engagement Rate', stats.lowestEngagementRate]
        );
      }

      return {
        title: 'Competency Radar Analysis Report',
        headers,
        data,
        timeframe
      };
    }
  }));

    const fetchCompetencyData = async () => {
    setIsRefreshing(true);
    setError(null);
      try {
      // For now, we'll just log the timeframe. The API endpoint remains unchanged
        const response = await fetch('/api/competencies/radar');
        if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch competency data');
        }
        const data = await response.json();
        setCompetencyData(data.competencies);
        setStats(data.stats);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      setIsRefreshing(false);
      }
    };

  useEffect(() => {
    fetchCompetencyData();
  }, [timeframe]); // Add timeframe as dependency

  const topCompetencies = competencyData.slice(0, 5);

  const radarChartData = {
    labels: topCompetencies.map(c => c.name),
    datasets: [
      {
        label: 'Trained', // Represents % of system users trained in this competency
        data: topCompetencies.map(c => c.metrics.trainedPercentage),
        backgroundColor: 'rgba(34, 197, 94, 0.2)',
        borderColor: 'rgba(34, 197, 94, 0.8)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(34, 197, 94, 1)',
        pointHoverRadius: 8,
        pointHoverBorderWidth: 3,
      },
      {
        label: 'Using System', // Represents % of system users using/engaged (same as trained for now)
        data: topCompetencies.map(c => c.metrics.inSystemPercentage),
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 0.8)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
        pointHoverRadius: 8,
        pointHoverBorderWidth: 3,
      },
      {
        label: 'Not Engaged', // Represents % of system users NOT engaged
        data: topCompetencies.map(c => c.metrics.notEngagedPercentage),
        backgroundColor: 'rgba(239, 68, 68, 0.2)',
        borderColor: 'rgba(239, 68, 68, 0.8)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(239, 68, 68, 1)',
        pointHoverRadius: 8,
        pointHoverBorderWidth: 3,
      }
    ]
  };

  const chartOptions = {
    scales: {
      r: {
        beginAtZero: true,
        max: 100, // Percentages are 0-100
        ticks: { stepSize: 20 },
        grid: { color: 'rgba(0, 0, 0, 0.1)' },
        pointLabels: { font: { size: 12, weight: 500 } },
        angleLines: { color: 'rgba(0, 0, 0, 0.1)' }
      }
    },
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: { padding: 20, usePointStyle: true }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        padding: 12,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.parsed.r}%`;
          }
        }
      }
    },
    responsive: true,
    maintainAspectRatio: false
  };

  // Status based on trainedPercentage (percentage of total system users)
  const getStatusColor = (trainedPercentage: number) => {
    if (trainedPercentage >= 80) return 'text-green-600';
    if (trainedPercentage >= 60) return 'text-blue-600'; 
    if (trainedPercentage >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusText = (trainedPercentage: number) => {
    if (trainedPercentage >= 80) return 'Excellent';
    if (trainedPercentage >= 60) return 'Good';
    if (trainedPercentage >= 40) return 'Fair';
    return 'Needs Improvement';
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="flex justify-center items-center h-64">
          <div className="text-red-600">Error: {error}</div>
        </div>
      </div>
    );
  }
  
  if (!competencyData.length && !stats) {
    return (
        <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
             <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-lg font-semibold">Competency Radar</h2>
                    <p className="text-sm text-gray-500">Monitor skill gaps and weakpoints yet to be addressed</p>
                </div>
            </div>
            <div className="text-center py-10">
                <Info size={48} className="mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">No competency data available at the moment.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex flex-col space-y-4 mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold">Competency Radar</h2>
            <p className="text-sm text-gray-500">Monitor skill gaps and weakpoints yet to be addressed</p>
            <p className="text-xs text-gray-400 mt-1">Showing data for: {timeframe}</p>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={fetchCompetencyData}
              disabled={isRefreshing}
              className={`p-2 rounded-lg text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-colors ${isRefreshing ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Refresh data"
            >
              <RefreshCw className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setSelectedTab('chart')}
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                selectedTab === 'chart'
                  ? 'bg-white text-gray-800 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Chart View
            </button>
            <button
              onClick={() => setSelectedTab('table')}
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                selectedTab === 'table'
                  ? 'bg-white text-gray-800 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Table View
            </button>
            </div>
          </div>
        </div>
      </div>

      {selectedTab === 'chart' ? (
        <>
          <div className="mb-8">
            <h3 className="text-sm font-medium text-gray-700 mb-4">Top 5 Competencies Overview</h3>
            <div className="h-[400px]">
              {topCompetencies.length > 0 ? 
                <Radar data={radarChartData} options={chartOptions} /> : 
                <p className="text-center text-gray-500 py-10">Not enough data for radar chart.</p> }
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-sm font-medium text-gray-700 mb-4">All Competencies</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competency</th>
                    {/* Total Users can be interpreted as unique users engaged for this competency */}
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Engaged Users</th> 
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Trained (%)</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Using System (%)</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Not Engaged (%)</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {competencyData.map((competency) => {
                    const status = getStatusText(competency.metrics.trainedPercentage);
                    const statusColor = getStatusColor(competency.metrics.trainedPercentage);
                    return (
                      <tr key={competency.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900">{competency.name}</td>
                        <td className="px-4 py-3 text-sm text-center text-gray-900">{competency.uniqueUsersEngaged}</td>
                        <td className="px-4 py-3 text-sm text-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {competency.metrics.trainedPercentage}%
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {competency.metrics.inSystemPercentage}%
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {competency.metrics.notEngagedPercentage}%
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-center">
                          <span className={`font-medium ${statusColor}`}>
                            {status}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competency</th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Engaged Users</th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Trained (%)</th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Using System (%)</th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Not Engaged (%)</th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {competencyData.map((competency) => {
                const status = getStatusText(competency.metrics.trainedPercentage);
                const statusColor = getStatusColor(competency.metrics.trainedPercentage);
                return (
                  <tr key={competency.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">{competency.name}</td>
                    <td className="px-4 py-3 text-sm text-center text-gray-900">{competency.uniqueUsersEngaged}</td>
                    <td className="px-4 py-3 text-sm text-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {competency.metrics.trainedPercentage}%
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {competency.metrics.inSystemPercentage}%
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {competency.metrics.notEngagedPercentage}%
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-center">
                      <span className={`font-medium ${statusColor}`}>
                        {status}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}

      {stats && (
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-100">
            <div className="flex items-center gap-2 mb-2">
              <Info size={16} className="text-blue-600" />
              <h3 className="text-sm font-medium text-blue-900">Most Engaged Competency</h3>
            </div>
            <p className="text-sm text-blue-800">
              {stats.mostEngagedCompetency}
              {/* Display uniqueUsersEngaged for the most engaged competency */}
              {competencyData.find(c => c.name === stats.mostEngagedCompetency) && (
              <span className="block text-xs text-blue-600 mt-1">
                  {competencyData.find(c => c.name === stats.mostEngagedCompetency)?.uniqueUsersEngaged || 0} users engaged
              </span>
              )}
            </p>
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg border border-green-100">
            <div className="flex items-center gap-2 mb-2">
              <Info size={16} className="text-green-600" />
              <h3 className="text-sm font-medium text-green-900">Highest Engagement Rate</h3>
            </div>
            <p className="text-sm text-green-800">
              {stats.highestEngagementRate}
               {/* Optionally show the percentage rate */}
               {competencyData.find(c => c.name === stats.highestEngagementRate) && (
                <span className="block text-xs text-green-600 mt-1">
                  {competencyData.find(c => c.name === stats.highestEngagementRate)?.metrics.trainedPercentage || 0}%
                </span>
              )}
            </p>
          </div>
          
          <div className="p-4 bg-red-50 rounded-lg border border-red-100">
            <div className="flex items-center gap-2 mb-2">
              <Info size={16} className="text-red-600" />
              <h3 className="text-sm font-medium text-red-900">Lowest Engagement Rate</h3>
            </div>
            <p className="text-sm text-red-800">
              {stats.lowestEngagementRate}
              {/* Optionally show the percentage rate */}
              {competencyData.find(c => c.name === stats.lowestEngagementRate) && (
                <span className="block text-xs text-red-600 mt-1">
                  {competencyData.find(c => c.name === stats.lowestEngagementRate)?.metrics.trainedPercentage || 0}%
                </span>
              )}
            </p>
          </div>
        </div>
      )}
    </div>
  );
});

export default CompetencyRadar; 