"use client"

import React, { useState, useMemo } from 'react'
import DashboardHeader from './components/DashboardComponents/DashboardHeader'
import OverviewCards from './components/DashboardComponents/OverviewCards'
import SystemActivityChart from './components/DashboardComponents/SystemActivityChart'
import RecentEvents from './components/DashboardComponents/RecentEvents'
import TabNavigation from './components/DashboardComponents/TabNavigation'
import SecurityTabContent from './components/DashboardComponents/SecurityTabContent'

const tabs = [
  { id: 'overview', label: 'Overview' },
  { id: 'security', label: 'Security' },
]

const ITDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')

  const renderActiveTabContent = useMemo(() => {
    if (activeTab === 'overview') {
      return (
        <>
          <div className="mb-3">
            <OverviewCards />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 flex-1 min-h-0">
            <div className="lg:col-span-2 flex flex-col h-full min-h-0">
              <SystemActivityChart />
            </div>
            <div className="flex flex-col h-full min-h-0">
              <RecentEvents />
            </div>
          </div>
        </>
      )
    }
    if (activeTab === 'security') return <SecurityTabContent />
    return null
  }, [activeTab])

  return (
    <div className="h-screen w-full flex flex-col">
      <div className="flex-shrink-0">
        <DashboardHeader />
        <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />
      </div>
      <div className="flex-1 flex flex-col min-h-0">
        {renderActiveTabContent}
      </div>
    </div>
  )
}

export default ITDashboard
