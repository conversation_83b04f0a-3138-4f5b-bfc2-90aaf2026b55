import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Count of units
    const unitCount = await prisma.unit.count();
    
    // Get all divisions
    const divisions = await prisma.division.findMany({
      select: {
        id: true,
        divisionName: true,
      }
    });
    
    // Count units per division
    const unitsByDivision = await Promise.all(
      divisions.map(async (division) => {
        const count = await prisma.unit.count({
          where: { divisionId: division.id }
        });
        return {
          divisionId: division.id,
          divisionName: division.divisionName,
          unitCount: count
        };
      })
    );
    
    // Count users per division
    const usersByDivision = await Promise.all(
      divisions.map(async (division) => {
        const count = await prisma.user.count({
          where: { 
            unit: {
              divisionId: division.id
            }
          }
        });
        return {
          divisionId: division.id,
          divisionName: division.divisionName,
          userCount: count
        };
      })
    );
    
    // Get sample of training progress entries
    const trainingProgressSample = await prisma.userTrainingProgress.findMany({
      take: 5,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            unitId: true,
            unit: {
              select: {
                unitName: true,
                divisionId: true
              }
            }
          }
        },
        trainingMaterial: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });
    
    return NextResponse.json({
      unitCount,
      divisions,
      unitsByDivision,
      usersByDivision,
      trainingProgressSample
    });
    
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json(
      { error: 'Debug endpoint failed' },
      { status: 500 }
    );
  }
} 