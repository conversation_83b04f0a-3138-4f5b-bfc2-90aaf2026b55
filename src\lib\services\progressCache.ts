import redis from '../redis';
import {
  materialProgressSchema,
  stepProgressSchema,
  moduleProgressSchema,
  courseProgressSchema,
  MaterialProgress,
  StepProgress,
  ModuleProgress,
  CourseProgress
} from '../schemas/progress';

// Key patterns for Redis
const MATERIAL_KEY = (userId: string, courseId: string, materialId: string) =>
  `progress:material:${userId}:${courseId}:${materialId}`;

const STEP_KEY = (userId: string, courseId: string, stepId: string) =>
  `progress:step:${userId}:${courseId}:${stepId}`;

const MODULE_KEY = (userId: string, courseId: string, moduleId: string) =>
  `progress:module:${userId}:${courseId}:${moduleId}`;

const COURSE_KEY = (userId: string, courseId: string) =>
  `progress:course:${userId}:${courseId}`;

// TTL in seconds (1 hour by default)
const DEFAULT_TTL = 3600;

// Material progress functions
export async function cacheMaterialProgress(data: MaterialProgress, ttl = DEFAULT_TTL): Promise<boolean> {
  try {
    console.log(`1.) Viewing Current Material - ID: ${data.materialId} Name: ${data.materialName}`);

    // Validate data with Zod
    const validData = materialProgressSchema.parse(data);
    console.log(`2.) Validating material data for caching - Type: ${validData.materialType}, Progress: ${validData.progress}%`);

    // Create the key
    const key = MATERIAL_KEY(validData.userId, validData.courseId, validData.materialId);
    console.log(`3.) Adjusting Redis Database - Key: ${key}`);

    // Store in Redis with TTL
    await redis.set(key, JSON.stringify(validData), 'EX', ttl);
    console.log(`4.) Caching progress for Material ID: ${validData.materialId} Type: ${validData.materialType} - Success`);

    return true;
  } catch (error) {
    console.error(`Error caching material progress for ID: ${data.materialId}:`, error);
    return false;
  }
}

export async function getMaterialProgress(userId: string, courseId: string, materialId: string): Promise<MaterialProgress | null> {
  try {
    const key = MATERIAL_KEY(userId, courseId, materialId);
    const data = await redis.get(key);

    if (!data) return null;

    // Parse and validate with Zod
    return materialProgressSchema.parse(JSON.parse(data));
  } catch (error) {
    return null;
  }
}

// Step progress functions
export async function cacheStepProgress(data: StepProgress, ttl = DEFAULT_TTL): Promise<boolean> {
  try {
    const validData = stepProgressSchema.parse(data);
    const key = STEP_KEY(validData.userId, validData.courseId, validData.stepId);


    await redis.set(key, JSON.stringify(validData), 'EX', ttl);
return true;
  } catch (error) {
    return false;
  }
}

export async function getStepProgress(userId: string, courseId: string, stepId: string): Promise<StepProgress | null> {
  try {
    const key = STEP_KEY(userId, courseId, stepId);
    const data = await redis.get(key);

    if (!data) return null;

    return stepProgressSchema.parse(JSON.parse(data));
  } catch (error) {
    return null;
  }
}

// Module progress functions
export async function cacheModuleProgress(data: ModuleProgress, ttl = DEFAULT_TTL): Promise<boolean> {
  try {
    const validData = moduleProgressSchema.parse(data);
    const key = MODULE_KEY(validData.userId, validData.courseId, validData.moduleId);


    await redis.set(key, JSON.stringify(validData), 'EX', ttl);
 return true;
  } catch (error) {
    return false;
  }
}

export async function getModuleProgress(userId: string, courseId: string, moduleId: string): Promise<ModuleProgress | null> {
  try {
    const key = MODULE_KEY(userId, courseId, moduleId);
    const data = await redis.get(key);

    if (!data) return null;

    return moduleProgressSchema.parse(JSON.parse(data));
  } catch (error) {
    return null;
  }
}

// Course progress functions
export async function cacheCourseProgress(data: CourseProgress, ttl = DEFAULT_TTL): Promise<boolean> {
  try {
    const validData = courseProgressSchema.parse(data);
    const key = COURSE_KEY(validData.userId, validData.courseId);


    await redis.set(key, JSON.stringify(validData), 'EX', ttl);
return true;
  } catch (error) {
    return false;
  }
}

export async function getCourseProgress(userId: string, courseId: string): Promise<CourseProgress | null> {
  try {
    const key = COURSE_KEY(userId, courseId);
    const data = await redis.get(key);

    if (!data) return null;

    return courseProgressSchema.parse(JSON.parse(data));
  } catch (error) {
    return null;
  }
}

// Helper to create a lock for avoiding race conditions
export async function acquireLock(key: string, ttl = 30): Promise<boolean> {
  const lockKey = `lock:${key}`;
  // According to ioredis issue #1811, we need to use the correct parameter order
  // for NX and EX, not the object syntax which is causing TypeScript errors
  const result = await redis.set(lockKey, '1', 'EX', ttl, 'NX');
  return result === 'OK';
}

export async function releaseLock(key: string): Promise<void> {
  const lockKey = `lock:${key}`;
  await redis.del(lockKey);
}

// Helper to delete progress
export async function deleteProgress(key: string): Promise<boolean> {
  try {
    await redis.del(key);
    return true;
  } catch (error) {
    return false;
  }
}