'use client';

import React from 'react';
import { StatusBadgeProps } from '../utils/types';
import { statusConfig } from '../data/config';
import { MaterialStatus } from '@prisma/client';

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  // Ensure we have a valid MaterialStatus
  const validStatus = Object.values(MaterialStatus).includes(status) ? status : MaterialStatus.DRAFT;
  const config = statusConfig[validStatus];
  const Icon = config.icon;

  return (
    <div className={config.className}>
      <Icon className="w-4 h-4 inline-block mr-1" />
      <span>{config.text}</span>
    </div>
  );
};

export default StatusBadge;