import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';

const markReadSchema = z.object({
  notificationIds: z.array(z.number().int().positive()).min(1).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) { 
      return NextResponse.json({ error: 'Unauthorized: Please log in' }, { status: 401 });
    }
    
    const userId = parseInt(session.user.id as string, 10);
    if (isNaN(userId)) {
        return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 });
    }

    let rawPayload;
    try {
      rawPayload = await request.json();
    } catch (error) {
      console.error('Invalid request body:', error);
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    const validationResult = markReadSchema.safeParse(rawPayload);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    const { notificationIds } = validationResult.data;

    if (notificationIds && notificationIds.length > 0) {
      await prisma.systemNotification.updateMany({
        where: {
          id: { in: notificationIds },
          recipientUserId: userId
        },
        data: {
          isRead: true,
          updatedAt: new Date()
        }
      });

      return NextResponse.json({ 
        success: true,
        message: `Marked ${notificationIds.length} notification(s) as read`
      });
    } 

    else {
      const result = await prisma.systemNotification.updateMany({
        where: {
          recipientUserId: userId,
          isRead: false
        },
        data: {
          isRead: true,
          updatedAt: new Date()
        }
      });

      return NextResponse.json({ 
        success: true,
        message: `Marked ${result.count} notification(s) as read`
      });
    }
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return NextResponse.json(
      { error: 'Failed to mark notifications as read' },
      { status: 500 }
    );
  }
} 