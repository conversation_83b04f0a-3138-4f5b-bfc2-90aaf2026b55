import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { MaterialStatus } from '@prisma/client';
import { z } from 'zod';

const requestFileSchema = z.object({
  type: z.enum(['file', 'link']),
  link: z.string().url().optional(),
  description: z.string().optional(),
  videoType: z.string().optional(),
  fileName: z.string().optional(),
  fileType: z.string().optional(),
});

const requestModuleSchema = z.object({
  title: z.string().min(1, 'Module title is required'),
  files: z.array(requestFileSchema),
});

const createMaterialSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  categoryId: z.number({ required_error: 'Category is required' }),
  modules: z.array(requestModuleSchema).min(1, 'At least one module is required'),
  existingCompetencyIds: z.array(z.number()).optional(),
  newCompetencyNames: z.array(z.string()).optional(),
  participantIds: z.array(z.string()).optional(),
  learningObjectives: z.array(z.string()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = parseInt(session.user.id.toString());
    
    let rawBody;
    try {
      rawBody = await request.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const validationResult = createMaterialSchema.safeParse(rawBody);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }
    const {
      title, 
      description, 
      categoryId,
      modules,
      existingCompetencyIds = [],
      newCompetencyNames = [],
      participantIds = [],
      learningObjectives = []
    } = validationResult.data;

    const trainingMaterial = await prisma.$transaction(async (tx) => {
      // 1. Handle temporary competencies
      const tempCompetencyPromises = newCompetencyNames.map(async (name: string) => {
        const existingTempComp = await tx.temporaryCompetency.findFirst({
          where: {
            competencyName: name,
            status: 'PENDING'
          }
        });
        if (existingTempComp) {
          return existingTempComp;
        }
        return tx.temporaryCompetency.create({
          data: {
            competencyName: name,
            status: 'PENDING',
            createdByUserId: userId
          }
        });
      });
      const createdTempCompetencies = await Promise.all(tempCompetencyPromises);
      const tempCompetencyIds = createdTempCompetencies.map(comp => comp.id);

      // 2. Create the training material (without modules)
      const material = await tx.trainingMaterial.create({
        data: {
          title,
          description,
          categoryId,
          status: MaterialStatus.DRAFT,
          objectives: learningObjectives.length > 0 ? {
            create: learningObjectives.map((text: string, index: number) => ({
              text,
              displayOrder: index + 1
            }))
          } : undefined,
          materialCompetencies: {
            create: [
              ...existingCompetencyIds.map((id: number) => ({
                competency_id: id
              })),
              ...tempCompetencyIds.map((id: number) => ({
                temporary_competency_id: id
              }))
            ]
          },
          trainingParticipants: {
            create: [
              {
                userId,
                isCreator: true
              },
              ...participantIds
                .filter((id: string) => id !== userId.toString())
                .map((id: string) => ({
                  userId: parseInt(id),
                  isCreator: false
                }))
            ]
          }
        }
      });

      // 3. For each module, create the module, then its resources, then link via ModuleResources
      const createdModules = [];
      for (let moduleIndex = 0; moduleIndex < modules.length; moduleIndex++) {
        const module = modules[moduleIndex];
        const createdModule = await tx.module.create({
          data: {
            title: module.title,
            moduleOrder: moduleIndex + 1,
            trainingMaterialId: material.id,
          }
        });
        // For each resource in the module
        for (let resourceIndex = 0; resourceIndex < module.files.length; resourceIndex++) {
          const file = module.files[resourceIndex];
          const createdResource = await tx.trainingResource.create({
            data: {
              resourceType: file.type,
              resourceUrl: file.link,
              description: file.description,
              videoType: file.videoType,
              fileName: file.fileName,
              fileType: file.fileType,
              displayOrder: resourceIndex + 1,
            }
          });
          await tx.moduleResources.create({
            data: {
              A: createdModule.id,
              B: createdResource.id
            }
          });
        }
        createdModules.push(createdModule);
      }

      // 4. Return the material, including modules and their resources (in order)
      // Note: Prisma does not support orderBy inside include for nested relations, so we sort in JS below
      const fullMaterial = await tx.trainingMaterial.findUnique({
        where: { id: material.id },
        include: {
          modules: {
            include: {
              ModuleResources: {
                include: {
                  TrainingResource: true
                }
              }
            }
          }
        }
      });
      // Sort modules and resources in JS to ensure correct order
      if (fullMaterial && fullMaterial.modules) {
        fullMaterial.modules.sort((a, b) => a.moduleOrder - b.moduleOrder);
        for (const mod of fullMaterial.modules) {
          if (mod.ModuleResources) {
            mod.ModuleResources.sort((a, b) => (a.TrainingResource?.displayOrder || 0) - (b.TrainingResource?.displayOrder || 0));
          }
        }
      }
      return fullMaterial;
    });

    return NextResponse.json({
      success: true,
      material: trainingMaterial
    });

  } catch (error) {
    console.error('Error creating training material:', error);
    return NextResponse.json(
      { error: 'Failed to create training material' },
      { status: 500 }
    );
  }
}