import { LucideIcon } from 'lucide-react';

export type MaterialType = 'text' | 'video' | 'quiz' | 'pdf' | 'ppt' | 'document' | 'youtube' | 'jpg' | 'jpeg' | 'png';

export interface Material {
  name: string;
  type: MaterialType;
  file: string;
  active: boolean;
  viewed: boolean;
}

export interface TrainingStep {
  id: string;
  title: string;
  description: string;
  materials: Material[];
  learningObjectives: string[];
  isCompleted: boolean;
  estimatedTime: string;
}

export interface TrainingModule {
  id: string;
  title: string;
  description: string;
  moduleOrder: number;
  steps: TrainingStep[];
  isExpanded: boolean;
  completed: boolean;
}

export interface TrainingData {
  id: string;
  title: string;
  description: string;
  modules: TrainingModule[];
  totalSteps: number;
  completedSteps: number;
  estimatedTotalTime: string;
  progress: number;
  lastModule?: string;
  instructor?: string;
}

export interface SidebarToggleProps {
  isExpanded: boolean;
  onToggle: () => void;
  position: 'left' | 'right';
}

export interface NavbarProps {
  courseTitle: string;
  onBack: () => void;
}

export interface MaterialsListProps {
  modules: TrainingModule[];
  selectedStep: string | null;
  onStepSelect: (stepId: string) => void;
  onModuleToggle: (moduleId: string) => void;
  onMaterialClick: (moduleIndex: number, materialIndex: number) => void;
  openModule: number;
}

export interface MaterialPreviewProps {
  selectedStep: TrainingStep | null;
  currentMaterial: Material | null;
  onComplete: (stepId: string) => void;
  onNextMaterial: () => void;
  onPreviousMaterial: () => void;
  onMarkAsRead: () => void;
  currentPage?: number;
  numPages?: number | null;
  onPageChange: (page: number) => void;
  savedProgress?: number;
}

export interface MaterialOverviewProps {
  currentStep: TrainingStep | null;
  trainingData: TrainingData;
  currentMaterial: Material | null;
  onNextModule?: (moduleId: string) => void;
  allStepsCompleted?: boolean;
  onFinishCourse?: () => void;
}