import { User } from '../components/UsermanagementComponents/UserTable';

export interface UserFilter {
  searchQuery?: string;
  role?: string;
  status?: string;
}

class UserService {
  private baseUrl = '/api/users'; // Will be used when real API is ready

  async getUsers(filters: UserFilter): Promise<{ users: User[]; total: number }> {
    // For now, using mock data
    const mockUsers = [
      {
        id: '1',
        firstName: 'John',
        lastName: '<PERSON>',
        email: '<EMAIL>',
        role: 'IT Admin',
        department: 'IT Department',
        status: 'Active',
        lastLogin: '2 minutes ago'
      },
      // ... other mock users
    ];

    return {
      users: mockUsers,
      total: mockUsers.length
    };
  }

  async exportUsers(filters: UserFilter, users: User[]): Promise<Blob> {
    // Convert users to CSV
    const headers = ['ID', 'First Name', 'Last Name', 'Email', 'Role', 'Department', 'Status', 'Last Login'];
    const rows = users.map(user => [
      user.id,
      user.firstName,
      user.lastName,
      user.email,
      user.role,
      user.department,
      user.status,
      user.lastLogin
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  }
}

export const userService = new UserService(); 