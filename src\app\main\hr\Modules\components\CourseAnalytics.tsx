import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { ChevronDown, Filter, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, FolderOpen } from 'lucide-react';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface Course {
  name: string;
  started: number;
  completed: number;
  effectiveness: number;
  score: number;
  category: string;
}

interface Category {
  id: number;
  name: string;
}

interface ApiResponse {
  courses: Course[];
  categories: Category[];
}

interface CourseAnalyticsProps {
  timeframe?: string;
}

const EmptyState = ({ message }: { message: string }) => (
  <div className="flex flex-col items-center justify-center py-12 px-4">
    <div className="text-gray-400">
      <FolderOpen className="w-16 h-16 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-1">No Data Available</h3>
      <p className="text-gray-500 text-center mb-4">{message}</p>
    </div>
  </div>
);

const CourseAnalytics = forwardRef<any, CourseAnalyticsProps>(({ timeframe = 'This Week' }, ref) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedView, setSelectedView] = useState<'table' | 'charts'>('table');
  const [courses, setCourses] = useState<Course[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useImperativeHandle(ref, () => ({
    getExportData: () => {
      if (!courses.length) return null;

      const headers = [
        'Course Name',
        'Category',
        'Started',
        'Completed',
        'Completion Rate (%)',
        'Effectiveness Score',
        'Status'
      ];

      const data = courses.map(course => {
        const completionRate = Math.round((course.completed / course.started) * 100);
        const status = getCompletionRateColor(completionRate).includes('green') 
          ? 'Excellent' 
          : getCompletionRateColor(completionRate).includes('yellow')
          ? 'Good'
          : 'Needs Improvement';

        return [
          course.name,
          course.category,
          course.started,
          course.completed,
          completionRate,
          course.effectiveness,
          status
        ];
      });

      // Add effectiveness distribution summary
      const effectivenessRanges = {
        'Excellent (90-100%)': 0,
        'Good (80-89%)': 0,
        'Fair (70-79%)': 0,
        'Needs Improvement (<70%)': 0
      };

      courses.forEach(course => {
        if (course.effectiveness >= 90) effectivenessRanges['Excellent (90-100%)']++;
        else if (course.effectiveness >= 80) effectivenessRanges['Good (80-89%)']++;
        else if (course.effectiveness >= 70) effectivenessRanges['Fair (70-79%)']++;
        else effectivenessRanges['Needs Improvement (<70%)']++;
      });

      // Add summary section
      data.push(
        [], // Empty row as separator
        ['Effectiveness Distribution'],
        ...Object.entries(effectivenessRanges).map(([range, count]) => [
          range,
          count,
          `${((count / courses.length) * 100).toFixed(1)}%`
        ])
      );

      return {
        title: 'Course Analytics Report',
        headers,
        data,
        timeframe
      };
    }
  }));

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await fetch(`/api/analytics/courses?category=${selectedCategory}`);
        if (!response.ok) {
          throw new Error('Failed to fetch course analytics');
        }
        const data: ApiResponse = await response.json();
        setCourses(data.courses);
        setCategories(data.categories);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [selectedCategory, timeframe]);

  const getCompletionRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const barChartData = {
    labels: courses.map(course => course.name),
    datasets: [
      {
        label: 'Completion Rate',
        data: courses.map(course => (course.completed / course.started) * 100),
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
      },
      {
        label: 'Effectiveness',
        data: courses.map(course => course.effectiveness),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      }
    ]
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(tickValue: number | string) {
            return `${tickValue}%`;
          }
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    },
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
        }
      }
    }
  };

  // Calculate effectiveness ranges for pie chart
  const effectivenessRanges = {
    'Excellent (90-100%)': 0,
    'Good (80-89%)': 0,
    'Fair (70-79%)': 0,
    'Needs Improvement (<70%)': 0
  };

  courses.forEach(course => {
    if (course.effectiveness >= 90) effectivenessRanges['Excellent (90-100%)']++;
    else if (course.effectiveness >= 80) effectivenessRanges['Good (80-89%)']++;
    else if (course.effectiveness >= 70) effectivenessRanges['Fair (70-79%)']++;
    else effectivenessRanges['Needs Improvement (<70%)']++;
  });

  const pieChartData = {
    labels: Object.keys(effectivenessRanges),
    datasets: [
      {
        data: Object.values(effectivenessRanges),
        backgroundColor: [
          'rgba(34, 197, 94, 0.6)',  // green
          'rgba(59, 130, 246, 0.6)', // blue
          'rgba(245, 158, 11, 0.6)', // yellow
          'rgba(239, 68, 68, 0.6)',  // red
        ],
        borderColor: [
          'rgba(34, 197, 94, 1)',
          'rgba(59, 130, 246, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(239, 68, 68, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const total = Object.values(effectivenessRanges).reduce((a, b) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} courses (${percentage}%)`;
          }
        }
      }
    }
  };

  // Check if there's data to display
  const hasData = courses.length > 0;

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header with Filter and View Toggle */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-lg font-semibold">Course Analytics</h2>
          <p className="text-sm text-gray-500">Track course completion and effectiveness</p>
          <p className="text-xs text-gray-400 mt-1">Showing data for: {timeframe}</p>
        </div>
        <div className="flex items-center gap-4">
          {/* View Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setSelectedView('table')}
              className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
                selectedView === 'table'
                  ? 'bg-white text-gray-800 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <BarChart3 size={16} />
              Table View
            </button>
            <button
              onClick={() => setSelectedView('charts')}
              className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
                selectedView === 'charts'
                  ? 'bg-white text-gray-800 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <PieChart size={16} />
              Charts View
            </button>
          </div>

          {/* Category Filter */}
          <div className="relative">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 px-2.5 py-1.5 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer"
            >
              <Filter size={14} className="text-gray-500" />
              <span className="text-gray-700 max-w-[180px] truncate">{selectedCategory}</span>
              <ChevronDown size={14} className={`text-gray-500 transition-transform ${isFilterOpen ? 'rotate-180' : ''}`} />
            </button>
            {isFilterOpen && (
              <div className="absolute right-0 mt-1 w-[240px] bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1 max-h-[400px] overflow-y-auto">
                  {/* All Categories Option */}
                  <button
                    onClick={() => {
                      setSelectedCategory('All Categories');
                      setIsFilterOpen(false);
                    }}
                    className={`block w-full text-left px-4 py-2 text-sm ${
                      selectedCategory === 'All Categories'
                        ? 'bg-color3 text-white'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    All Categories
                  </button>

                  {/* Divider */}
                  <div className="border-t border-gray-200 my-1"></div>

                  {/* Categories List */}
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => {
                        setSelectedCategory(category.name);
                        setIsFilterOpen(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm ${
                        selectedCategory === category.name
                          ? 'bg-color3 text-white'
                          : 'hover:bg-gray-100 text-gray-700'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {!hasData ? (
        <div className="bg-white rounded-lg shadow-md">
          <EmptyState 
            message={
              selectedCategory === 'All Categories' 
                ? "There are no courses available in the system yet."
                : `There are no courses available in the "${selectedCategory}" category.`
            } 
          />
        </div>
      ) : selectedView === 'table' ? (
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium">Course Completion Rates</h3>
            <p className="text-xs text-gray-500 mt-1">Overview of course completion statistics</p>
          </div>
          <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course Name</th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Started</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                {courses.map((course, index) => {
                    const completionRate = Math.round((course.completed / course.started) * 100);
                    return (
                      <tr key={index}>
                      <td className="px-4 py-3 text-sm text-gray-900">{course.name}</td>
                      <td className="px-4 py-3 text-sm text-center text-gray-600">{course.category}</td>
                        <td className="px-4 py-3 text-sm text-center text-gray-600">{course.started}</td>
                        <td className="px-4 py-3 text-sm text-center text-gray-600">{course.completed}</td>
                        <td className="px-4 py-3 text-center text-sm">
                          <span className={`font-medium ${getCompletionRateColor(completionRate)}`}>
                            {completionRate}%
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Bar Chart */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-sm font-medium mb-4">Course Completion & Effectiveness</h3>
            <div className="h-[400px] flex items-center justify-center">
              {hasData ? (
              <Bar data={barChartData} options={barChartOptions} />
              ) : (
                <EmptyState message="No course data available to display charts." />
              )}
            </div>
          </div>

          {/* Pie Chart */}
          <div className="bg-white rounded-lg shadow-md p-4">
            <h3 className="text-sm font-medium mb-4">Course Effectiveness Distribution</h3>
            <div className="h-[400px] flex items-center justify-center">
              {hasData ? (
              <Pie data={pieChartData} options={pieChartOptions} />
              ) : (
                <EmptyState message="No effectiveness data available to display distribution." />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

export default CourseAnalytics; 