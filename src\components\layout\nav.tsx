'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Logo from '../../../public/images/Logo.svg';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import Modal from '@/components/ui/modal';
import LoginForm from '@/components/ui/Loginform';
import ForgotPasswordForm from '@/components/ui/ForgotPasswordForm';

// Define the possible views for the auth modal
type AuthModalView = 'login' | 'forgotPassword';

const Nav = () => {
  // Single state to control modal visibility and view
  const [modalView, setModalView] = useState<AuthModalView | null>(null); 
  const pathname = usePathname();

  const isLandingPage = pathname === '/';

  // Function to open the modal in login view
  const openLoginModal = () => setModalView('login');

  // Function to switch the modal view to forgotPassword
  const switchToForgotPassword = () => setModalView('forgotPassword');

  // Function to switch the modal view back to login
  const switchToLogin = () => setModalView('login');

  // Function to close the modal entirely
  const closeModal = () => setModalView(null);

  // Determine the modal title based on the current view
  const modalTitle = modalView === 'login' ? "Login to your account" : "Forgot Password";

  return (
    <>
      <nav className="w-full h-[70px] bg-white shadow-md sticky top-0 z-50 flex items-center px-4 sm:px-6 lg:px-16 border-b border-gray-200">
        <div className="w-full flex justify-between items-center">
          <div className="flex items-center">
            <Link href="/">
              <Image src={Logo} alt="Logo" width={128} height={128} className="h-auto w-32 mr-2" priority />
            </Link>
          </div>
          {isLandingPage && (
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-4">
                <Button
                  variant="default"
                  size="lg"
                  className='bg-color3 text-white cursor-pointer transition  hover:scale-105'
                  onClick={openLoginModal}
                >
                  Login
                </Button>
              </div>
            </div>
          )}
        </div>
      </nav>

      <Modal
        isOpen={modalView !== null}
        onClose={closeModal}
        title={modalTitle}
        size="small"
      >
        {modalView === 'login' && (
          <LoginForm
            onClose={closeModal}
            onForgotPassword={switchToForgotPassword}
          />
        )}
        {modalView === 'forgotPassword' && (
          <ForgotPasswordForm
            onSwitchToLogin={switchToLogin}
          />
        )}
      </Modal>
    </>
  );
};

export default Nav;
