'use client';

import React, { useState, useMemo } from 'react';
import { Bell, Check, X, Search, AlertCircle, Funnel, Calendar, ChevronDown, Eye } from 'lucide-react';
import Modal from '@/components/ui/modal';
import Pagination from './Pagination';
import Swal from 'sweetalert2';
import { Participant } from '../utils/types';

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  trainingMaterialId: number;
}

// Helper function to highlight text
const highlightText = (text: string, searchQuery: string) => {
  if (!searchQuery.trim()) return text;

  const regex = new RegExp(`(${searchQuery.trim()})`, 'gi');
  const parts = text.split(regex);

  return parts.map((part, index) => {
    if (part.toLowerCase() === searchQuery.toLowerCase()) {
      return <span key={index} className="text-blue-600 font-semibold">{part}</span>;
    }
    return part;
  });
};

const ReviewModal: React.FC<ReviewModalProps> = ({ isOpen, onClose, trainingMaterialId }) => {
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(false);
  const [trainingMaterialTitle, setTrainingMaterialTitle] = useState<string>('');

  React.useEffect(() => {
    if (!isOpen || !trainingMaterialId) return;
    setLoading(true);
    fetch(`/api/training-material/${trainingMaterialId}`)
      .then(async res => {
        try {
          const data = await res.json();
          setTrainingMaterialTitle(data.title || 'Untitled Training');
          // Map trainingParticipants to Participant[]
          const participants = (data.trainingParticipants || []).map((tp: any) => {
            let name = 'Unknown';
            if (tp.user) {
              if (tp.user.firstName || tp.user.lastName) {
                name = `${tp.user.firstName || ''} ${tp.user.lastName || ''}`.trim();
              } else if (tp.user.email) {
                name = tp.user.email;
              }
            }
            return {
              name,
              userId: tp.userId, // Ensure userId is present for notification API
              hasAssessment: !!tp.assessmentFormKey,
              assessmentFormKey: tp.assessmentFormKey,
              hasCertification: !!tp.certificateKey,
              certificateKey: tp.certificateKey,
              // Add more fields as needed
            };
          });
          setParticipants(participants);
        } catch (e) {
          setParticipants([]);
        }
      })
      .finally(() => setLoading(false));
  }, [isOpen, trainingMaterialId]);

  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState<'name' | 'status'>('name');
  const itemsPerPage = 5;

  // Filter participants based on search query and status
  const filteredParticipants = useMemo(() => {
    return participants.filter(participant => {
      const searchTerms = searchQuery.toLowerCase().trim();
      const matchesSearch = !searchTerms || 
        participant.name.toLowerCase().includes(searchTerms);
      
      const matchesStatus = selectedStatus === 'all' || 
        (selectedStatus === 'complete' && participant.hasAssessment && participant.hasCertification) ||
        (selectedStatus === 'incomplete' && (!participant.hasAssessment || !participant.hasCertification));
      
      return matchesSearch && matchesStatus;
    });
  }, [participants, searchQuery, selectedStatus]);

  // Sort participants
  const sortedParticipants = useMemo(() => {
    return [...filteredParticipants].sort((a, b) => {
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name);
      } else {
        // Sort by status (complete first)
        const aComplete = a.hasAssessment && a.hasCertification;
        const bComplete = b.hasAssessment && b.hasCertification;
        return bComplete ? 1 : aComplete ? -1 : 0;
      }
    });
  }, [filteredParticipants, sortBy]);

  // Pagination calculations
  const totalItems = sortedParticipants.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = (currentPage - 1) * itemsPerPage + 1;
  const currentParticipants = sortedParticipants.slice(indexOfFirstItem - 1, indexOfLastItem);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleViewDocument = (fileKey: string | null | undefined) => {
    if (!fileKey) {
      console.error('No file key provided.');
      // Optionally show an error message to the user
      Swal.fire('Error', 'No document key found to view.', 'error');
      return;
    }
    // Construct the URL to the download endpoint
    // IMPORTANT: This assumes a consistent download endpoint structure
    const downloadUrl = `/api/training-material/download?key=${encodeURIComponent(fileKey)}`;
    window.open(downloadUrl, '_blank', 'noopener,noreferrer');
  };

  const handleNotify = async (participant: Participant) => {
    const missingItems = [];
    if (!participant.hasAssessment) missingItems.push('Assessment Form');
    if (!participant.hasCertification) missingItems.push('Certification');

    if (missingItems.length === 0) {
      await Swal.fire({
        title: 'No Action Needed',
        text: `${participant.name} has already submitted all required documents.`,
        icon: 'info',
        confirmButtonColor: '#0077CC',
      });
      return;
    }

    const result = await Swal.fire({
      title: 'Send Notification?',
      html: `
        Are you sure you want to notify <strong>${participant.name}</strong> about missing:<br>
        <ul style="text-align: left; margin-top: 10px;">
          ${missingItems.map(item => `<li>• ${item}</li>`).join('')}
        </ul>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#0077CC',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, notify',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        setLoading(true);
        const missingItemsText = missingItems.join(' and ');
        const notificationMessage = `Please submit the following requirements ${missingItemsText} for the Training Material entitled "${trainingMaterialTitle}".`;
        // Send notification API call
        const response = await fetch('/api/notifications/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recipientUserId: participant.userId, // Make sure userId is available in Participant
            message: notificationMessage,
            type: 'MISSING_REQUIREMENTS',
            relatedEntityType: 'TRAINING_MATERIAL',
            relatedEntityId: trainingMaterialId,
          })
        });
        if (!response.ok) throw new Error('Failed to send notification');
        await Swal.fire({
          title: 'Notification Sent!',
          text: `${participant.name} has been notified about the missing requirements.`,
          icon: 'success',
          confirmButtonColor: '#0077CC',
        });
      } catch (error) {
        await Swal.fire({
          title: 'Error',
          text: `Failed to send notification. Please try again or contact support.`,
          icon: 'error',
          confirmButtonColor: '#d33',
        });
      } finally {
        setLoading(false);
      }
    }
  };

  const handleNotifyAll = async () => {
    const participantsWithMissing = sortedParticipants.filter(
      participant => !participant.hasAssessment || !participant.hasCertification
    );

    if (participantsWithMissing.length === 0) {
      await Swal.fire({
        title: 'No Action Needed',
        text: 'All participants have submitted their required documents.',
        icon: 'info',
        confirmButtonColor: '#0077CC',
      });
      return;
    }

    const result = await Swal.fire({
      title: 'Send Notifications?',
      html: `
        <div class="text-left mb-4">
          Are you sure you want to notify all participants with missing requirements?
        </div>
        <div class="max-h-[300px] overflow-y-auto border border-gray-200 rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-color1 sticky top-0">
              <tr>
                <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">
                  Participant
                </th>
                <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase">
                  Missing Requirements
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              ${participantsWithMissing.map(p => `
                <tr class="hover:bg-gray-50">
                  <td class="px-4 py-2 text-sm text-gray-900">
                    ${p.name}
                  </td>
                  <td class="px-4 py-2 text-sm text-gray-500">
                    <ul class="list-inside">
                      ${!p.hasAssessment ? '<li>Assessment Form</li>' : ''}
                      ${!p.hasCertification ? '<li>Certification</li>' : ''}
                    </ul>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#0077CC',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, notify all',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      setLoading(true);
      let successCount = 0;
      let errorCount = 0;
      for (const participant of participantsWithMissing) {
        try {
          const missingItems = [];
          if (!participant.hasAssessment) missingItems.push('Assessment Form');
          if (!participant.hasCertification) missingItems.push('Certification');
          const missingItemsText = missingItems.join(' and ');
          const notificationMessage = `Please submit the following requirements ${missingItemsText} for the Training Material entitled "${trainingMaterialTitle}".`;
          const response = await fetch('/api/notifications/send', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              recipientUserId: participant.userId,
              message: notificationMessage,
              type: 'MISSING_REQUIREMENTS',
              relatedEntityType: 'TRAINING_MATERIAL',
              relatedEntityId: trainingMaterialId,
            })
          });
          if (!response.ok) throw new Error('Failed to send notification');
          successCount++;
        } catch (error) {
          errorCount++;
        }
      }
      setLoading(false);
      await Swal.fire({
        title: 'Batch Notification Result',
        text: `Notifications sent: ${successCount}\nFailed: ${errorCount}`,
        icon: errorCount === 0 ? 'success' : (successCount > 0 ? 'warning' : 'error'),
        confirmButtonColor: '#0077CC',
      });
    }
  };

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Contributors" size="xlarge">
      <div className="space-y-4 bg-white p-6 rounded-lg">
        {/* Header with Search, Filters, and Notify All */}
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex-1 w-full">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search participants..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-color3/20"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="relative">
              <Funnel size={15} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="appearance-none cursor-pointer pl-9 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white text-sm text-gray-600"
              >
                <option value="all">All Status</option>
                <option value="complete">Complete</option>
                <option value="incomplete">Incomplete</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={15} />
            </div>

            <div className="relative">
              <Calendar size={15} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'name' | 'status')}
                className="appearance-none cursor-pointer pl-9 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-[1px] focus:ring-color3/20 bg-white text-sm text-gray-600"
              >
                <option value="name">Sort by Name</option>
                <option value="status">Sort by Status</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={15} />
            </div>

            <button
              onClick={handleNotifyAll}
              className="flex items-center gap-2 px-4 py-2 bg-color3 text-white rounded-lg hover:bg-color3/90 transition-colors"
              disabled={loading}
            >
              {loading ? (
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              ) : (
                <AlertCircle className="w-4 h-4" />
              )}
              <span>Notify Missing</span>
            </button>
          </div>
        </div>

        {/* Table */}
        <div className="rounded-lg border border-gray-200 relative">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-color3"></div>
            </div>
          )}
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr className="bg-color1">
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                  Contributor
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                  Assessment Form
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                  Certification
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                  Notify
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentParticipants.length > 0 ? (
                currentParticipants.map((participant, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm">
                          <div className="font-medium text-gray-900">
                            {highlightText(participant.name, searchQuery)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap">
                      <div className="flex justify-center items-center gap-2">
                        {participant.hasAssessment ? (
                          <Check className="w-5 h-5 text-green-500" />
                        ) : (
                          <X className="w-5 h-5 text-red-500" />
                        )}
                        {participant.assessmentFormKey && (
                          <button 
                            onClick={() => handleViewDocument(participant.assessmentFormKey)}
                            title="View Assessment Form"
                            className="text-gray-400 hover:text-color3 focus:outline-none p-1 rounded-md hover:bg-gray-100"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap">
                      <div className="flex justify-center items-center gap-2">
                        {participant.hasCertification ? (
                          <Check className="w-5 h-5 text-green-500" />
                        ) : (
                          <X className="w-5 h-5 text-red-500" />
                        )}
                        {participant.certificateKey && (
                          <button 
                            onClick={() => handleViewDocument(participant.certificateKey)}
                            title="View Certification"
                            className="text-gray-400 hover:text-color3 focus:outline-none p-1 rounded-md hover:bg-gray-100"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap">
                      <div className="flex justify-center">
                        <button
                          onClick={() => handleNotify(participant)}
                          className="flex cursor-pointer items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-100"
                          disabled={loading}
                        >
                          {loading ? (
                            <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                          ) : (
                            <Bell className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center">
                      {participants.length === 0 ? (
                        <>
                          <Search className="w-10 h-10 text-gray-300 mb-2" />
                          <p className="text-gray-500 text-sm">No participants found in this material</p>
                        </>
                      ) : (
                        <>
                          <Search className="w-10 h-10 text-gray-300 mb-2" />
                          <p className="text-gray-500 text-sm">
                            {searchQuery.trim() 
                              ? `No participants found matching "${searchQuery}"`
                              : 'No participants found'}
                          </p>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              indexOfFirstItem={indexOfFirstItem}
              indexOfLastItem={indexOfLastItem}
              handlePageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ReviewModal;