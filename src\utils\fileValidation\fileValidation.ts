import path from 'path';
import { lookup } from 'mime-types';
import { ALLOWED_MIME_TYPES, MAX_FILE_SIZE, MAX_FILES_PER_UPLOAD } from './fileTypes';

export interface FileValidationError {
  code: string;
  message: string;
}

export const validateFileName = (fileName: string): FileValidationError | null => {
  // Check for malicious characters
  if (/[<>:"/\\|?*]/.test(fileName)) {
    return {
      code: 'INVALID_FILE_NAME',
      message: 'File name contains invalid characters'
    };
  }

  // Check for double extensions
  if (fileName.split('.').length > 2) {
    return {
      code: 'INVALID_FILE_NAME',
      message: 'File name contains multiple extensions'
    };
  }

  // Check for path traversal attempts
  if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
    return {
      code: 'INVALID_FILE_NAME',
      message: 'File name contains path traversal characters'
    };
  }

  return null;
};

// Define the types that are allowed in the API
export type DocumentType = 'module' | 'certificate' | 'assessmentForm';
// Define the types used internally for validation
type InternalDocumentType = 'module' | 'certificate' | 'assessment';

export const validateFileType = (file: File, type: InternalDocumentType): FileValidationError | null => {
  const fileExt = path.extname(file.name).toLowerCase();
  const mimeType = file.type.toLowerCase();

  // Get allowed types based on upload type
  const allowedTypes = type === 'module' 
    ? Object.values(ALLOWED_MIME_TYPES).flat()
    : type === 'certificate'
      ? ['.pdf', '.jpg', '.jpeg', '.png']
      : ['.pdf', '.docx'];

  // Check if file extension is allowed
  if (!allowedTypes.includes(fileExt)) {
    return {
      code: 'INVALID_FILE_TYPE',
      message: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  // Check if MIME type matches extension
  const expectedMimeType = lookup(fileExt);
  if (expectedMimeType && expectedMimeType !== mimeType) {
    return {
      code: 'INVALID_MIME_TYPE',
      message: `File MIME type does not match extension`
    };
  }

  return null;
};

export const validateFileSize = (file: File, type: InternalDocumentType): FileValidationError | null => {
  if (file.size > MAX_FILE_SIZE[type]) {
    return {
      code: 'FILE_TOO_LARGE',
      message: `File size exceeds the maximum limit of ${MAX_FILE_SIZE[type] / (1024 * 1024)}MB`
    };
  }
  return null;
};

export const validateFile = (file: File, type: DocumentType): FileValidationError | null => {
  // Validate file name
  const nameError = validateFileName(file.name);
  if (nameError) return nameError;

  // Map assessmentForm to assessment for internal processing
  const internalType: InternalDocumentType = type === 'assessmentForm' ? 'assessment' : type;

  // Validate file type
  const typeError = validateFileType(file, internalType);
  if (typeError) return typeError;

  // Validate file size
  const sizeError = validateFileSize(file, internalType);
  if (sizeError) return sizeError;

  return null;
};

export const validateFiles = (files: File[]): FileValidationError | null => {
  // Check number of files
  if (files.length > MAX_FILES_PER_UPLOAD) {
    return {
      code: 'TOO_MANY_FILES',
      message: `Maximum ${MAX_FILES_PER_UPLOAD} files allowed per upload`
    };
  }

  // Validate each file as a module (since FileUploader is used for module uploads)
  for (const file of files) {
    const error = validateFile(file, 'module');
    if (error) return error;
  }

  return null;
}; 