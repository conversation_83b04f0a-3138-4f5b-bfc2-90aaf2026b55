/*
  Warnings:

  - The primary key for the `material_competencies` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `contentType` on the `moduleFiles` table. All the data in the column will be lost.
  - You are about to drop the column `url` on the `moduleFiles` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[training_material_id,competency_id,temporary_competency_id]` on the table `material_competencies` will be added. If there are existing duplicate values, this will fail.
  - Made the column `fileName` on table `moduleFiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `fileKey` on table `moduleFiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `fileType` on table `moduleFiles` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "material_competencies" DROP CONSTRAINT "material_competencies_pkey",
ADD COLUMN     "id" SERIAL NOT NULL,
ADD COLUMN     "temporary_competency_id" INTEGER,
ALTER COLUMN "competency_id" DROP NOT NULL,
ADD CONSTRAINT "material_competencies_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "moduleFiles" DROP COLUMN "contentType",
DROP COLUMN "url",
ALTER COLUMN "fileName" SET NOT NULL,
ALTER COLUMN "fileKey" SET NOT NULL,
ALTER COLUMN "fileType" SET NOT NULL;

-- DropEnum
DROP TYPE "ModuleContentType";

-- CreateTable
CREATE TABLE "temporary_competencies" (
    "id" SERIAL NOT NULL,
    "competencyName" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "createdByUserId" INTEGER NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "temporary_competencies_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "material_competencies_training_material_id_competency_id_te_key" ON "material_competencies"("training_material_id", "competency_id", "temporary_competency_id");

-- AddForeignKey
ALTER TABLE "temporary_competencies" ADD CONSTRAINT "temporary_competencies_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "material_competencies" ADD CONSTRAINT "material_competencies_temporary_competency_id_fkey" FOREIGN KEY ("temporary_competency_id") REFERENCES "temporary_competencies"("id") ON DELETE CASCADE ON UPDATE CASCADE;
