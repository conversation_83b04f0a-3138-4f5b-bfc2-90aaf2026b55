'use client';

import { useState } from 'react';
import { Link as LinkIcon, File as FileIcon } from 'lucide-react';
import { <PERSON>Eye, FiChevronDown } from 'react-icons/fi';
import { ReviewData, ResourceType } from '../types';

interface ReviewResourcesProps {
  data: ReviewData;
  nextStep: () => void;
  prevStep: () => void;
}

const ReviewResources = ({ data, nextStep, prevStep }: ReviewResourcesProps) => {
  const [openModules, setOpenModules] = useState<{ [key: number]: boolean }>(
    (data.modules || []).reduce((acc, module) => ({ ...acc, [module.id]: true }), {})
  );

  const toggleModule = (moduleId: number) => {
    setOpenModules(prev => ({
      ...prev,
      [moduleId]: !prev[moduleId]
    }));
  };

  const handleDownload = async (resource: ResourceType) => {
    try {
      if (resource.resourceType === 'link' && resource.resourceUrl) {
   
        window.open(resource.resourceUrl, '_blank');
        return;
      } else if (resource.resourceType === 'file' && resource.fileKey) {
        const viewUrl = `/api/files/view?key=${encodeURIComponent(resource.fileKey)}`;

        window.open(viewUrl, '_blank');
        return;
      } else {
        console.warn('Cannot handle resource:', resource);
        alert('Cannot open or download this resource type.');
        return;
      }

    } catch (error) {
      console.error('Error handling resource:', error);
      alert('An error occurred while trying to access the resource.');
    }
  };

  const modulesExist = data.modules && Array.isArray(data.modules);

  return (
    <div className="space-y-8">
      {/* Modules Section */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Modules & Resources</h2>
        <div className="space-y-2">
          {modulesExist && data.modules.map((module) => (
            <div key={module.id} className="border-[0.1px] border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleModule(module.id)}
                className="flex items-center w-full p-4 cursor-pointer  bg-gray-50 text-left"
              >
                <FiChevronDown
                  className={`w-5 h-5 mr-2 transform transition-transform duration-200 ${
                    openModules[module.id] ? "rotate-180" : ""
                  }`}
                />
                <span className="font-medium">Module {module.moduleOrder}: {module.title}</span>
              </button>

              {openModules[module.id] && (
                <div className="p-4 space-y-3">
                  {module.resources && Array.isArray(module.resources) && module.resources.map((resource, index) => (
                    <div key={resource.id || index} className="flex items-start gap-4 p-3 bg-gray-50 rounded-lg">
                      <div className="flex-shrink-0 pt-0.5">
                        {resource.resourceType === 'link' ? 
                          <LinkIcon className="w-5 h-5 text-gray-400" aria-hidden="true" /> : 
                          <FileIcon className="w-5 h-5 text-gray-400" aria-hidden="true" />
                        }
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {resource.description || resource.fileName || resource.resourceUrl || 'Resource'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {resource.resourceType === 'link' && resource.resourceUrl ? 
                           <a href={resource.resourceUrl} target="_blank" rel="noopener noreferrer" className="hover:underline break-all">{resource.resourceUrl}</a> : 
                           resource.fileName ? resource.fileName : resource.fileType ? `(${resource.fileType})` : 'File'
                          }
                        </p>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(resource);
                        }}
                        className="flex items-center text-sm cursor-pointer text-color3 hover:text-color3/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3 rounded-lg self-center"
                        aria-label={`Access ${resource.description || 'resource'}`}
                      >
                        {resource.resourceType === 'link' ? 
                          <LinkIcon className="w-4 h-4 mr-1" aria-hidden="true" /> : 
                          <FiEye className="w-4 h-4 mr-1" aria-hidden="true" />
                        }
                        {resource.resourceType === 'link' ? 'Open Link' : 'View'}
                      </button>
                    </div>
                  ))}
                  {(!module.resources || module.resources.length === 0) && (
                      <p className="text-sm text-gray-500 italic px-3 py-2">No resources in this module.</p>
                  )}
                </div>
              )}
            </div>
          ))}
          {!modulesExist && (
              <p className="text-gray-500 italic">No modules found for this material.</p>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <button
          onClick={prevStep}
          className="px-6 py-2 cursor-pointer border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3"
        >
          Previous
        </button>
        <button
          onClick={nextStep}
          className="px-6 py-2 cursor-pointer bg-color3 text-white rounded-lg hover:bg-color3/90 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-color3"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default ReviewResources; 