import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { MaterialStatus } from '@prisma/client';
import { z } from 'zod';

const resubmitRequestSchema = z.object({
  reason: z.string().min(1, 'Resubmission reason is required').trim().optional()
});

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const materialId = parseInt(params.id);
    if (isNaN(materialId)) {
      return NextResponse.json({ error: 'Invalid material ID format.' }, { status: 400 });
    }

    let rawBody;
    try {
      rawBody = await request.json();
    } catch (error) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const validationResult = resubmitRequestSchema.safeParse(rawBody);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { reason = 'Material resubmitted for approval' } = validationResult.data;

    const result = await prisma.$transaction(async (tx) => {
      const material = await tx.trainingMaterial.findUnique({
        where: { id: materialId },
        include: {
          trainingParticipants: {
            where: { isCreator: true }
          }
        }
      });

      if (!material) {
        throw new Error('Material not found');
      }

      if (material.status !== MaterialStatus.ARCHIVED) {
        throw new Error('Only archived materials can be resubmitted');
      }

      const isCreator = material.trainingParticipants.some(
        p => p.isCreator && p.userId === parseInt(session.user.id)
      );

      if (!isCreator) {
        throw new Error('Only the creator can resubmit a material');
      }

      const updatedMaterial = await tx.trainingMaterial.update({
        where: { id: materialId },
        data: { 
          status: MaterialStatus.PENDING_APPROVAL,
        }
      });

      await tx.materialStatusLog.create({
        data: {
          trainingMaterialId: materialId,
          changedByUserId: parseInt(session.user.id),
          oldStatus: MaterialStatus.ARCHIVED,
          newStatus: MaterialStatus.PENDING_APPROVAL,
          reason
        }
      });

      await tx.systemNotification.createMany({
        data: await tx.user.findMany({
          where: {
            role: {
              roleName: 'HR_ADMIN'
            }
          },
          select: {
            id: true
          }
        }).then(admins => admins.map(admin => ({
          recipientUserId: admin.id,
          message: `A previously rejected training material "${material.title}" has been resubmitted for approval`,
          type: 'MATERIAL_RESUBMITTED',
          relatedEntityType: 'TRAINING_MATERIAL',
          relatedEntityId: materialId
        })))
      });

      return updatedMaterial;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error resubmitting training material:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to resubmit training material' },
      { status: 500 }
    );
  }
}