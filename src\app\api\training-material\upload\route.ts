import { writeFile, mkdir } from 'fs/promises';
import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import crypto from 'crypto';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { 
  uploadSchema, 
  validateUploadRequest,
  validateFile,
  FileValidationError 
} from '@/utils/fileValidation';

// Define constants from upload-module-file for module uploads
const ALLOWED_MODULE_FILE_TYPES = ['.pdf', '.docx', '.pptx', '.jpg', '.jpeg', '.png', '.mp4', '.mov']; // Removed .avi, .mp3, added .jpeg
const MAX_FILE_SIZE_MODULE = 300 * 1024 * 1024; // 300MB for modules
const MAX_FILE_SIZE_OTHER = 10 * 1024 * 1024; // 10MB for others

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      console.error('[API /upload] Unauthorized: No session or user ID');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const userId = parseInt(session.user.id, 10);
    if (isNaN(userId)) {
      console.error('[API /upload] Invalid user ID format in session:', session.user.id);
      return NextResponse.json({ error: 'Invalid user ID format for session.' }, { status: 400 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const type = formData.get('type') as string | null;
    const materialIdString = formData.get('materialId') as string | null;
    const moduleIdString = formData.get('moduleId') as string | null;

    if (!file || !type || !materialIdString) {
      console.error('[API /upload] Missing required fields:', { file: !!file, type, materialIdString });
      return NextResponse.json(
        { error: 'Missing required fields: file, type, or materialId' },
        { status: 400 }
      );
    }

    // Validate upload request data
    const validationResult = validateUploadRequest({ type, materialId: materialIdString, moduleId: moduleIdString });
    if (!validationResult.success || !validationResult.data) {
      console.error('[API /upload] Validation failed:', validationResult.error);
      return NextResponse.json({
        error: 'Invalid request payload',
        details: validationResult.error
      }, { status: 400 });
    }
    
    const { type: validatedType, materialId: validatedMaterialId, moduleId: validatedModuleId } = validationResult.data;

    // Validate file
    const fileValidationError = validateFile(file, validatedType);
    if (fileValidationError) {
      console.error(`[API /upload] File validation failed:`, fileValidationError);
      return NextResponse.json({
        error: fileValidationError.message,
        code: fileValidationError.code
      }, { status: 400 });
    }

    const MAX_SIZE = validatedType === 'module' ? MAX_FILE_SIZE_MODULE : MAX_FILE_SIZE_OTHER;
    if (file.size > MAX_SIZE) {
      console.error(`[API /upload] File size (${file.size}) exceeds limit (${MAX_SIZE}) for type '${validatedType}'`);
      return NextResponse.json(
        { error: `File size exceeds ${MAX_SIZE / 1024 / 1024}MB limit for type '${validatedType}'` },
        { status: 400 }
      );
    }

    const allowedTypes = validatedType === 'certificate'
      ? ['.pdf', '.jpg', '.jpeg', '.png']
      : validatedType === 'assessment'
        ? ['.pdf', '.docx']
        : ALLOWED_MODULE_FILE_TYPES;

    const fileExt = path.extname(file.name).toLowerCase();
    if (!allowedTypes.includes(fileExt)) {
      console.error(`[API /upload] Invalid file type '${fileExt}' for '${validatedType}'. Allowed: ${allowedTypes.join(', ')}`);
      return NextResponse.json(
        { error: `Invalid file type for '${validatedType}'. Allowed: ${allowedTypes.join(', ')}` },
        { status: 400 }
      );
    }

    const timestamp = Date.now();
    const randomName = crypto.randomBytes(16).toString('hex');
    const fileName = `${timestamp}_${randomName}${fileExt}`;

    const baseDir = path.join(process.cwd(), 'storage', 'training-materials');
    const materialDir = path.join(baseDir, validatedMaterialId.toString());

    let typeDir;
    let fileKeyParts: string[];

    if (validatedType === 'module') {
      typeDir = path.join(materialDir, 'modules', validatedModuleId!.toString());
      fileKeyParts = [
          'training-materials',
          validatedMaterialId.toString(),
          'modules',
          validatedModuleId!.toString(),
          fileName
      ];
    } else {
      const subDir = validatedType === 'certificate' ? 'certificates' : 'assessments';
      typeDir = path.join(materialDir, subDir);
      fileKeyParts = [
          'training-materials',
          validatedMaterialId.toString(),
          subDir,
          fileName
      ];
    }
    const fileKey = fileKeyParts.join('/');

    await mkdir(typeDir, { recursive: true });

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const filePath = path.join(typeDir, fileName);

    let resourceId: number;

    try {
        await writeFile(filePath, buffer);

        const resource = await prisma.trainingResource.create({
            data: {
                fileKey,
                fileName: file.name,
                fileType: fileExt.substring(1),
                resourceType: 'file',
            },
            select: { id: true }
        });
        resourceId = resource.id;

        if (validatedType === 'module' && validatedModuleId !== undefined) {
            await prisma.moduleResources.create({
                data: {
                    A: validatedModuleId,
                    B: resourceId
                }
            });
        }

    } catch (dbError: any) {
        console.error('[API /upload] Database error during resource creation/linking or file write:', dbError);
        if (dbError instanceof Prisma.PrismaClientKnownRequestError) {
            if (dbError.code === 'P2003' && (dbError.meta?.field_name as string)?.includes('moduleId')) {
                console.error(`[API /upload] Prisma Error P2003: Module with ID ${validatedModuleId} not found.`);
                return NextResponse.json({ error: `Failed to link resource: Module with ID ${validatedModuleId} not found.` }, { status: 400 });
            } else if (dbError.code === 'P2025') {
                console.error(`[API /upload] Prisma Error P2025: Record to update not found (e.g. Module ID ${validatedModuleId}).`);
                return NextResponse.json({ error: `Failed to link resource: The specified module (ID: ${validatedModuleId}) does not exist.` }, { status: 400 });
            }
            console.error(`[API /upload] Prisma Error (Code: ${dbError.code}): ${dbError.message}`);
            return NextResponse.json({ error: `Database error (Code: ${dbError.code}). Failed to save resource link.` }, { status: 500 });
        }
        throw dbError;
    }

    return NextResponse.json({
      success: true,
      fileKey,
      fileName: file.name,
      fileSize: file.size,
      fileType: fileExt.substring(1),
      resourceId: resourceId
    });

  } catch (error: any) {
    console.error('[API /upload] Unhandled error in POST handler:', error);
    if (error instanceof Error) {
      return NextResponse.json(
        { error: 'Failed to upload file due to an unexpected server error.', details: error.message },
        { status: 500 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to upload file due to an unexpected server error.' },
      { status: 500 }
    );
  }
}