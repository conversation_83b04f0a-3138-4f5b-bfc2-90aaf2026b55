import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import {
  cacheMaterialProgress,
  cacheStepProgress,
  cacheModuleProgress,
  cacheCourseProgress
} from '@/lib/services/progressCache';
import {
  materialProgressSchema,
  stepProgressSchema,
  moduleProgressSchema,
  courseProgressSchema
} from '@/lib/schemas/progress';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';

// Define the request body schema
const requestSchema = z.object({
  type: z.enum(['material', 'step', 'module', 'course']),
  data: z.record(z.any()) // Will be validated based on 'type'
});

export async function POST(req: NextRequest) {
  try {
    console.log('API: Progress cache request received');

    // Get the session to ensure user is authenticated
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      console.log('API: Authentication failed for progress cache request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await req.json();
    console.log(`API: Processing progress cache request for user: ${session.user.id}`);
    const parsed = requestSchema.safeParse(body);

    if (!parsed.success) {
      console.log('API: Invalid request format for progress cache');
      return NextResponse.json(
        { error: 'Invalid request format', details: parsed.error.format() },
        { status: 400 }
      );
    }

    const { type, data } = parsed.data;

    // Make sure userId is set and matches session user
    if (!data.userId) {
      data.userId = session.user.id;
    } else if (data.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Cannot update another user's progress" },
        { status: 403 }
      );
    }

    let result = false;

    // Validate and cache based on type
    console.log(`API: Caching progress of type: ${type}`);
    switch (type) {
      case 'material':
        console.log(`API: Processing material progress - ID: ${data.materialId}, Name: ${data.materialName || 'Unknown'}`);
        const materialData = materialProgressSchema.parse(data);
        result = await cacheMaterialProgress(materialData);
        break;

      case 'step':
        console.log(`API: Processing step progress - Step ID: ${data.stepId}`);
        const stepData = stepProgressSchema.parse(data);
        result = await cacheStepProgress(stepData);
        break;

      case 'module':
        console.log(`API: Processing module progress - Module ID: ${data.moduleId}`);
        const moduleData = moduleProgressSchema.parse(data);
        result = await cacheModuleProgress(moduleData);
        break;

      case 'course':
        console.log(`API: Processing course progress - Course ID: ${data.courseId}`);
        const courseData = courseProgressSchema.parse(data);
        result = await cacheCourseProgress(courseData);
        break;

      default:
        console.log(`API: Invalid progress type: ${type}`);
        return NextResponse.json(
          { error: 'Invalid progress type' },
          { status: 400 }
        );
    }

    if (result) {
      console.log(`API: Successfully cached ${type} progress`);
      return NextResponse.json({ success: true });
    } else {
      console.log(`API: Failed to cache ${type} progress`);
      return NextResponse.json(
        { error: 'Failed to cache progress' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Progress cache error:', error);

    if (error instanceof z.ZodError) {
      console.log('API: Invalid progress data format:', error.format());
      return NextResponse.json(
        { error: 'Invalid progress data', details: error.format() },
        { status: 400 }
      );
    }

    console.log('API: Internal server error during progress caching');
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Also handle GET requests to check if progress exists
export async function GET(req: NextRequest) {
  // Implementation for retrieving progress
  // This would be implemented later if needed
  return NextResponse.json(
    { error: 'Not implemented' },
    { status: 501 }
  );
}