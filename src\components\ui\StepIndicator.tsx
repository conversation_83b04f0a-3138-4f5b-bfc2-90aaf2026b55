import React from "react";
import { CheckIcon } from "lucide-react";

interface StepIndicatorProps {
    steps: string[];
    currentStep: number;
}

export default function StepIndicator({ steps, currentStep }: StepIndicatorProps) {
    return (
        <div className="bg-white">
            <nav className="max-w-screen-xl mx-auto px-6 py-4">
                <div className="flex justify-between items-center">
                    {steps.map((step, index) => {
                        const stepNumber = index + 1;
                        const isActive = stepNumber === currentStep;
                        const isCompleted = stepNumber < currentStep;

                        return (
                            <div key={step} className="flex items-center">
                                <div className="flex items-center">
                                    <div 
                                        className={`flex items-center justify-center w-6 h-6 rounded-full text-sm font-medium ${
                                            isActive
                                            ? "bg-white border-2 border-color5 text-color5"
                                            : isCompleted
                                                ? "bg-green-500 text-white"
                                                : "bg-gray-200 text-gray-400"
                                        }`}
                                    >
                                        {isCompleted ? <CheckIcon className="w-4 h-4" /> : stepNumber}
                                    </div>
                                    <div className={`ml-2 text-sm font-medium ${
                                        isActive 
                                        ? "text-color5" 
                                        : isCompleted 
                                            ? "text-color5/60" 
                                            : "text-gray-400"
                                    }`}>
                                        {step}
                                    </div>
                                </div>
                                {index < steps.length - 1 && (
                                    <div className={`w-24 h-px mx-4 ${
                                        isCompleted 
                                        ? "bg-green-500" 
                                        : "bg-gray-200"
                                    }`} />
                                )}
                            </div>
                        )
                    })}
                </div>
            </nav>
        </div>
    )
}
