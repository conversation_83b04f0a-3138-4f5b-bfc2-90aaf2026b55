"use client"

import { X, AlertTriangle } from "lucide-react"
import { FileValidationError } from "@/utils/fileValidation"

interface FileErrorDialogProps {
  error: FileValidationError | null;
  onClose: () => void;
}

const FileErrorDialog: React.FC<FileErrorDialogProps> = ({ error, onClose }) => {
  if (!error) return null;

  const getErrorTitle = (code: string): string => {
    switch (code) {
      case 'INVALID_FILE_TYPE':
        return 'Unsupported File Type';
      case 'INVALID_FILE_NAME':
        return 'Invalid File Name';
      case 'FILE_TOO_LARGE':
        return 'File Too Large';
      case 'TOO_MANY_FILES':
        return 'Too Many Files';
      case 'INVALID_MIME_TYPE':
        return 'Invalid File Format';
      default:
        return 'Upload Error';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center">
      <div 
        className="bg-white rounded-lg shadow-lg max-w-md w-full mx-4 overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="bg-red-50 p-4 flex justify-between items-center border-b border-red-100">
          <div className="flex items-center">
            <AlertTriangle className="h-6 w-6 text-red-500 mr-3" />
            <h3 className="text-lg font-medium text-red-800">
              {getErrorTitle(error.code)}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6">
          <p className="text-gray-700 mb-4">{error.message}</p>
          
          {error.code === 'INVALID_FILE_TYPE' && (
            <div className="mt-2 text-sm">
              <p className="font-medium text-gray-700 mb-1">Please upload only supported file types:</p>
              <ul className="list-disc pl-5 text-gray-600 space-y-1">
                <li>Documents: PDF, DOC, DOCX, PPT, PPTX, TXT</li>
                <li>Images: JPG, JPEG, PNG, GIF, SVG</li>
                <li>Videos: MP4, WEBM, MOV</li>
                <li>Audio: MP3, WAV, OGG</li>
              </ul>
            </div>
          )}
          
          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-red-500"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileErrorDialog; 