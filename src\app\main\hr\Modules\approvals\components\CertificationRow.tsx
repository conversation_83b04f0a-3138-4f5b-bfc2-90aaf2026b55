'use client';

import type { PendingApproval } from '../utils/types';

import React, { useState } from 'react';
import { BookOpen } from 'lucide-react';
import { PublicationRowProps } from '../utils/types';
import { highlightSearchMatches } from '../utils/helpers';
import StatusBadge from './StatusBadge';
import ReviewModal from './ReviewModal';
import { createPortal } from 'react-dom';

const CertificationRow: React.FC<PublicationRowProps> = ({ item, searchQuery }) => {
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  
  // Get tags with null checks to prevent errors
  const officialTags = item.officialTags || [];
  const temporaryTags = item.temporaryTags || [];
  const allTags = [...officialTags, ...temporaryTags];
  
  const handleReviewClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent any default navigation
    e.stopPropagation(); // Stop event propagation
    setIsReviewModalOpen(true);
  };

  return (
    <>
      <tr className="hover:bg-gray-50">
        {/* Training Material Column (title + tags) */}
        <td className="px-6 py-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
              <BookOpen className="w-4 h-4 text-color3" />
            </div>
            <div className="min-w-0">
              <div className="text-sm truncate font-medium text-gray-900 max-w-[230px]">
                {highlightSearchMatches(item.title, searchQuery)}
              </div>
              {/* Tags (officialTags + temporaryTags) */}
              {allTags.length > 0 && (
                <div className="flex flex-wrap gap-1.5 mt-1.5">
                  {allTags.slice(0, 2).map((tag, index) => (
                    <span
                      key={index}
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        officialTags.includes(tag)
                          ? 'bg-blue-50 text-blue-600'
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {tag}
                    </span>
                  ))}
                  {/* Show +N more if more than 2 tags */}
                  {allTags.length > 2 && (
                    <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">
                      +{allTags.length - 2} more
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </td>
        {/* Creator / Department Column */}
        <td className="px-6 py-4 whitespace-nowrap">
          {item.author ? (
            <>
              <div className="text-sm text-gray-900">{item.author.name}</div>
              <div className="text-sm text-gray-500">{item.author.department}</div>
            </>
          ) : (
            <div className="text-sm text-gray-500">No author specified</div>
          )}
        </td>
        {/* Category Column */}
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="inline-flex items-center px-2.5 py-1 rounded-md bg-gray-100 text-xs font-medium text-gray-800">
            {highlightSearchMatches(item.category, searchQuery)}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <StatusBadge status={calculateStatus(item)} />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm text-gray-900">
            {new Date(item.submittedDate).toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' })}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center gap-2 justify-end">
            <button
              type="button"
              className="flex items-center gap-1 px-3 py-1 text-sm text-color3 hover:bg-color3/10 rounded-md transition-colors"
              onClick={handleReviewClick}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
              <span>Contributors</span>
            </button>
          </div>
        </td>
      </tr>
      {isReviewModalOpen && createPortal(
        <ReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => setIsReviewModalOpen(false)}
          trainingMaterialId={item.id}
        />, document.body)}
    </>
  );
};

// Helper to calculate status based on participants
function calculateStatus(item: PendingApproval) {
  if (item.participants && item.participants.length > 0) {
    const allComplete = item.participants.every(
      p => p.hasAssessment && p.hasCertification
    );
    return allComplete ? 'PUBLISHED' : 'PENDING_APPROVAL';
  }
  return item.status;
}

export default CertificationRow;