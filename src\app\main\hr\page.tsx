'use client'

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import HRSidebar from '@/components/layout/HRsidebar';
import { SidebarProvider } from '@/components/layout/HRsidebar';
import HRnav from '@/components/layout/HRnav';
import HRDashboard from './Modules/HRDashboard';
import ParticipationModule from './Modules/participation';
import ApprovalsModule from './Modules/approvals/page';
import AnalyticsModule from './Modules/analytics';

const HRPage = () => {
  const searchParams = useSearchParams();
  const [activeModule, setActiveModule] = useState<'dashboard' | 'participation' | 'approvals' | 'analytics'>('dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    const module = searchParams?.get('module') || null;
    if (module === 'participation') {
      setActiveModule('participation');
    } 
    else if (module === 'approvals') {
      setActiveModule('approvals');
    }
    else if (module === 'analytics') {
      setActiveModule('analytics');
    }
    else {
      setActiveModule('dashboard');
    }
  }, [searchParams]);

  return (
    <SidebarProvider>
      <div className="flex min-h-screen bg-gray-50">
        <HRSidebar onCollapse={(collapsed) => setIsSidebarCollapsed(collapsed)} />
        <div className="flex-1">
          <HRnav />
          <div 
            className={`relative  p-4 transition-all duration-300 mt-16 ${
              isSidebarCollapsed ? '' : ''
            }`}
          >
            {activeModule === 'dashboard' && <HRDashboard />}
            {activeModule === 'participation' && <ParticipationModule />}
            {activeModule === 'approvals' && <ApprovalsModule />}
            {activeModule === 'analytics' && <AnalyticsModule />}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default HRPage; 