import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma'; 
import crypto from 'crypto';
import { sendEmail } from '@/lib/email'; 

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email || typeof email !== 'string' || !email.includes('@')) {
      return NextResponse.json({ message: 'Invalid email address provided.' }, { status: 400 });
    }

    
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }, 
    });

    if (user) {
      const resetToken = crypto.randomBytes(32).toString('hex');

      const passwordResetToken = crypto
        .createHash('sha256')
        .update(resetToken)
        .digest('hex');

      const passwordResetExpires = new Date(Date.now() + 3600000); 

      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken,
          passwordResetExpires,
        },
      });

      const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password?token=${resetToken}`;
      
      const subject = 'Password Reset Request';
      const text = `You requested a password reset. Click the following link to reset your password: ${resetUrl}\n\nIf you did not request this, please ignore this email.\nThis link will expire in 1 hour.`;
      const html = `
        <p>You requested a password reset.</p>
        <p>Click the following link to reset your password:</p>
        <a href="${resetUrl}">${resetUrl}</a>
        <p>If you did not request this, please ignore this email.</p>
        <p>This link will expire in 1 hour.</p>
      `;

      try {
        await sendEmail({
          to: user.email,
          subject,
          text,
          html,
        });
      } catch (emailError) {
        console.error(`Failed to send password reset email to ${user.email}:`, emailError);

      }
    }
    
    return NextResponse.json({ 
      message: "If an account exists for this email, a password reset link has been sent."
    }, { status: 200 });

  } catch (error) {
    console.error("Forgot Password Error:", error);
    return NextResponse.json({ message: 'An internal server error occurred.' }, { status: 500 });
  }
} 